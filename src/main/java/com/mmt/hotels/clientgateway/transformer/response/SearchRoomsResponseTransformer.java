package com.mmt.hotels.clientgateway.transformer.response;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.google.common.reflect.TypeToken;
import com.google.gson.Gson;
import com.mmt.hotels.clientgateway.businessobjects.AdditionalChargesBO;
import com.mmt.hotels.clientgateway.businessobjects.CommonModifierResponse;
import com.mmt.hotels.clientgateway.constants.Constants;
import com.mmt.hotels.clientgateway.constants.ConstantsTranslation;
import com.mmt.hotels.clientgateway.consul.CommonConfigConsul;
import com.mmt.hotels.clientgateway.consul.FlexiCancelStaticDetail;
import com.mmt.hotels.clientgateway.consul.LinkedRatePlanStyle;
import com.mmt.hotels.clientgateway.consul.properties.ExtraAdultChildInclusionConfig;
import com.mmt.hotels.clientgateway.enums.DependencyLayer;
import com.mmt.hotels.clientgateway.enums.ExperimentKeys;
import com.mmt.hotels.clientgateway.pms.CommonConfig;
import com.mmt.hotels.clientgateway.pms.MobConfigHelper;
import com.mmt.hotels.clientgateway.request.Filter;
import com.mmt.hotels.clientgateway.request.RequestDetails;
import com.mmt.hotels.clientgateway.request.RoomStayCandidate;
import com.mmt.hotels.clientgateway.request.SearchCriteria;
import com.mmt.hotels.clientgateway.request.SearchRoomsCriteria;
import com.mmt.hotels.clientgateway.request.SearchRoomsRequest;
import com.mmt.hotels.clientgateway.request.dayuse.DayUseRoomsRequest;
import com.mmt.hotels.clientgateway.response.*;
import com.mmt.hotels.clientgateway.response.Facility;
import com.mmt.hotels.clientgateway.response.FacilityGroup;
import com.mmt.hotels.clientgateway.response.availrooms.Alert;
import com.mmt.hotels.clientgateway.response.availrooms.FeatureFlags;
import com.mmt.hotels.clientgateway.response.corporate.GuestHouseResponse;
import com.mmt.hotels.clientgateway.response.dayuse.rooms.DayUsePersuasion;
import com.mmt.hotels.clientgateway.response.dayuse.rooms.DayUsePriceDetail;
import com.mmt.hotels.clientgateway.response.dayuse.rooms.DayUseRoom;
import com.mmt.hotels.clientgateway.response.dayuse.rooms.DayUseRoomCriteria;
import com.mmt.hotels.clientgateway.response.dayuse.rooms.DayUseRoomStayCandidate;
import com.mmt.hotels.clientgateway.response.dayuse.rooms.DayUseRoomsResponse;
import com.mmt.hotels.clientgateway.response.dayuse.rooms.DayUseSlotPlan;
import com.mmt.hotels.clientgateway.response.emi.CouponCardConfig;
import com.mmt.hotels.clientgateway.response.emi.EmiConfigDetails;
import com.mmt.hotels.clientgateway.response.emi.EmiTagDetails;
import com.mmt.hotels.clientgateway.response.moblanding.SupportDetails;
import com.mmt.hotels.clientgateway.response.rooms.*;
import com.mmt.hotels.clientgateway.response.rooms.AlternateDate;
import com.mmt.hotels.clientgateway.response.rooms.MediaData;
import com.mmt.hotels.clientgateway.response.rooms.SharedInfo;
import com.mmt.hotels.clientgateway.response.rooms.Space;
import com.mmt.hotels.clientgateway.response.rooms.SpaceData;
import com.mmt.hotels.clientgateway.response.rooms.StayDetail;
import com.mmt.hotels.clientgateway.response.searchHotels.BgGradient;
import com.mmt.hotels.clientgateway.response.searchHotels.BorderGradient;
import com.mmt.hotels.clientgateway.response.staticdetail.AllInclusiveCard;
import com.mmt.hotels.clientgateway.response.staticdetail.Image360.View360Image;
import com.mmt.hotels.clientgateway.response.staticdetail.PrimaryOffer;
import com.mmt.hotels.clientgateway.response.thankyou.RtbCard;
import com.mmt.hotels.clientgateway.response.thankyou.RtbPersuasionCard;
import com.mmt.hotels.clientgateway.service.CorporateService;
import com.mmt.hotels.clientgateway.service.PolyglotService;
import com.mmt.hotels.clientgateway.thirdparty.response.PersuasionData;
import com.mmt.hotels.clientgateway.thirdparty.response.PersuasionObject;
import com.mmt.hotels.clientgateway.thirdparty.response.PersuasionStyle;
import com.mmt.hotels.clientgateway.transformer.factory.SearchHotelsFactory;
import com.mmt.hotels.clientgateway.util.Currency;
import com.mmt.hotels.clientgateway.util.DateUtil;
import com.mmt.hotels.clientgateway.util.DayUseUtil;
import com.mmt.hotels.clientgateway.util.MDCHelper;
import com.mmt.hotels.clientgateway.util.MetricAspect;
import com.mmt.hotels.clientgateway.util.ObjectMapperUtil;
import com.mmt.hotels.clientgateway.util.PersuasionUtil;
import com.mmt.hotels.clientgateway.util.Utility;
import com.mmt.hotels.model.enums.BNPLVariant;
import com.mmt.hotels.model.enums.LuckyUserContext;
import com.mmt.hotels.model.response.corporate.GuestHouseListingResponse;
import com.mmt.hotels.model.response.dayuse.MissingSlotDetail;
import com.mmt.hotels.model.response.dayuse.Slot;
import com.mmt.hotels.model.response.emi.NoCostEmiDetails;
import com.mmt.hotels.model.response.mypartner.MarkUpDetails;
import com.mmt.hotels.model.response.persuasion.HotelCloudData;
import com.mmt.hotels.model.response.pricing.BestCoupon;
import com.mmt.hotels.model.response.pricing.CancelPenalty.CancellationType;
import com.mmt.hotels.model.response.pricing.CancellationTimeline;
import com.mmt.hotels.model.response.pricing.ExtraGuestDetail;
import com.mmt.hotels.model.response.pricing.FlexiCancelAddOnDetails;
import com.mmt.hotels.model.response.pricing.FlexiCancelDetails;
import com.mmt.hotels.model.response.pricing.HotelRates;
import com.mmt.hotels.model.response.pricing.Inclusion;
import com.mmt.hotels.model.response.pricing.MealPlan;
import com.mmt.hotels.model.response.pricing.OccupancyDetails;
import com.mmt.hotels.model.response.pricing.PackageRoomRatePlan;
import com.mmt.hotels.model.response.pricing.PackageRoomType;
import com.mmt.hotels.model.response.pricing.PaymentMode;
import com.mmt.hotels.model.response.pricing.RangePrice;
import com.mmt.hotels.model.response.pricing.RatePlan;
import com.mmt.hotels.model.response.pricing.RoomDetailsResponse;
import com.mmt.hotels.model.response.pricing.RoomType;
import com.mmt.hotels.model.response.pricing.RoomTypeDetails;
import com.mmt.hotels.model.response.pricing.SupplierDetails;
import com.mmt.hotels.model.response.pricing.WalletSurge;
import com.mmt.hotels.model.response.pricing.HeroTierDetails;
import com.mmt.hotels.model.response.searchwrapper.Segments;
import com.mmt.hotels.model.response.staticdata.*;
import com.mmt.hotels.model.response.staticdata.Image360.Image360;
import com.mmt.hotels.model.response.staticdata.SleepingInfoArrangement;
import com.mmt.hotels.pojo.response.CampaignPojo;
import com.mmt.hotels.util.Tuple;
import com.mmt.model.*;
import com.mmt.model.RoomInfo;
import com.mmt.propertymanager.config.PropertyManager;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.commons.lang3.tuple.ImmutablePair;
import org.apache.commons.lang3.tuple.Pair;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cache.CacheManager;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.lang.reflect.Type;
import java.text.DecimalFormat;
import java.text.MessageFormat;
import java.time.DayOfWeek;
import java.time.LocalDate;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.mmt.hotels.clientgateway.constants.Constants.*;
import static com.mmt.hotels.clientgateway.constants.ConstantsTranslation.ADDITIONAL_FEE_SUBTEXT_ROOM;
import static com.mmt.hotels.clientgateway.constants.ConstantsTranslation.ADDITIONAL_FEE_SUBTEXT_ROOMS;
import static com.mmt.hotels.clientgateway.constants.ConstantsTranslation.INSTANT_BOOKING;
import static com.mmt.hotels.clientgateway.constants.ConstantsTranslation.*;
import static com.mmt.hotels.clientgateway.constants.ControllerConstants.DETAIL_SEARCH_ROOMS;
import static com.mmt.hotels.clientgateway.constants.ControllerConstants.DETAIL_SEARCH_SLOTS;
import static com.mmt.hotels.clientgateway.enums.ExperimentKeys.APPLY_FILTER_TO_COMBO;
import static com.mmt.hotels.clientgateway.util.Utility.*;
import static com.mmt.hotels.model.response.staticdata.PriceVariationType.DROP;
import static com.mmt.hotels.model.response.staticdata.PriceVariationType.UNAVAILABLE;
import static java.lang.Math.ceil;
import static java.lang.Math.max;
import static java.lang.Math.min;

@Component
public abstract class SearchRoomsResponseTransformer {

	@Autowired
	private CommonResponseTransformer commonResponseTransformer;
	@Autowired
	protected PersuasionUtil persuasionUtil;

	@Autowired
	private SearchHotelsFactory searchHotelsFactory;

	@Autowired
	private Utility utility;

	@Autowired
	PropertyManager propManager;

	@Autowired
	private DateUtil dateUtil;

	@Autowired
	private DayUseUtil dayUseUtil;

	@Autowired
	private MetricAspect metricAspect;

	@Value("${consul.enable}")
	private boolean consulFlag;
	@Autowired
	CommonConfigConsul commonConfigConsul;

	@Value("${pah.without.cc.text}")
	private String pahWithoutCCText;

	@Value("${pah.with.cc.text}")
	private String pahWithCCText;

	@Value("${pah.gcc.text}")
	private String pahGccText;

	@Value("${super.package.icon.url}")
	private String superPackageIconUrl;

	@Value("${view.360.icon.url}")
	private String view360IconUrl;

	@Value("${super.package.icon.url.secondary}")
	private String superPackageIconUrlSecondary;

	@Value("${default.search.room.url}")
	private String defaultSearchRoomUrl;

	@Value("${red.cross.Icon}")
	private String redCrossIcon;

	@Value("${free.kids.inclusion.icon.url}")
	private String freeChildInclusionIcon;

	@Value("${negotiated.rates.delayed.confirmation.no.of.hours}")
	protected int noOfHoursForConfirmation;


	@Value("#{'${mypat_exclusive_rate_segmentId.list}'.split(',')}")
	private Set<String> mypatExclusiveRateSegmentIdList;

	@Value("${corp.one.on.one.segmentId}")
	private String corpPreferredRateSegmentId;

	@Value("${negotiated.rate.icon.url}")
	private String negotiatedRateIconUrl;

	@Value("${negotiated.rate.icon.url.newApp}")
	private String negotiatedRateIconUrlNewApp;

	@Value("${flyer.persuasion.color.detail}")
	private String flyerPersuasionColorDetail;

	@Value("${vistara.persuasion.color.detail}")
	private String vistaraPersuasionColorDetail;

	@Value("${bus.persuasion.color.detail}")
	private String busPersuasionColorDetail;

	@Value("${train.persuasion.color.detail}")
	private String trainPersuasionColorDetail;

	@Value("${flyer.persuasion.image.url.detail}")
	private String flyerPersuasionImageUrlDetail;

	@Value("${vistara.persuasion.image.url.detail}")
	private String vistaraPersuasionImageUrlDetail;

	@Value("${vistara.persuasion.image.url.detail.dt}")
	private String vistaraPersuasionImageUrlDetailDT;

	@Value("${bus.persuasion.image.url.detail}")
	private String busPersuasionImageUrlDetail;

	@Value("${train.persuasion.image.url.detail}")
	private String trainPersuasionImageUrlDetail;

	@Value("${flyer.gcc.persuasion.image.url.detail}")
	private String flyerGccPersuasionImageUrlDetail;

	@Value("${flyer.persuasion.image.url.detail.dt}")
	private String flyerPersuasionImageUrlDetailDT;

	@Value("${bus.persuasion.image.url.detail.dt}")
	private String busPersuasionImageUrlDetailDT;

	@Value("${train.persuasion.image.url.detail.dt}")
	private String trainPersuasionImageUrlDetailDT;

	@Value("${early.bird.icon.url}")
	private String earlyBirdIconUrl;

	@Value("${last.minute.icon.url}")
	private String lastMinuteIconUrl;

	private String noCostEmiIconUrl;
	private BgGradient noCostEmiIconConfig;

	@Value("${supplier.deals.bg.color}")
	private String supplierBgColor;

	@Value("#{'${combo.title.meal.plan.code}'.split(',')}")
	private List<String> mealPlanCodeList;

	@Value("${single.tick.url}")
	private String singleTickUrl;

	@Value("${long.stay.gcc.nudge.iconUrl}")
	private String longStayGccNudgeIconUrl;

	@Value("${extra.guest.free.child.color}")
	private String extraGuestFreeChildColor;

	@Value("${high.demand.persuasion.color}")
	private String highDemandPersuasionColor;

	@Value("${dot.icon.url}")
	private String dotIconUrl;

	@Value("${black.icon.details.page.url}")
	private String blackIconDetailsPage;

	@Value("${addon.info.most.popular.tag}")
	private String addOnInfoMostPopularTagConfig;

	@Value("${food.rating.thresold}")
	private int foodRatingThresold;

	@Value("${call.to.book.iconUrl}")
	private String callToBookIconUrl;

	@Value("${call.to.book.title}")
	private String callToBookTitle;

	@Value("${call.to.book.option}")
	private String callToBookOption;

	//TODO need to update this
	@Value("${black.revamp.fallback.bullet.icon}")
	private String stayInfoIcon;

	@Value("${los.icon.url.room}")
	private String losIconUrl;

	@Value("${los.position.select.room}")
	private int losPositionSelectRoom;

	@Value("${bathroom.stay.info.icon}")
	private String bathroomInfoIcon;

	@Value("${kitchen.stay.info.icon}")
	private String kitchenStayInfoIcon;

	@Value("${livingroom.stay.info.icon}")
	private String livingroomStayInfoIcon;

	@Value("${bedroom.stay.info.icon}")
	private String bedroomStayInfoIcon;

	@Value("${price.graph.text.icon}")
	private String priceGraphTextIcon;

	@Value("${price.graph.icon}")
	private String priceGraphIcon;

	@Value("${price.graph.recommended.icon}")
	private String priceGraphRecommendedIcon;


	private int apLimitForInclusionIcons = 2;

	private int losFosGCCNudgePersuasion ;

	private Map<String, String> mealPlanMapPolyglot;

	private int ratePlanMoreOptionsLimit = 1;
	private boolean mealplanFilterEnable;
	private boolean partnerExclusiveFilterEnable;
	private Map<String, Map<String, List<String>>> supplierToRateSegmentMapping;

	@Autowired
	protected PolyglotService polyglotService;

	@Autowired
	private ObjectMapperUtil objectMapperUtil;

	@Autowired
	private CacheManager cacheManager;

	private static final Gson gson = new Gson();
	private static final Logger LOGGER = LoggerFactory.getLogger(SearchRoomsResponseTransformer.class);

	private Map<String,Map<String,Map<String,Integer>>> ratePlanDisplayLogic;
	Map<String, String> rtbCardConfigs;

	String mandatoryChargesAlert;

	AllInclusiveCard allInclusiveCard;

	private MissingSlotDetail missingSlotDetails = null;

	private Map<String, DayUsePersuasion> dayUseFunnelPersuasions = null;

	private int thresholdForSlashedAndDefaultHourPrice = 0;
	private String recommendedRoomPropertiesConfig;
	private Map<String, Map<String, String>> recommendedRoomPropertiesMap;

	private static final Logger logger = LoggerFactory.getLogger(SearchRoomsResponseTransformer.class);

	@Autowired
	private MobConfigHelper mobConfigHelper;

	private Map<String,StayTypeInfo> actionInfoMap = new HashMap<>();

	private Map<String, String> addOnInfoMostPopularTag;

	private FlexiCancelStaticDetail flexiCancelStaticDetail;

	private LinkedRatePlanStyle linkedRatePlanStyle;

	private ExtraAdultChildInclusionConfig extraAdultChildInclusionConfig;

	@Autowired
	private CorporateService corporateService;

	@PostConstruct
	public void init() {
		if(consulFlag){
			thresholdForSlashedAndDefaultHourPrice = commonConfigConsul.getThresholdForSlashedAndDefaultHourPrice();
			recommendedRoomPropertiesConfig = commonConfigConsul.getRecommendedRoomProperties();
			mealPlanMapPolyglot = commonConfigConsul.getMealPlanMapPolyglot();
			ratePlanMoreOptionsLimit  = commonConfigConsul.getRatePlanMoreOptionsLimit();
			ratePlanDisplayLogic = commonConfigConsul.getRatePlanDisplayLogic();
			apLimitForInclusionIcons = commonConfigConsul.getApLimitForInclusionIcons();
			mealplanFilterEnable = commonConfigConsul.isMealplanFilterEnable();
			partnerExclusiveFilterEnable = commonConfigConsul.isPartnerExclusiveFilterEnable();
			rtbCardConfigs = commonConfigConsul.getRtbCardConfigs();
			mandatoryChargesAlert = commonConfigConsul.getMandatoryChargesAlert();
			allInclusiveCard = commonConfigConsul.getAllInclusiveCard();
			supplierToRateSegmentMapping = commonConfigConsul.getSupplierToRateSegmentMapping();
			//Added missing slot and persuasion details of dayUse detail page
			missingSlotDetails = commonConfigConsul.getMissingSlotDetails();
			dayUseFunnelPersuasions = commonConfigConsul.getDayUseFunnelPersuasions();
			noCostEmiIconUrl = commonConfigConsul.getNoCostEmiIconUrl();
			flexiCancelStaticDetail = commonConfigConsul.getFlexiCancelStaticDetail();
			noCostEmiIconConfig = commonConfigConsul.getNoCostEmiIconConfig();
			addOnInfoMostPopularTag = gson.fromJson(addOnInfoMostPopularTagConfig,HashMap.class);
			losFosGCCNudgePersuasion = commonConfigConsul.getLosFosGCCNudgePersuasion();
			linkedRatePlanStyle = commonConfigConsul.getLinkedRatePlanStyle();
			extraAdultChildInclusionConfig = commonConfigConsul.getExtraAdultChildInclusionConfig();
			try{
				Map<String, JsonNode> configJsonNodeMapEnglish = new HashMap<>();
				configJsonNodeMapEnglish = mobConfigHelper.populateConfigNodeMap(configJsonNodeMapEnglish, null, "eng");
				JsonNode actionInfoJsonNode = configJsonNodeMapEnglish.get("A").get(CONSUL_NODE_ACTION_INFO);
				actionInfoMap = objectMapperUtil.getObjectFromJsonNode(actionInfoJsonNode,  new TypeReference<Map<String,StayTypeInfo>>() {
				});
				recommendedRoomPropertiesMap = objectMapperUtil.getObjectFromJsonWithType(recommendedRoomPropertiesConfig,  new TypeReference<Map<String,Map<String, String>>>() {
				}, DependencyLayer.CLIENTGATEWAY);
			}catch (Exception e){
				LOGGER.error("Error while fetching actionInfo from commonConfig consul");
			}
			LOGGER.debug("Fetching values from commonConfig consul");

		}
		else{
			CommonConfig commonConfig = propManager.getProperty("commonConfig", CommonConfig.class);
			thresholdForSlashedAndDefaultHourPrice = commonConfig.thresholdForSlashedAndDefaultHourPrice();
			mealPlanMapPolyglot = commonConfig.mealPlanMapPolyglot();
			commonConfig.addPropertyChangeListener("mealPlanMapPolyglot", evt -> mealPlanMapPolyglot = commonConfig.mealPlanMapPolyglot());
			ratePlanMoreOptionsLimit  = commonConfig.ratePlanMoreOptionsLimit();
			commonConfig.addPropertyChangeListener("ratePlanMoreOptionsLimit", evt -> ratePlanMoreOptionsLimit = commonConfig.ratePlanMoreOptionsLimit());
			ratePlanDisplayLogic = commonConfig.ratePlanDisplayLogic();
			commonConfig.addPropertyChangeListener("ratePlanDisplayLogic",evt->ratePlanDisplayLogic = commonConfig.ratePlanDisplayLogic());
			apLimitForInclusionIcons = commonConfig.apLimitForInclusionIcons();
			mealplanFilterEnable = commonConfig.mealplanFilterEnable();
			commonConfig.addPropertyChangeListener("mealplanFilterEnable", event -> mealplanFilterEnable = commonConfig.mealplanFilterEnable());
			partnerExclusiveFilterEnable = commonConfig.partnerExclusiveFilterEnable();
			commonConfig.addPropertyChangeListener("partnerExclusiveFilterEnable", event -> partnerExclusiveFilterEnable = commonConfig.partnerExclusiveFilterEnable());
			rtbCardConfigs = commonConfig.rtbCardConfigs();
			commonConfig.addPropertyChangeListener("rtbCardConfigs", event -> rtbCardConfigs = commonConfig.rtbCardConfigs());
			mandatoryChargesAlert = commonConfig.mandatoryChargesAlert();
			commonConfig.addPropertyChangeListener("mandatoryChargesAlert", event -> mandatoryChargesAlert = commonConfig.mandatoryChargesAlert());
			allInclusiveCard = commonConfig.allInclusiveCard();
			commonConfig.addPropertyChangeListener("allInclusiveCard", event -> allInclusiveCard = commonConfig.allInclusiveCard());
			supplierToRateSegmentMapping = commonConfig.supplierToRateSegmentMapping();
			commonConfig.addPropertyChangeListener("supplierToRateSegmentMapping", event -> supplierToRateSegmentMapping = commonConfig.supplierToRateSegmentMapping());
			//Added missing slot and persuasion details of dayUse detail page
			missingSlotDetails = commonConfig.missingSlotDetails();
			commonConfig.addPropertyChangeListener("missingSlotDetails", event -> missingSlotDetails = commonConfig.missingSlotDetails());
			dayUseFunnelPersuasions = commonConfig.dayUseFunnelPersuasions();
			commonConfig.addPropertyChangeListener("dayUseFunnelPersuasions", event -> dayUseFunnelPersuasions = commonConfig.dayUseFunnelPersuasions());

		}

	}

	/*
	 * myPartner change log :
	 * 	This original method signature is retained. This makes test cases backward compatible
	 * 	The overloaded method is used for all other cases
	 * */
	public SearchRoomsResponse convertSearchRoomsResponse(RoomDetailsResponse roomDetailsResponse, HotelsRoomInfoResponseEntity hotelsRoomInfoResponseEntity,
														  HotelImage hotelImage, String expData, List<RoomStayCandidate> roomStayCandidates,
														  SearchCriteria searchRoomsCriteria, List<Filter> filterCriteria, String expVariantKeys, RequestDetails requestDetails) {
		return convertSearchRoomsResponse(null,roomDetailsResponse, hotelsRoomInfoResponseEntity,
				hotelImage, expData, roomStayCandidates,
				searchRoomsCriteria, filterCriteria, requestDetails, expVariantKeys, null);
	}

	/*
	 * myPartner change log :
	 * 	convertSearchRoomsResponse is overloaded to have another parameter CommonModifierResponse which contains profileType and subProfileType
	 *
	 * 	unlike filter-count api [where another parameter is added to the existing method] without overloading for test cases [since there was
	 * 	only a single test case and that is been handled]
	 * 	All calls B2C/CORP/myPartner will flow through this
	 * */
	public abstract String getHtml();

	public SearchRoomsResponse convertSearchRoomsResponse(SearchRoomsRequest searchRoomsRequest,RoomDetailsResponse roomDetailsResponse, HotelsRoomInfoResponseEntity hotelsRoomInfoResponseEntity,
														  HotelImage hotelImage, String expData, List<RoomStayCandidate> roomStayCandidates, SearchCriteria searchRoomsCriteria,
														  List<Filter> filterCriteria, RequestDetails requestDetails, String expVariantKeys, CommonModifierResponse commonModifierResponse) {
		SearchRoomsResponse searchRoomsResponse = new SearchRoomsResponse();
		boolean isRTBCTrue = commonModifierResponse != null && MapUtils.isNotEmpty(commonModifierResponse.getExpDataMap()) ? utility.isExperimentOn(commonModifierResponse.getExpDataMap(), EXP_RTBC) : false;
		boolean isNewDetailPageTrue = commonModifierResponse != null && MapUtils.isNotEmpty(commonModifierResponse.getExpDataMap()) ? utility.isExperimentTrue(commonModifierResponse.getExpDataMap(), NEW_DETAIL_PAGE) : false;
		boolean isNewBlackDeal = commonModifierResponse != null && MapUtils.isNotEmpty(commonModifierResponse.getExpDataMap()) ? utility.isExperimentOn(commonModifierResponse.getExpDataMap(), NEW_BLACK_DEAL) : false;
		boolean blackRevamp = commonModifierResponse != null && MapUtils.isNotEmpty(commonModifierResponse.getExpDataMap()) && utility.isExperimentTrue(commonModifierResponse.getExpDataMap(), ExperimentKeys.BLACK_REVAMP.getKey());
		boolean isCallToBookV2Applicable = commonModifierResponse != null && MapUtils.isNotEmpty(commonModifierResponse.getExpDataMap()) ? utility.isExperimentValid(commonModifierResponse.getExpDataMap(), callToBook,4) : false;
		boolean isMyBizNewDetailsPage = commonModifierResponse != null && MapUtils.isNotEmpty(commonModifierResponse.getExpDataMap()) && utility.isExperimentTrue(commonModifierResponse.getExpDataMap(), Constants.MYB_NEW_DETAILS_EXP_KEY);
		int ancillaryVariant = commonModifierResponse != null &&  MapUtils.isNotEmpty(commonModifierResponse.getExpDataMap()) && commonModifierResponse.getExpDataMap().containsKey(ExperimentKeys.ANCILLARY_DISPLAY_PRICE_VARIANT.getKey()) ? Integer.parseInt(commonModifierResponse.getExpDataMap().get(ExperimentKeys.ANCILLARY_DISPLAY_PRICE_VARIANT.getKey())) : 0;
		Set<String> hydraSegments = commonModifierResponse != null && commonModifierResponse.getHydraResponse() != null ? commonModifierResponse.getHydraResponse().getHydraMatchedSegment() : new HashSet<>();
		boolean isLiteResponse = searchRoomsRequest != null && searchRoomsRequest.getFeatureFlags() != null && searchRoomsRequest.getFeatureFlags().isLiteResponse();
		Map<String, String> expDataMap = commonModifierResponse != null ? commonModifierResponse.getExpDataMap() : null;
		searchRoomsResponse.setCurrency(roomDetailsResponse.getCurrency());

		if(commonModifierResponse != null){
			commonModifierResponse.setLiteResponse(isLiteResponse);
		}
		long startTime = System.currentTimeMillis();
		try {
			String client = MDC.get(MDCHelper.MDCKeys.CLIENT.getStringValue());
			String askedCurrency = searchRoomsCriteria != null ? searchRoomsCriteria.getCurrency() : "INR";
			if (CollectionUtils.isEmpty(roomDetailsResponse.getHotelRates())) {
				return searchRoomsResponse;
			}
			buildAlternatePriceCard(roomDetailsResponse, searchRoomsResponse, searchRoomsRequest);
			HotelRates hotelRates = roomDetailsResponse.getHotelRates().get(0);
			boolean isLuxeHotel = utility.isLuxeHotel(hotelRates.getCategories());
			boolean isAltAccoHotel = hotelRates.isAltAcco();
			boolean isHighSellingAltAcco = hotelRates.isHighSellingAltAcco();
			String propertyType = hotelRates.getPropertyTypeMerged()!=null?hotelRates.getPropertyTypeMerged():hotelRates.getPropertyType();
			String deviceType = searchRoomsRequest != null ? searchRoomsRequest.getDeviceDetails() != null ? searchRoomsRequest.getDeviceDetails().getDeviceType() : EMPTY_STRING : EMPTY_STRING;
			String checkIn = searchRoomsCriteria.getCheckIn();
			String checkOut = searchRoomsCriteria.getCheckOut();
			int los = dateUtil.getDaysDiff(LocalDate.parse(checkIn), LocalDate.parse(checkOut));
			int ap = dateUtil.getDaysDiff(LocalDate.now(), LocalDate.parse(checkIn));
			boolean isBlockPAH = roomDetailsResponse.getExpData() != null && roomDetailsResponse.getExpData().containsKey("blockPAH")
					&& StringUtils.isNotBlank(roomDetailsResponse.getExpData().get("blockPAH")) && Boolean.parseBoolean(roomDetailsResponse.getExpData().get("blockPAH"));
			boolean isOHSExpEnable = utility.isOHSExpEnable(hotelRates.getPropertyType(),(commonModifierResponse!=null?commonModifierResponse.getExpDataMap():null)); // Experiment for OPTIMISE HOSTEL SELECTION
			boolean isIHAAOrch = utility.isExperimentTrue(commonModifierResponse != null ? commonModifierResponse.getExpDataMap() : null, "IHAAOrch");
			boolean showOccassionPackagesPlan = commonModifierResponse != null && MapUtils.isNotEmpty(commonModifierResponse.getExpDataMap()) ? utility.isExperimentTrue(commonModifierResponse.getExpDataMap(), SHOW_OCC_PACKAGE) : false;
			boolean showMandatoryChargesDH = commonModifierResponse != null && MapUtils.isNotEmpty(commonModifierResponse.getExpDataMap()) ? utility.isExperimentTrue(commonModifierResponse.getExpDataMap(), SHOW_MANDATORY_CHARGES_DH) : false;
			String siteDomain = requestDetails.getSiteDomain();
			String selectedRoomCode = searchRoomsRequest != null && searchRoomsRequest.getSearchCriteria().getSelectedRatePlan() != null ? searchRoomsRequest.getSearchCriteria().getSelectedRatePlan().getRoomCode() : "";
			String selectedRateplanCode = searchRoomsRequest != null && searchRoomsRequest.getSearchCriteria().getSelectedRatePlan() != null ? searchRoomsRequest.getSearchCriteria().getSelectedRatePlan().getRatePlanCode() : "";
			if(showOccassionPackagesPlan) {
				searchRoomsResponse.setPersuasions(commonResponseTransformer.buildPersuasions(hotelRates, deviceType));
			} else {
				searchRoomsResponse.setOffers(getOffers(hotelRates.getOffers()));
				searchRoomsResponse.setAppliedOffers(getOffers(hotelRates.getAppliedOffers()));
				searchRoomsResponse.setBlackInfo(commonResponseTransformer.buildBlackInfo(hotelRates.getBlackInfo()));
				searchRoomsResponse.setLongStayBenefits(commonResponseTransformer.buildLongStayBenefits(hotelRates.getLongStayBenefits()));
			}
			searchRoomsResponse.setLongStayPersuasion(commonResponseTransformer.buildLongStayBenefits(hotelRates.getLongStayPersuasion()));
			//code changes for Persuastion
			//to do :: versioning check
			if (isNewBlackDeal && searchRoomsResponse.getBlackInfo() != null) {
				searchRoomsResponse.getBlackInfo().setSeparator(DOT);
			}
			if(Objects.nonNull(hotelRates.getOccassionPackageRoomDetails())){
				searchRoomsResponse.setSpecialOfferCard(commonResponseTransformer.buildSpecialOfferCard(hotelRates.getOccassionPackageRoomDetails()));
			}
			searchRoomsResponse.setSpotlightApplicable(hotelRates.isSpotlightApplicable());
			Map<String, String> trackingMap = roomDetailsResponse.getTrackingMap();
			if(commonModifierResponse != null && (REGION_AE).equalsIgnoreCase(commonModifierResponse.getRegion()) && hotelRates != null) {
				String businessOwnerID = commonResponseTransformer.getEvarBasedOnCountryAndRegion(commonModifierResponse.getUserCountry(), hotelRates.getCountryCode());
				if (StringUtils.isNotEmpty(businessOwnerID)) {
					if (MapUtils.isEmpty(trackingMap)) {
						trackingMap = new HashMap<>();
					}
					if (trackingMap.containsKey(EVAR_126)) {
						trackingMap.put(EVAR_126, trackingMap.get(EVAR_126).toString() + "|" + businessOwnerID);
					} else {
						trackingMap.put(EVAR_126, businessOwnerID);
					}
				}
			}
			searchRoomsResponse.setTrackingMap(commonResponseTransformer.buildTrackingMap(trackingMap));
			searchRoomsResponse.setDoubleBlackInfo(commonResponseTransformer.getDoubleBlackInfo(hotelRates.getDoubleBlackInfo()));
			searchRoomsResponse.setMsmeCorpCard(hotelRates.getMsmeCorpCard());
			searchRoomsResponse.setHydraSegments(roomDetailsResponse.getHydraSegments());
			searchRoomsResponse.setUserLoyaltyStatus(hotelRates.getUserLoyaltyStatus());
			searchRoomsResponse.setExpVariantKeys(StringUtils.isNotBlank(expVariantKeys) ? expVariantKeys : null);
			if (utility.isExperimentOn(roomDetailsResponse.getExpData(), Constants.ALL_INCLUSIVE_PLAN_EXPERIMENT) && hotelRates.isAnyRateAllinclusive()) {
				searchRoomsResponse.setAllInclusiveInclusions(hotelRates.getAllInclusiveInclusions());
				searchRoomsResponse.setAllInclusiveCard(buildAllInclusiveCard(hotelRates));
			}
			Map<String, String> ratePlanCodeAndNameMap = new HashMap<>();
			/** HTL-40907: Set instant fare information required to be shown in details page in case of negotiated rate hotels flow.
			 * Negotiated rates are the one-on-one rates that are directly negotiated between the corporate/organization and the hotel.
			 * This value is obtained from pricer to orchestrator.
			 */
			if (hotelRates.isNegotiatedRateFlag()) {
				buildInstantFareInfo(roomDetailsResponse.getCorpAlias(), searchRoomsResponse, hotelRates.getCurrencyCode(), isMyBizNewDetailsPage);
			}
			if (StringUtils.isNotEmpty(hotelRates.getAddOnErrorMessage())) {
				searchRoomsResponse.setAddOnErrorMessage(hotelRates.getAddOnErrorMessage());
			}
			/* Make Diff Types of Rooms */
			if (hotelRates.getRoomTypeDetails() != null && hotelRates.getOccupencyLessRoomTypeDetails() == null) {
				/*
				 * myPartner change log : commonModifierResponse floated down
				 * */
				searchRoomsResponse.setExactRooms(getRooms(
						hotelRates.getRoomTypeDetails(), hotelsRoomInfoResponseEntity, hotelImage, hotelRates.getListingType(),
						expData, true, askedCurrency, requestDetails.getFunnelSource(), hotelRates.getWalletSurge(),
						hotelRates.getSegments(), los, hotelRates.getStarRating(), ap, isBlockPAH, hotelRates.getRoomPersuasions(),
						commonModifierResponse, false, ratePlanCodeAndNameMap, isLuxeHotel, isAltAccoHotel,
						hotelRates.isUserGCCAndMmtExclusive(), hotelRates.isGroupBookingPrice(), isOHSExpEnable,
						false, searchRoomsResponse.getSpecialFareInfo(), roomDetailsResponse.getCorpAlias(),
						true,propertyType, hotelRates.getCountryCode(),
						hotelRates.getMarkUpDetails(), roomDetailsResponse.getFoodRating(),true, siteDomain,
						selectedRoomCode, selectedRateplanCode, false, hotelRates, false, isHighSellingAltAcco));
				searchRoomsResponse.setExtraGuestDetailPersuasion(buildExtraGuestDetailPersuasion(roomDetailsResponse.isHighDemandPersuasonEnable(),hotelRates.getRoomTypeDetails(), isAltAccoHotel));
			} else {
				searchRoomsResponse.setRecommendedCombos(getRecommendedCombos(hotelRates, propertyType, hotelsRoomInfoResponseEntity, hotelImage, hotelRates.getListingType(), expData, askedCurrency, requestDetails.getFunnelSource(),
						los, ap, isBlockPAH, hotelRates.getRoomPersuasions(), commonModifierResponse, ratePlanCodeAndNameMap,
						isLuxeHotel, isAltAccoHotel, isOHSExpEnable, searchRoomsResponse.getSpecialFareInfo(), roomDetailsResponse.getCorpAlias(),
						hotelRates.getMarkUpDetails(), roomDetailsResponse.getFoodRating(), siteDomain, selectedRoomCode, selectedRateplanCode, isHighSellingAltAcco));
				if(searchRoomsResponse.getExtraGuestDetailPersuasion()==null) {
					searchRoomsResponse.setExtraGuestDetailPersuasion(buildExtraGuestDetailPersuasion(roomDetailsResponse.isHighDemandPersuasonEnable(),hotelRates.getRecommendedRoomTypeDetails(), isAltAccoHotel));
				}
				if(searchRoomsResponse.getExtraGuestDetailPersuasion()==null &&  CollectionUtils.isNotEmpty(hotelRates.getOtherRecommendedRooms())) {
					searchRoomsResponse.setExtraGuestDetailPersuasion(buildExtraGuestDetailPersuasion(roomDetailsResponse.isHighDemandPersuasonEnable(),hotelRates.getOtherRecommendedRooms().get(0), isAltAccoHotel));
				}
			}
			searchRoomsResponse.setOccupancyRooms(getRooms(
					hotelRates.getOccupencyLessRoomTypeDetails(), hotelsRoomInfoResponseEntity, hotelImage, hotelRates.getListingType(),
					expData, false, askedCurrency, requestDetails.getFunnelSource(), hotelRates.getWalletSurge(),
					hotelRates.getSegments(), los, hotelRates.getStarRating(), ap, isBlockPAH, hotelRates.getRoomPersuasions(),
					commonModifierResponse, false, ratePlanCodeAndNameMap, isLuxeHotel, isAltAccoHotel,
					hotelRates.isUserGCCAndMmtExclusive(), hotelRates.isGroupBookingPrice(), isOHSExpEnable,
					false, searchRoomsResponse.getSpecialFareInfo(), roomDetailsResponse.getCorpAlias(),true,
					propertyType, hotelRates.getCountryCode(), hotelRates.getMarkUpDetails(),
					roomDetailsResponse.getFoodRating(),false, siteDomain, selectedRoomCode, selectedRateplanCode,
					false, hotelRates, false, isHighSellingAltAcco));
			if (Constants.LISTING_TYPE_ENTIRE.equalsIgnoreCase(hotelRates.getListingType())) {
				if(hotelRates.getRoomCount() > 1){
					searchRoomsResponse.setPropertyLayoutTitleText(MessageFormat.format(polyglotService.getTranslatedData(MULTIPLE_ENTIRE_PROPERTY_LAYOUT_TEXT), hotelRates.getRoomCount(), propertyType,propertyType));
				} else{
					searchRoomsResponse.setPropertyLayoutTitleText(MessageFormat.format(polyglotService.getTranslatedData(SINGLE_ENTIRE_PROPERTY_LAYOUT_TEXT),propertyType));
				}
			} else {
				if (getRoomTypeCount(hotelRates) > 1) {
					searchRoomsResponse.setPropertyLayoutTitleText(polyglotService.getTranslatedData(DEFAULT_PROPERTY_LAYOUT_TEXT));
				}
			}
			if(searchRoomsResponse.getExtraGuestDetailPersuasion()==null) {
				searchRoomsResponse.setExtraGuestDetailPersuasion(buildExtraGuestDetailPersuasion(roomDetailsResponse.isHighDemandPersuasonEnable(),hotelRates.getOccupencyLessRoomTypeDetails(), isAltAccoHotel));
			}
			if (hotelRates.getPackageRoomDetails() != null && CollectionUtils.isNotEmpty(searchRoomsResponse.getExactRooms()) && (commonModifierResponse==null || !utility.isExperimentTrue(commonModifierResponse.getExpDataMap(), ExperimentKeys.SHOW_RECOMMENDED_ROOMS.getKey()))) {
				searchRoomsResponse.setPackageRooms(getRooms(
						hotelRates.getPackageRoomDetails(), hotelsRoomInfoResponseEntity, hotelImage, hotelRates.getListingType(),
						expData, true, askedCurrency, requestDetails.getFunnelSource(), hotelRates.getWalletSurge(),
						hotelRates.getSegments(), los, hotelRates.getStarRating(), ap, isBlockPAH,
						hotelRates.getRoomPersuasions(), commonModifierResponse, true, ratePlanCodeAndNameMap,
						isLuxeHotel, isAltAccoHotel, hotelRates.isUserGCCAndMmtExclusive(), hotelRates.isGroupBookingPrice(),
						isOHSExpEnable, false, searchRoomsResponse.getSpecialFareInfo(), roomDetailsResponse.getCorpAlias(),
						false ,propertyType, hotelRates.getCountryCode(), hotelRates.getMarkUpDetails(),
						roomDetailsResponse.getFoodRating(),false, siteDomain, selectedRoomCode, selectedRateplanCode,
						false, hotelRates, false, isHighSellingAltAcco));
				updatePackageInclusionBaseRatePlanName(ratePlanCodeAndNameMap, searchRoomsResponse.getPackageRooms());
				if (searchRoomsResponse.getExtraGuestDetailPersuasion() == null) {
					searchRoomsResponse.setExtraGuestDetailPersuasion(buildExtraGuestDetailPersuasion(roomDetailsResponse.isHighDemandPersuasonEnable(),hotelRates.getPackageRoomDetails(), isAltAccoHotel));
				}
			}

			if(searchRoomsRequest != null && searchRoomsRequest.getDeviceDetails() != null){
				searchRoomsResponse.setPriceGraphInfo(getPriceGraphInfo(hotelRates.getPriceVariation(), askedCurrency, commonModifierResponse.getExpDataMap()));
			}

			boolean packageRoomPresent = false;
			if(commonModifierResponse!=null && utility.isExperimentTrue(commonModifierResponse.getExpDataMap(), ExperimentKeys.SHOW_RECOMMENDED_ROOMS.getKey())) {
				if(Objects.nonNull(hotelRates.getOccassionPackageRoomDetails()) && Objects.nonNull(hotelRates.getOccassionPackageRoomDetails().getOccassionDetails())) {
					searchRoomsResponse.setRecommendedRooms(getRooms(hotelRates.getOccassionPackageRoomDetails(), hotelsRoomInfoResponseEntity, hotelImage, hotelRates.getListingType(), expData, true,
							askedCurrency, requestDetails.getFunnelSource(), hotelRates.getWalletSurge(),
							hotelRates.getSegments(), los, hotelRates.getStarRating(), ap, isBlockPAH,
							hotelRates.getRoomPersuasions(), commonModifierResponse, false, ratePlanCodeAndNameMap,
							isLuxeHotel, isAltAccoHotel, hotelRates.isUserGCCAndMmtExclusive(), hotelRates.isGroupBookingPrice(),
							isOHSExpEnable, false, searchRoomsResponse.getSpecialFareInfo(), roomDetailsResponse.getCorpAlias(),
							false ,propertyType, hotelRates.getCountryCode(), hotelRates.getMarkUpDetails(), roomDetailsResponse.getFoodRating(),
							false, siteDomain, selectedRoomCode, selectedRateplanCode, false, hotelRates, true, isHighSellingAltAcco));
				} else {
					searchRoomsResponse.setRecommendedRooms(getRooms(hotelRates.getPackageRoomDetails(), hotelsRoomInfoResponseEntity, hotelImage, hotelRates.getListingType(), expData, true,
							askedCurrency, requestDetails.getFunnelSource(), hotelRates.getWalletSurge(),
							hotelRates.getSegments(), los, hotelRates.getStarRating(), ap, isBlockPAH,
							hotelRates.getRoomPersuasions(), commonModifierResponse, true, ratePlanCodeAndNameMap, isLuxeHotel,
							isAltAccoHotel, hotelRates.isUserGCCAndMmtExclusive(), hotelRates.isGroupBookingPrice(), isOHSExpEnable,
							false, searchRoomsResponse.getSpecialFareInfo(), roomDetailsResponse.getCorpAlias(), false, propertyType,
							hotelRates.getCountryCode(), hotelRates.getMarkUpDetails(), roomDetailsResponse.getFoodRating(), false, siteDomain,
							selectedRoomCode, selectedRateplanCode, false, hotelRates, false, isHighSellingAltAcco));
					if(CollectionUtils.isNotEmpty(searchRoomsResponse.getRecommendedRooms())) {
						packageRoomPresent = true;
					}
				}
				if(utility.isExperimentTrue(commonModifierResponse.getExpDataMap(), ExperimentKeys.SHOW_UPSELL_RECOMMENDATION.getKey())) {
					List<RoomDetails> recommendedRoom = new ArrayList<>();
					if(!utility.isExperimentTrue(commonModifierResponse.getExpDataMap(), ExperimentKeys.recommendationV1.getKey())) {
						List<RoomDetails> recommendedRoomClients = getRooms(
								hotelRates.getMealUpsellRoomDetails(), hotelsRoomInfoResponseEntity, hotelImage,
								hotelRates.getListingType(), expData, true,
								askedCurrency, requestDetails.getFunnelSource(), hotelRates.getWalletSurge(),
								hotelRates.getSegments(), los, hotelRates.getStarRating(), ap, isBlockPAH,
								hotelRates.getRoomPersuasions(), commonModifierResponse, false, ratePlanCodeAndNameMap, isLuxeHotel,
								isAltAccoHotel, hotelRates.isUserGCCAndMmtExclusive(), hotelRates.isGroupBookingPrice(), isOHSExpEnable,
								false, searchRoomsResponse.getSpecialFareInfo(), roomDetailsResponse.getCorpAlias(),
								false, propertyType, hotelRates.getCountryCode(), hotelRates.getMarkUpDetails(), roomDetailsResponse.getFoodRating(),
								false, siteDomain, selectedRoomCode, selectedRateplanCode, true, hotelRates,false, isHighSellingAltAcco);
						if(CollectionUtils.isNotEmpty(recommendedRoomClients)) {
							recommendedRoom.addAll(recommendedRoomClients);
						}
					} else if (hotelRates!=null && CollectionUtils.isNotEmpty(hotelRates.getRecommendedRooms())){
						for(RoomTypeDetails recommendation : hotelRates.getRecommendedRooms()) {
							List<RoomDetails> recommendedRoomClients = getRooms(
									recommendation, hotelsRoomInfoResponseEntity, hotelImage, hotelRates.getListingType(),
									expData, true, askedCurrency, requestDetails.getFunnelSource(), hotelRates.getWalletSurge(),
									hotelRates.getSegments(), los, hotelRates.getStarRating(), ap, isBlockPAH,
									hotelRates.getRoomPersuasions(), commonModifierResponse, false, ratePlanCodeAndNameMap, isLuxeHotel, isAltAccoHotel,
									hotelRates.isUserGCCAndMmtExclusive(), hotelRates.isGroupBookingPrice(), isOHSExpEnable, false, searchRoomsResponse.getSpecialFareInfo(),
									roomDetailsResponse.getCorpAlias(), false, propertyType, hotelRates.getCountryCode(), hotelRates.getMarkUpDetails(),
									roomDetailsResponse.getFoodRating(), false, siteDomain, selectedRoomCode, selectedRateplanCode, true, hotelRates,
									false, isHighSellingAltAcco);
							recommendedRoom.addAll(CollectionUtils.isNotEmpty(recommendedRoomClients) ? recommendedRoomClients : new ArrayList<>());
						}
					}
					if(CollectionUtils.isNotEmpty(recommendedRoom) && CollectionUtils.isNotEmpty(searchRoomsResponse.getRecommendedRooms()) && searchRoomsResponse.getRecommendedRooms().get(0)!=null) {
						searchRoomsResponse.getRecommendedRooms().get(0).setFilterDetails(null);
					}
					if(CollectionUtils.isEmpty(searchRoomsResponse.getRecommendedRooms())) {
						searchRoomsResponse.setRecommendedRooms(new ArrayList<>());
					}
					if(recommendedRoom!=null) {
						searchRoomsResponse.getRecommendedRooms().addAll(recommendedRoom);
					}
				}
			}

			if(CollectionUtils.isNotEmpty(searchRoomsResponse.getPackageRooms()) || packageRoomPresent) {
				setSuperPackageCard(searchRoomsResponse, askedCurrency);
			}

			if(Utility.isRegionGccOrKsa(siteDomain) && utility.isExperimentOn(commonModifierResponse.getExpDataMap(),"GBRP")
			&& searchRoomsRequest.getSearchCriteria().getSelectedRatePlan() != null){
				String selectedRpc = searchRoomsResponse.getExactRooms().get(0).getRatePlans().stream().filter(f -> f.getRpc().equalsIgnoreCase(searchRoomsRequest.getSearchCriteria().getSelectedRatePlan().getRatePlanCode())).findFirst().get().getRpc();
				searchRoomsRequest.getSearchCriteria().getSelectedRatePlan().setRatePlanCode(selectedRpc);
				searchRoomsResponse.setSelectedRatePlan(searchRoomsRequest.getSearchCriteria().getSelectedRatePlan());
			}
			/* Make Diff Types of Rooms */
			searchRoomsResponse.setAddons(commonResponseTransformer.getAddons(hotelRates.getAddOns()));
			boolean bnplNewVariant = false;
			boolean applyFilterToCombo = false;
			if (MapUtils.isNotEmpty(roomDetailsResponse.getExpData())) {
				bnplNewVariant = roomDetailsResponse.getExpData().containsKey(EXP_BNPL_NEW_VARIANT) && Boolean.parseBoolean(roomDetailsResponse.getExpData().get(EXP_BNPL_NEW_VARIANT));
				applyFilterToCombo = roomDetailsResponse.getExpData().containsKey(APPLY_FILTER_TO_COMBO.getKey()) && Boolean.parseBoolean(roomDetailsResponse.getExpData().get(APPLY_FILTER_TO_COMBO.getKey()));
			}
			BNPLVariant bnplVariant = hotelRates.getBnplVariant();
			searchRoomsResponse.setTrackingText(hotelRates.getTrackingText());
			boolean hideSpecificFilters = false;
			if (StringUtils.isNotEmpty(hotelRates.getTrackingText())  && hotelRates.getTrackingText().contains("FLXI_AVL")) {
				hideSpecificFilters = true;
			}
			searchRoomsResponse.setFilters(getFilters(searchRoomsResponse.getExactRooms(), searchRoomsResponse.getOccupancyRooms(), filterCriteria, requestDetails.getFunnelSource(), ap, isBlockPAH, bnplVariant, commonModifierResponse, isLuxeHotel, hotelRates.isNegotiatedRateFlag(), applyFilterToCombo, hideSpecificFilters, requestDetails.getPageContext()));
			searchRoomsResponse.setSearchRoomDeeplinkUrl(hotelRates.getSearchRoomDeeplinkUrl());
			searchRoomsResponse.setDetailDeeplinkUrl(hotelRates.getDetailDeeplinkUrl());
			searchRoomsResponse.setRecentDeepLink(hotelRates.getListingDeepLinkWithoutFilters()); //				set listingDeepLinkWithoutFilters as recentDeepLink to pass to the client
			searchRoomsResponse.setFeatureFlags(getFeatureFlags(hotelRates, searchRoomsResponse.getExactRooms(), (commonModifierResponse != null ? commonModifierResponse.getExpDataMap() : null)));
			searchRoomsResponse.setContextDetails(getContextDetails(hotelRates));
			buildEmiConfig(searchRoomsResponse);

			if (searchRoomsRequest != null && searchRoomsRequest.getSearchCriteria() != null && CollectionUtils.isNotEmpty(searchRoomsRequest.getSearchCriteria().getRoomStayCandidates())) {
				buildCouponCardConfig(searchRoomsResponse, searchRoomsRequest.getSearchCriteria().getRoomStayCandidates().get(0).getAdultCount());
			}

			//Blocking popup for Android and IOS devices SWAT-13518306
			if (!(Constants.FUNNEL_SOURCE_HOMESTAY.equalsIgnoreCase(requestDetails.getFunnelSource()) ||
					Constants.ANDROID.equalsIgnoreCase(client) || Constants.DEVICE_IOS.equalsIgnoreCase(client))) {
				searchRoomsResponse.setImpInfo(getImpInfo(searchRoomsResponse.getRecommendedCombos(), roomStayCandidates));
			}
			searchRoomsResponse.setPropertySellableType(buildPropertySellableType(roomDetailsResponse));
			if(SELLABLE_ROOM_TYPE.equalsIgnoreCase(searchRoomsResponse.getPropertySellableType())){
				searchRoomsResponse.setPropertySellableText(polyglotService.getTranslatedData(ROOM_SELLABLE_TYPE));
			}else{
				searchRoomsResponse.setPropertySellableText(polyglotService.getTranslatedData(STAY_SELLABLE_TYPE));
			}
			searchRoomsResponse.setSelectedRatePlanCode(hotelRates.getSelectedRatePlanCode());
			mappingSpaceIdToEachRoomCode(searchRoomsResponse, hotelRates.getSpaceIdToSleepingInfoArrMap());
			Pair<Boolean, Boolean> bedAndRoomPresent = new ImmutablePair<>(false,false);
			if(isOHSExpEnable){
				bedAndRoomPresent = addSellableLabelFromSellableType(searchRoomsResponse);
			}
			searchRoomsResponse.setRoomInfo(buildRoomInfo(hotelRates, searchRoomsResponse, roomStayCandidates, searchRoomsCriteria.getCountryCode(), isOHSExpEnable,bedAndRoomPresent,isNewDetailPageTrue, hotelsRoomInfoResponseEntity, isIHAAOrch, roomDetailsResponse.isServiceApartment(), expDataMap));
			searchRoomsResponse.setRtbPersuasionCard(!isRTBCTrue?buildRtbPersuasionCard(hotelRates):null);
			searchRoomsResponse.setPaymentCard(buildPaymentCard(requestDetails, hotelRates));
			if (CollectionUtils.isNotEmpty(hotelRates.getMandatoryCharges())) {
				//Mandatory charges are not showing for DH on detail page
				if (showMandatoryChargesDH) {
					searchRoomsResponse.setAdditionalFees(buildAdditionalFees(hotelRates, commonModifierResponse, expDataMap));
				} else if (StringUtils.isNotEmpty(hotelRates.getCountryCode())  && !DH_COUNTRY_CODE.equalsIgnoreCase(hotelRates.getCountryCode())) {
					searchRoomsResponse.setAdditionalFees(buildAdditionalFees(hotelRates, commonModifierResponse, expDataMap));
				}
			}

			if (hotelsRoomInfoResponseEntity != null && CollectionUtils.isNotEmpty(hotelsRoomInfoResponseEntity.getHtlRmInfo()) && hotelsRoomInfoResponseEntity.getHtlRmInfo().get(0).isSpaceDetailsRequired()) {
				if (commonModifierResponse != null && utility.isExperimentOn(commonModifierResponse.getExpDataMap(),EXP_PLV2)) {
					searchRoomsResponse.setSharedSpacesV2(getSpaceDataV2(hotelsRoomInfoResponseEntity.getHtlRmInfo().get(0).getSharedSpaces(), false));
				} else {
					searchRoomsResponse.setSharedSpaces(getSpaceData(hotelsRoomInfoResponseEntity.getHtlRmInfo().get(0).getSharedSpaces(), null, expData));
				}
			}
			if (!requestDetails.isLoggedIn()) {
				searchRoomsResponse.setLoginPersuasion(buildLoginPersuasion());
			}

			if (!Constants.CLIENT_DESKTOP.equalsIgnoreCase(client)) {
				searchRoomsResponse.setHotelDetails(buildHotelDetails(hotelRates, requestDetails.getFunnelSource()));
			}
			if (!DEVICE_OS_DESKTOP.equalsIgnoreCase(client) && !CORP_ID_CONTEXT.equalsIgnoreCase(requestDetails.getIdContext()))
				searchRoomsResponse.setBanner(buildBanner(searchRoomsResponse, hotelRates));
			if (commonModifierResponse != null && isOHSExpEnable) {
				sortBySellableType(searchRoomsResponse);
				updateSearchRoomsResponseFilters(searchRoomsResponse, bedAndRoomPresent);
			}
			// Set specialFareInfo node value as null if there are no instant fare rate plan available for the hotel in details page since there's no need to display delayed confirmation pop-up in this case.
			if (searchRoomsResponse.getSpecialFareInfo() != null && searchRoomsResponse.getSpecialFareInfo().getSubheader() != null
					&& searchRoomsResponse.getSpecialFareInfo().getSubheader().contains("{INSTANT_FARE}")) {
				searchRoomsResponse.setSpecialFareInfo(null);
			}
			// Setting Detail page persuasion for Flyer ,Bus ,train Deals
			if (CollectionUtils.isNotEmpty(roomDetailsResponse.getHotelRates())) {
				Optional<HotelRates> hotelRate = roomDetailsResponse.getHotelRates().stream().findFirst();
				if (hotelRate.isPresent() && hotelRate.get().isTrainExclusiveRateAvailable()) {
					PrimaryOffer primaryOffer = getPrimaryOfferForTrainDeal(hotelRates, client);
					searchRoomsResponse.setPrimaryOffer(primaryOffer);
				}

				if (hotelRate.isPresent() && hotelRate.get().isBusExclusiveRateAvailable()) {
					PrimaryOffer primaryOffer = getPrimaryOfferForBusDeal(hotelRates, client);
					searchRoomsResponse.setPrimaryOffer(primaryOffer);
				}

				//flow

				if (hotelRate.isPresent() && hotelRate.get().isExclusiveFlyerRateAvailable()) {
					if (utility.isMyPartner(commonModifierResponse)) {
						persuasionUtil.buildHotelPersuasionOfExclusiveDealForDetail(searchRoomsResponse);
					} else {
						PrimaryOffer primaryOffer = getPrimaryOfferForFlyerDeal(hotelRates, client);
						searchRoomsResponse.setPrimaryOffer(primaryOffer);
					}
				}
				searchRoomsResponse.setExtraBedPolicy(buildExtraBedPolicy(roomDetailsResponse.getHotelRates().get(0).getHouseRules()));
			}
			commonResponseTransformer.buildAltDatesPersuasionAndBottomsheet(roomDetailsResponse.getAlternatePriceCard(), searchRoomsRequest, expData, searchRoomsResponse, searchRoomsCriteria, los, isAltAccoHotel, isHighSellingAltAcco);
			if(!hasAltDatesPersuasion(searchRoomsResponse.getPersuasions())){
				searchRoomsResponse.setLongStayGCCNudgePersuasion(buildLongStayGCCNudgePersuasion(hotelRates,checkIn,checkOut));
			}
			//[HTL-49652] Changes for sale persuasion on detail page, it has higher priority then flyer persuasion
			if (utility.isB2CFunnel() && hotelRates.getCampaignPojo() != null) {
				PrimaryOffer primaryOffer = getPrimaryOfferForSaleCampaign(hotelRates.getCampaignPojo());
				searchRoomsResponse.setPrimaryOffer(primaryOffer);
			}

			if (searchRoomsResponse.getPrimaryOffer() == null) {
//				PrimaryOffer primaryOffer = getPrimaryOfferForNoCostEmi(hotelRates);
				// Supplier to shown only if flyer persuasion is not present!
				PrimaryOffer primaryOffer = getPrimaryOfferForSupplierDeals(hotelRates, askedCurrency);
				if (StringUtils.isNotEmpty(primaryOffer.getDescription())) {
					searchRoomsResponse.setPrimaryOffer(primaryOffer);
				}
			}

			if (roomDetailsResponse.isVistaraDealAvailable()) {
				PrimaryOffer primaryOffer = getPrimaryOfferForVistaraDeal(hotelRates, client);
				searchRoomsResponse.setPrimaryOffer(primaryOffer);
			}

			if(roomDetailsResponse.getLuckyUserContext() != null){
				searchRoomsResponse.setLuckyUserContext(LuckyUserContext.LUCKY.equals(roomDetailsResponse.getLuckyUserContext()) ? PRIVILEGED_USER : LuckyUserContext.LUCKY_UNLUCKY.equals(roomDetailsResponse.getLuckyUserContext()) ? CURSED_USER : UNFORTUNATE_USER);
			}

			if(hotelRates != null && (hotelRates.getMmtIhForexCashback() > 0.0 || hotelRates.getMmtIhCabCashback() > 0.0) && ancillaryVariant != 0){
				searchRoomsResponse.setDetailPersuasionCards(utility.buildIhCashbackCard(hotelRates,hydraSegments,ancillaryVariant));
			}

			String luckyUserContextWithExp = utility.logLuckyUserData(commonModifierResponse, searchRoomsResponse.getLuckyUserContext(), "search-rooms");
			searchRoomsResponse.setLuckyUserContext(luckyUserContextWithExp);
			searchRoomsResponse.setHotelCloudData(roomDetailsResponse.getHotelCloudData());
			if(Objects.nonNull(commonModifierResponse) && !utility.isBusinessIdentificationEnableFromUserService(commonModifierResponse.getExtendedUser()) && roomDetailsResponse.isBusinessIdentificationFeatureEnabledForNonRegisteredUser()
			&& roomDetailsResponse.isCityInBusinessIdentificationNonRegisteredCardEnabledLocations() && DOM_COUNTRY.equalsIgnoreCase(searchRoomsCriteria.getCountryCode()) && utility.isB2CFunnel()
			&& (MDC.get(MDCHelper.MDCKeys.CLIENT.getStringValue()).equalsIgnoreCase(Constants.CLIENT_ANDROID) || MDC.get(MDCHelper.MDCKeys.CLIENT.getStringValue()).equalsIgnoreCase(Constants.CLIENT_IOS))){
				searchRoomsResponse.setCardData(commonResponseTransformer.buildBusinessIdentificationCardsNonRegisteredUser());
			} else {
				searchRoomsResponse.setCardData(commonResponseTransformer.buildBusinessIdentificationCards(hotelRates, roomDetailsResponse.getAffiliateId()));
			}
			searchRoomsResponse.setCardsMap(mobConfigHelper.convertCardDataToCardInfoMap(roomDetailsResponse.getCardDataMap()));
			boolean isHighValueCall = commonModifierResponse != null && MapUtils.isNotEmpty(commonModifierResponse.getExpDataMap()) ? utility.isExperimentTrue(commonModifierResponse.getExpDataMap(), HVC) : false;
			if(isHighValueCall){
				String defaultPriceKey = "";
				int totalTicketValue = 0;
				if (searchRoomsResponse != null && CollectionUtils.isNotEmpty(searchRoomsResponse.getExactRooms())) {
                    if(searchRoomsResponse.getExactRooms().get(0)!=null && CollectionUtils.isNotEmpty(searchRoomsResponse.getExactRooms().get(0).getRatePlans())
							&& searchRoomsResponse.getExactRooms().get(0).getRatePlans().get(0) != null && CollectionUtils.isNotEmpty(searchRoomsResponse.getExactRooms().get(0).getRatePlans().get(0).getTariffs())
							&& searchRoomsResponse.getExactRooms().get(0).getRatePlans().get(0).getTariffs().get(0) != null
					){
						defaultPriceKey = searchRoomsResponse.getExactRooms().get(0).getRatePlans().get(0).getTariffs().get(0).getDefaultPriceKey();
						Map<String, TotalPricing> pricingMap = searchRoomsResponse.getExactRooms().get(0).getRatePlans().get(0).getTariffs().get(0).getPriceMap();
						totalTicketValue = calculateTotalTicketValue(defaultPriceKey,pricingMap);
					}
				} else if (searchRoomsResponse != null && CollectionUtils.isNotEmpty(searchRoomsResponse.getRecommendedCombos())) {
					if(searchRoomsResponse.getRecommendedCombos().get(0)!=null && searchRoomsResponse.getRecommendedCombos().get(0).getComboTariff()!=null){
						defaultPriceKey = searchRoomsResponse.getRecommendedCombos().get(0).getComboTariff().getDefaultPriceKey();
						Map<String, TotalPricing> pricingMap = searchRoomsResponse.getRecommendedCombos().get(0).getComboTariff().getPriceMap();
						totalTicketValue = calculateTotalTicketValue(defaultPriceKey,pricingMap);
					}
				}
				boolean aboApplicable = commonModifierResponse!=null && MapUtils.isNotEmpty(commonModifierResponse.getExpDataMap()) ? utility.isExperimentOn(commonModifierResponse.getExpDataMap(), ExperimentKeys.aboApplicable.name()) : false;
				searchRoomsResponse.setSupportDetails(utility.buildSupportDetails(totalTicketValue,PAGE_CONTEXT_DETAIL,
						utility.getHighValueCallFunnelType(hotelRates.getPropertyType(),requestDetails.getFunnelSource(),searchRoomsCriteria.getCountryCode()), aboApplicable));
				if(searchRoomsResponse.getSupportDetails()!=null){
					searchRoomsResponse.setRequestCallbackData(utility.buildRequestToCallBackDataForHighValue( PAGE_CONTEXT_DETAIL,"","", isCallToBookV2Applicable));
				}
				if(searchRoomsResponse.getSupportDetails()!=null){
					String hotelName = StringUtils.isNotEmpty(hotelRates.getName()) ? hotelRates.getName() : EMPTY_STRING;
					searchRoomsResponse.getSupportDetails().setFormUrl(buildFormUrlForDetail(searchRoomsRequest,searchRoomsResponse.getSupportDetails(),hotelRates.getPropertyType(),hotelName));
				}
			}
			if(isCallToBookV2Applicable && hotelRates.isShowCallToBook() && Utility.isGccOrKsa()){
				updateSupportDetails(searchRoomsResponse);
			}
			try {
				if (searchRoomsRequest != null && searchRoomsRequest.getSearchCriteria() != null && CORP_ID_CONTEXT.equalsIgnoreCase(requestDetails.getIdContext())) {
					Boolean guestHouseAvailable = searchRoomsRequest.getSearchCriteria().getGuestHouseAvailable();
					searchRoomsResponse.setGuestHouseAvailable(
							guestHouseAvailable != null ? guestHouseAvailable : roomDetailsResponse.getGuestHouseAvailable()
					);

					if ( (Boolean.TRUE.equals(guestHouseAvailable) && HOTEL.equalsIgnoreCase(searchRoomsRequest.getSearchCriteria().getHotelType()) )
							|| guestHouseAvailable == null || searchRoomsRequest.getSearchCriteria().getHotelType() == null) {
						GuestHouseResponse guestHouseResponse = buildGuestHouseResponse(roomDetailsResponse.getGuestHouseListingResponse(), null, null);
						searchRoomsResponse.setAvailableGuestHouse(guestHouseResponse);
					}
				}
			}
			catch (Exception e) {
				logger.error("Error while fetching available guest houses", e);
			}
		} finally {
			metricAspect.addToTimeInternalProcess(Constants.PROCESS_DETAIL_RESPONSE_PROCESS, DETAIL_SEARCH_ROOMS, System.currentTimeMillis() - startTime);
		}
		return searchRoomsResponse;
	}

	public PriceGraphInfo getPriceGraphInfo(PriceVariation priceVariationInfo, String askedCurrency, Map<String, String> experimentDataMap) {
		if(priceVariationInfo != null && priceVariationInfo.getPriceVariationType() != null && priceVariationInfo.getPriceVariationType() != UNAVAILABLE){
			PriceGraphInfo info = new PriceGraphInfo();
			info.setType(priceVariationInfo.getPriceVariationType().name());
			info.setBgGradient(utility.getBgLinearGradientForPriceVariationType(priceVariationInfo.getPriceVariationType()));
			if (utility.isPriceVariationV2Enabled(experimentDataMap)) {
				if (priceVariationInfo.getPriceVariationType() == DROP && CollectionUtils.isNotEmpty(priceVariationInfo.getAlternateDates())) {
					info.setType(PRICE_DROP_TYPE_ALTERNATE_DATES);
					info.setAlternateDates(buildAlternateDates(priceVariationInfo.getAlternateDates(), askedCurrency));
				}
				info.setIconUrl(priceGraphIcon);
				info.setIconTitle(polyglotService.getTranslatedData(PRICE_GRAPH_ICON_TITLE));
				info.setIconSubTitle(polyglotService.getTranslatedData(PRICE_GRAPH_ICON_SUBTITLE));
				info.setHeading(priceVariationInfo.getHeading());
				info.setDescription(priceVariationInfo.getDescription());
			} else {
				info.setIconUrl(priceGraphTextIcon);
				switch (priceVariationInfo.getPriceVariationType()){
					case DROP:{
						info.setHeading(getUpdatedPriceGraphText(PRICE_GRAPH_DROP_TITLE, priceVariationInfo, askedCurrency));
						info.setDescription(getUpdatedPriceGraphText(PRICE_GRAPH_DROP_SUBTITLE, priceVariationInfo, askedCurrency));
						break;
					}
					case SURGE:{
						info.setHeading(getUpdatedPriceGraphText(PRICE_GRAPH_SURGE_TITLE, priceVariationInfo, askedCurrency));
						info.setDescription(getUpdatedPriceGraphText(PRICE_GRAPH_SURGE_SUBTITLE, priceVariationInfo, askedCurrency));
						break;
					}
					default:{
						// TYPICAL
						info.setHeading(getUpdatedPriceGraphText(PRICE_GRAPH_TYPICAL_TITLE, priceVariationInfo, askedCurrency));
						info.setDescription(getUpdatedPriceGraphText(PRICE_GRAPH_TYPICAL_SUBTITLE, priceVariationInfo, askedCurrency));
						break;
					}
				}
			}
			return info;
		}
		return null;
	}

	private List<AlternateDate> buildAlternateDates(List<com.mmt.hotels.model.response.staticdata.AlternateDate> alternateDates, String askedCurrency) {
		List<AlternateDate> alternateDateList = new ArrayList<>();
		if (CollectionUtils.isNotEmpty(alternateDates)) {

			alternateDates.sort(Comparator.comparing(com.mmt.hotels.model.response.staticdata.AlternateDate::getCheckIn));

			for (com.mmt.hotels.model.response.staticdata.AlternateDate alternateDate : alternateDates) {
				AlternateDate date = new AlternateDate();
				date.setCheckIn(alternateDate.getCheckIn());
				date.setCheckOut(alternateDate.getCheckOut());
				date.setPrice(buildAternateDatesPrice(alternateDate.getPrice(), askedCurrency));
				date.setPriceColor(utility.getPriceColorForPriceDrop(alternateDate.getPriceVariationType()));
				if (alternateDate.isRecommended()) {
					date.setPriceTagUrl(priceGraphRecommendedIcon);
				} else {
					date.setPriceTagUrl(null);
				}
				alternateDateList.add(date);
			}
		}
		return alternateDateList;
	}

	private String buildAternateDatesPrice(int price, String askedCurrency) {
		String currencySymbol = Currency.getCurrencyEnum(StringUtils.isNotBlank(askedCurrency) ? askedCurrency : DEFAULT_CUR_INR).getCurrencySymbol();
		String priceStr = utility.convertNumericValueToCommaSeparatedString(price, Locale.ENGLISH);
		return currencySymbol + priceStr;
	}

	private String getUpdatedPriceGraphText(String priceGraphPolyglotKey, PriceVariation priceVariationInfo, String askedCurrency) {
		String currencySymbol = Currency.getCurrencyEnum(StringUtils.isNotBlank(askedCurrency) ? askedCurrency : DEFAULT_CUR_INR).getCurrencySymbol();
		String priceGraphStr = polyglotService.getTranslatedData(priceGraphPolyglotKey);
		priceGraphStr = priceGraphStr.replace("{currency}", currencySymbol);
		priceGraphStr = priceGraphStr.replace("{amount}", utility.convertNumericValueToCommaSeparatedString(priceVariationInfo.getAmount(), Locale.ENGLISH));
		priceGraphStr = priceGraphStr.replace("{percentage}", String.valueOf(priceVariationInfo.getPercentage()));
		priceGraphStr = priceGraphStr.replace("{duration}", String.valueOf(priceVariationInfo.getDurationDays()));
		return priceGraphStr;
	}

	private GuestHouseResponse buildGuestHouseResponse(GuestHouseListingResponse guestHouseListingResponse, String client, Map<String, String> expDataMap){
		if (guestHouseListingResponse == null || null == guestHouseListingResponse.getHotelSearchResponse() ||  ( guestHouseListingResponse.getResponseErrors() != null && CollectionUtils.isNotEmpty(guestHouseListingResponse.getResponseErrors().getErrorList()) )) {
			return null;
		}
		GuestHouseResponse guestHouseResponse = new GuestHouseResponse();
		guestHouseResponse.setCtaMap(guestHouseListingResponse.getCtaMap());
		guestHouseResponse.setTitle("Available Guest Houses");
		guestHouseResponse.setCtaText("VIEW PROPERTY DETAILS");
		guestHouseResponse.setSubtitle("Your organisation recommends selecting guest house whenever available.");
		if (null != guestHouseListingResponse.getHotelSearchResponse()) {
			if (CollectionUtils.isNotEmpty(guestHouseListingResponse.getHotelSearchResponse().getHotelList())) {
				guestHouseResponse.setPropertyList(searchHotelsFactory.getResponseService(client).buildPersonalizedHotels(guestHouseListingResponse.getHotelSearchResponse().getHotelList(),
						expDataMap, null, null, null, null, null, null));
			}
		}
		return guestHouseResponse;
	}

	private boolean hasAltDatesPersuasion(List<PersuasionObject> persuasions) {
		if (CollectionUtils.isEmpty(persuasions)) {
			return false;
		}
		return persuasions.stream()
				.filter(persuasionObject -> IMAGE_TEXT_H.equals(persuasionObject.getTemplate()) &&
						ALT_DATE_PERSUASION_PLACEHOLDER.equals(persuasionObject.getPlaceholder()))
				.flatMap(persuasionObject -> persuasionObject.getData().stream())
				.anyMatch(persuasionData -> "ALT_DATES".equals(persuasionData.getPersuasionType()));
	}


	private ExtraBedPolicy buildExtraBedPolicy(HouseRules houseRules) {
		ExtraBedPolicy extraBedPolicy = null;
		if (houseRules != null && CollectionUtils.isNotEmpty(houseRules.getCategoryInfoList())) {
			for (CategoryInfo categoryInfo : houseRules.getCategoryInfoList()) {
				if ("EXTRA_BED_POLICY".equals(categoryInfo.getId())) {
					RuleTableInfo ruleTableInfo = getParsedRuleTableInfo(categoryInfo.getRuleTableInfo());
					if (ruleTableInfo == null) {
						return null;
					}
					extraBedPolicy =  new ExtraBedPolicy();
					extraBedPolicy.setDesc(categoryInfo.getCategoryDesc());
					extraBedPolicy.setTitle(categoryInfo.getCategoryHeading());
					extraBedPolicy.setRules(categoryInfo.getRuleDesc());
					extraBedPolicy.setRuleTableInfo(ruleTableInfo);
				}
			}
		}
		return extraBedPolicy;
	}

	private int getRoomTypeCount(HotelRates hotelRates) {
		if (hotelRates == null) {
			return 0;
		}
		int roomTypeCount = 0;
		if (hotelRates.getRecommendedRoomTypeDetails() != null) {
			if (MapUtils.isNotEmpty(hotelRates.getRecommendedRoomTypeDetails().getRoomType())) {
				roomTypeCount = hotelRates.getRecommendedRoomTypeDetails().getRoomType().size();
			}
		} else if (hotelRates.getRoomTypeDetails() != null) {
			if (MapUtils.isNotEmpty(hotelRates.getRoomTypeDetails().getRoomType())) {
				roomTypeCount = hotelRates.getRoomTypeDetails().getRoomType().size();
			}
		}
		return roomTypeCount;
	}

	private void updateSupportDetails(SearchRoomsResponse searchRoomsResponse) {
		SupportDetails supportDetails = searchRoomsResponse.getSupportDetails() != null ? searchRoomsResponse.getSupportDetails() : new SupportDetails();
		supportDetails.setIconUrl(callToBookIconUrl);
		supportDetails.setTitle(callToBookTitle);
		List<String> options = CollectionUtils.isNotEmpty(supportDetails.getOptions()) ? supportDetails.getOptions() : new ArrayList<>();
		options.add(callToBookOption);
		supportDetails.setOptions(options);
		searchRoomsResponse.setSupportDetails(supportDetails);
	}

	private void buildEmiConfig(SearchRoomsResponse searchRoomsResponse) {
		if (isEmiPlanAvailableForExactRoom(searchRoomsResponse) || isEmiPlanAvailableForRecommendedRoom(searchRoomsResponse)) {
			EmiTagDetails emiTagDetails = new EmiTagDetails();
			emiTagDetails.setText("NO COST EMI");
			emiTagDetails.setBgGradient(noCostEmiIconConfig);
			Map<String, EmiTagDetails> emiTagDetailsMap = new HashMap<>();
			emiTagDetailsMap.put("NO_COST", emiTagDetails);

			EmiConfigDetails emiConfigDetails = new EmiConfigDetails();
			emiConfigDetails.setCtaText(polyglotService.getTranslatedData(ConstantsTranslation.VIEW_PLANS_CTA_TEXT));
			emiConfigDetails.setEmiTags(emiTagDetailsMap);
			searchRoomsResponse.setEmiConfig(emiConfigDetails);
		}
	}

	private void buildCouponCardConfig(SearchRoomsResponse searchRoomsResponse, Integer paxCount) {
		CouponCardConfig couponCardConfig = new CouponCardConfig();
		couponCardConfig.setHeader(polyglotService.getTranslatedData(ConstantsTranslation.DETAIL_COUPON_CARD_HEADER));
		couponCardConfig.setSubHeader(polyglotService.getTranslatedData(COUPON_CARD_CONFIG_SUBHEADING).replace("{paxCount}", String.valueOf(paxCount)));
		searchRoomsResponse.setCouponCardConfig(couponCardConfig);
	}

	private boolean isEmiPlanAvailableForRoom(List<RoomDetails> roomDetails) {
		return Optional.ofNullable(roomDetails)
				.filter(CollectionUtils::isNotEmpty)
				.map(roomList -> roomList.get(0))
				.map(RoomDetails::getRatePlans)
				.filter(CollectionUtils::isNotEmpty)
				.map(ratePlans -> ratePlans.get(0))
				.map(SelectRoomRatePlan::getTariffs)
				.filter(CollectionUtils::isNotEmpty)
				.map(tariffs -> tariffs.get(0))
				.map(Tariff::getEmiPlanDetail)
				.isPresent();
	}

	private boolean isEmiPlanAvailableForRecommendedRoom(SearchRoomsResponse searchRoomsResponse) {
		return Optional.ofNullable(searchRoomsResponse)
                .map(SearchRoomsResponse::getRecommendedCombos)
                .filter(CollectionUtils::isNotEmpty)
                .map(recommendedCombos -> recommendedCombos.get(0))
                .map(RecommendedCombo::getComboTariff)
				.flatMap(comboTariff -> Optional.ofNullable(comboTariff).map(Tariff::getEmiPlanDetail))
				.isPresent();
	}

	private boolean isEmiPlanAvailableForExactRoom(SearchRoomsResponse searchRoomsResponse) {
		return Optional.ofNullable(searchRoomsResponse)
				.map(SearchRoomsResponse::getExactRooms)
				.filter(CollectionUtils::isNotEmpty)
				.map(this::isEmiPlanAvailableForRoom)
				.orElse(false);
	}


//	private PrimaryOffer getPrimaryOfferForLastMinuteDeal(HotelRates hotelRates, String client) {
//		PrimaryOffer primaryOffer = null;
//		if (hotelRates != null && (ANDROID.equalsIgnoreCase(client) || DEVICE_IOS.equalsIgnoreCase(client))) {
//			primaryOffer = new PrimaryOffer();
//			String descriptiontext  = getLastMinuteDealDescriptionText(hotelRates.getLastMinuteDealExpiry());
//			primaryOffer.setDescription(descriptiontext);
//			primaryOffer.setType("saleCampaign");
//			primaryOffer.setIconUrl("https://promos.makemytrip.com/sale_images/limited_time_sale_2x.png");
//			primaryOffer.setExpiry(dateUtil.convertToTimestamp(hotelRates.getLastMinuteDealExpiry()));
//			Style lastMinuteDealStyle = new Style();
//			lastMinuteDealStyle.setBgColor("#E6FFF9");
//			primaryOffer.setStyle(lastMinuteDealStyle);
//		}
//		return primaryOffer;
//	}

//	private String getLastMinuteDealDescriptionText(String lastMinuteDealExpiry) {
//		String text = "Special discount available on this property, valid till {date}. Book now to save BIG!";
//		String date = dateUtil.getDateString(lastMinuteDealExpiry);
//		return text.replace("{date}", date);
//	}


	private AdditionalMandatoryCharges buildAdditionalFees(HotelRates hotelRates, CommonModifierResponse commonModifierResponse,
														   Map<String, String> expDataMap) {

		Map<String,RoomType> roomTypeMap = null;
		if (hotelRates.getRoomTypeDetails() != null && MapUtils.isNotEmpty(hotelRates.getRoomTypeDetails().getRoomType())) {
			roomTypeMap = hotelRates.getRoomTypeDetails().getRoomType();
		} else if (hotelRates.getRecommendedRoomTypeDetails() != null && MapUtils.isNotEmpty(hotelRates.getRecommendedRoomTypeDetails().getRoomType())) {
			roomTypeMap = hotelRates.getRecommendedRoomTypeDetails().getRoomType();
		}

		SupplierDetails supplierDetails = null;
		double conversionFactor = 1.0;
		String roomName = null;

		if (MapUtils.isNotEmpty(roomTypeMap)) {
			for (Map.Entry<String, RoomType> roomTypeEntry : roomTypeMap.entrySet()) {
				RoomType roomType = roomTypeEntry.getValue();
				if (MapUtils.isNotEmpty(roomType.getRatePlanList())) {
					for (Map.Entry<String, RatePlan> ratePlanEntry : roomType.getRatePlanList().entrySet()) {
						RatePlan ratePlan = ratePlanEntry.getValue();
						if (ratePlan.getSupplierDetails() != null && ratePlan.getDisplayFare().getConversionFactor() > 0) {
							supplierDetails = ratePlan.getSupplierDetails();
							conversionFactor = ratePlan.getDisplayFare().getConversionFactor();
							roomName = roomName == null ? roomType.getRoomTypeName() : roomName;
							break;
						}
					}
				}
			}
		}

		boolean showTransfersFeeTxt = false;
		if (commonModifierResponse != null && MapUtils.isNotEmpty(commonModifierResponse.getExpDataMap())
				&& commonModifierResponse.getExpDataMap().containsKey(Constants.TRANSFERS_FEE_TEXT_KEY)) {
			showTransfersFeeTxt = Constants.EXP_TRUE_VALUE.equalsIgnoreCase(commonModifierResponse.getExpDataMap().get(Constants.TRANSFERS_FEE_TEXT_KEY));
		}

		AdditionalChargesBO additionalChargesBO = new AdditionalChargesBO.Builder()
				.buildUserCurrency(hotelRates.getCurrencyCode())
				.buildHotelierCurrency(supplierDetails != null ? supplierDetails.getHotelierCurrencyCode() : "INR")
				.buildPropertyType(hotelRates.getPropertyType())
				.buildAdditionalFees(hotelRates.getMandatoryCharges())
				.buildConversionFactor(conversionFactor)
				.buildCityCode(hotelRates.getCityCode())
				.buildCountryCode(hotelRates.getCountryCode())
				.buildRoomName(roomName)
				.buildCityName(hotelRates.getCityName())
				.buildRecommendationFlow(hotelRates.getRecommendedRoomTypeDetails() != null)
				.build();
		return commonResponseTransformer.buildAdditionalCharges(
				additionalChargesBO, showTransfersFeeTxt, hotelRates.getListingType(),
				PAGE_CONTEXT_DETAIL, expDataMap);
	}

	private AdditionalMandatoryCharges buildAdditionalFeesForRatePlan(HotelRates hotelRates, RatePlan ratePlan, CommonModifierResponse commonModifierResponse,
																	  Map<String, String> expDataMap, String roomName) {

		SupplierDetails supplierDetails = null;
		double conversionFactor = 1.0;
		boolean showTransfersFeeTxt = false;
		if (commonModifierResponse != null && MapUtils.isNotEmpty(commonModifierResponse.getExpDataMap())
				&& commonModifierResponse.getExpDataMap().containsKey(Constants.TRANSFERS_FEE_TEXT_KEY)) {
			showTransfersFeeTxt = Constants.EXP_TRUE_VALUE.equalsIgnoreCase(commonModifierResponse.getExpDataMap().get(Constants.TRANSFERS_FEE_TEXT_KEY));
		}
		if (ratePlan.getSupplierDetails() != null && ratePlan.getDisplayFare().getConversionFactor() > 0) {
			supplierDetails = ratePlan.getSupplierDetails();
			conversionFactor = ratePlan.getDisplayFare().getConversionFactor();
		}
		AdditionalChargesBO additionalChargesBO = new AdditionalChargesBO.Builder()
				.buildUserCurrency(hotelRates.getCurrencyCode())
				.buildHotelierCurrency(supplierDetails != null ? supplierDetails.getHotelierCurrencyCode() : "INR")
				.buildPropertyType(hotelRates.getPropertyType())
				.buildAdditionalFees(ratePlan.getAdditionalFees())
				.buildConversionFactor(conversionFactor)
				.buildCityCode(hotelRates.getCityCode())
				.buildCountryCode(hotelRates.getCountryCode())
				.buildRoomName(roomName)
				.buildCityName(hotelRates.getCityName())
				.buildRecommendationFlow(hotelRates.getRecommendedRoomTypeDetails() != null)
				.build();
		return commonResponseTransformer.buildAdditionalCharges(
				additionalChargesBO, showTransfersFeeTxt, hotelRates.getListingType(),
				PAGE_CONTEXT_DETAIL, expDataMap);
	}


	private PrimaryOffer getPrimaryOfferForSaleCampaign(CampaignPojo campaignPojo) {
		if (campaignPojo == null || StringUtils.isAnyBlank(campaignPojo.getIconUrl(), campaignPojo.getDescription(), campaignPojo.getHeadingColor())) {
			return null;
		}
		PrimaryOffer primaryOffer = new PrimaryOffer();
		primaryOffer.setDescription(campaignPojo.getDescription());
		primaryOffer.setIconUrl(campaignPojo.getIconUrl());
		primaryOffer.setType(SALE_CAMPAIGN);

		Style style = new Style();
		style.setBgColor(campaignPojo.getHeadingColor());
		primaryOffer.setStyle(style);
		return primaryOffer;
	}

	private int calculateTotalTicketValue(String defaultPriceKey,Map<String, TotalPricing> pricingMap){
		int totalTicketValue = 0;
		for (Map.Entry<String, TotalPricing> entry : pricingMap.entrySet()) {
			String key = entry.getKey();
			TotalPricing totalPricing = entry.getValue();
			if(key.equals(defaultPriceKey)){
				totalTicketValue = utility.getTotalTicketValue(totalPricing,true);
			}
		}
        return totalTicketValue;
	}

	private String buildFormUrlForDetail(SearchRoomsRequest searchRoomsRequest, SupportDetails supportDetails, String propertyType,String hotelName) {
		String formUrl = null;
		try {
			if (searchRoomsRequest != null) {
				String checkin = searchRoomsRequest.getSearchCriteria() != null ? dateUtil.getDateFormatted(searchRoomsRequest.getSearchCriteria().getCheckIn(),DateUtil.YYYY_MM_DD,DDMMYYYY) : EMPTY_STRING;
				String checkout = searchRoomsRequest.getSearchCriteria() != null ? dateUtil.getDateFormatted(searchRoomsRequest.getSearchCriteria().getCheckOut(),DateUtil.YYYY_MM_DD,DDMMYYYY) : EMPTY_STRING;
				String city = searchRoomsRequest.getSearchCriteria() != null ? searchRoomsRequest.getSearchCriteria().getCityCode() : EMPTY_STRING;
				String country = searchRoomsRequest.getSearchCriteria() != null ? searchRoomsRequest.getSearchCriteria().getCountryCode() : "IN";
				String locusId = searchRoomsRequest.getSearchCriteria() != null ? searchRoomsRequest.getSearchCriteria().getLocationId() : EMPTY_STRING;
				String locusType = searchRoomsRequest.getSearchCriteria() != null ? searchRoomsRequest.getSearchCriteria().getLocationType() : TYPE_CITY;
				String rsc = searchRoomsRequest.getSearchCriteria() != null ? utility.buildRscValue(searchRoomsRequest.getSearchCriteria().getRoomStayCandidates()):EMPTY_STRING;
				String _uCurrency = searchRoomsRequest.getSearchCriteria() != null ? searchRoomsRequest.getSearchCriteria().getCurrency() : DEFAULT_CUR_INR;
				String appVersion = searchRoomsRequest.getDeviceDetails() != null ? searchRoomsRequest.getDeviceDetails().getAppVersion() : EMPTY_STRING;
				String deviceId = searchRoomsRequest.getDeviceDetails() != null ? searchRoomsRequest.getDeviceDetails().getDeviceId() : EMPTY_STRING;
				String bookingDevice = searchRoomsRequest.getDeviceDetails() != null ? searchRoomsRequest.getDeviceDetails().getBookingDevice() : EMPTY_STRING;
				String deviceType = searchRoomsRequest.getDeviceDetails() != null ? searchRoomsRequest.getDeviceDetails().getDeviceType() : EMPTY_STRING;
				String visitorId = searchRoomsRequest.getRequestDetails() != null ? searchRoomsRequest.getRequestDetails().getVisitorId() : EMPTY_STRING;
				String visitNumber = String.valueOf(searchRoomsRequest.getRequestDetails() != null ? searchRoomsRequest.getRequestDetails().getVisitNumber() : 1);
				String funnelSource = searchRoomsRequest.getRequestDetails() != null ? searchRoomsRequest.getRequestDetails().getFunnelSource() : EMPTY_STRING;
				String idContext = searchRoomsRequest.getRequestDetails() != null ? searchRoomsRequest.getRequestDetails().getIdContext() : EMPTY_STRING;
				String funnelName = searchRoomsRequest.getRequestDetails() != null ? searchRoomsRequest.getRequestDetails().getFunnelSource() : EMPTY_STRING;
				propertyType = StringUtils.isNotEmpty(propertyType) ? propertyType : HOTEL;
				hotelName = hotelName.replace(' ', '+');
				formUrl = MessageFormat.format(supportDetails.getFormUrl(), checkin, checkout, city, country, locusId, locusType, rsc, _uCurrency, appVersion, deviceId, bookingDevice, deviceType, visitorId, visitNumber, funnelSource, idContext, funnelName, propertyType,hotelName);
			}
		} catch (Exception e) {
			LOGGER.error("Exception occured while preparing formUrl for supportDetail for Detail Page : " + e.getMessage(), e);
		}
		return formUrl;
	}

	private void setSuperPackageCard(SearchRoomsResponse searchRoomsResponse, String askedCurrency) {
		com.mmt.hotels.clientgateway.response.rooms.RoomFilterCard roomFilterCard = new com.mmt.hotels.clientgateway.response.rooms.RoomFilterCard();
		roomFilterCard.setTitle(buildSuperPackageCardText(searchRoomsResponse, askedCurrency));
		roomFilterCard.setCtaText(polyglotService.getTranslatedData(ConstantsTranslation.VIEW_ALL_PACKAGES));
		roomFilterCard.setBgStyle(getBgStyle(Constants.FFFFFF, Constants.ffeaa7, Constants.DIAGONAL_START));
		roomFilterCard.setIconUrl(superPackageIconUrl);
		roomFilterCard.setFilterCode(new ArrayList<>());
		roomFilterCard.getFilterCode().add(PACKAGE_RATE);
		searchRoomsResponse.setRoomFilterCard(roomFilterCard);
	}

	private String buildSuperPackageCardText(SearchRoomsResponse searchRoomsResponse, String askedCurrency) {
		String superPackageCardText = polyglotService.getTranslatedData(ConstantsTranslation.ENJOY_PACKAGE_BENEFITS);
		String slashedPrice = "";
		String priceBenfits = "";
		if(searchRoomsResponse!=null && searchRoomsResponse.getPackageRooms()!=null && CollectionUtils.isNotEmpty(searchRoomsResponse.getPackageRooms()) && CollectionUtils.isNotEmpty(searchRoomsResponse.getPackageRooms().get(0).getRatePlans()) && searchRoomsResponse.getPackageRooms().get(0).getRatePlans().get(0) instanceof PackageSelectRoomRatePlan) {
			PackageSelectRoomRatePlan packageSelectRoomRatePlan = (PackageSelectRoomRatePlan) searchRoomsResponse.getPackageRooms().get(0).getRatePlans().get(0);
			if(packageSelectRoomRatePlan!=null && packageSelectRoomRatePlan.getPackageInclusionDetails()!=null) {
				slashedPrice = String.valueOf((int)Double.parseDouble(packageSelectRoomRatePlan.getPackageInclusionDetails().getPackageBenefitsSlashedPrice()));
				priceBenfits = String.valueOf((int)Double.parseDouble(packageSelectRoomRatePlan.getPackageInclusionDetails().getPackageBenefitsPrice()));
			}
		}
		if(searchRoomsResponse!=null && CollectionUtils.isNotEmpty(searchRoomsResponse.getPackageRooms()) && CollectionUtils.isNotEmpty(searchRoomsResponse.getPackageRooms().get(0).getRatePlans()) && CollectionUtils.isNotEmpty(searchRoomsResponse.getPackageRooms().get(0).getRatePlans().get(0).getInclusionsList())) {
			List<BookedInclusion> inclusionList = searchRoomsResponse.getPackageRooms().get(0).getRatePlans().get(0).getInclusionsList();
			for(BookedInclusion inclusion:inclusionList) {
				if(StringUtils.isEmpty(inclusion.getType()) && StringUtils.isNotEmpty(slashedPrice) && StringUtils.isNotEmpty(priceBenfits)) {
					String currencySymbol = Currency.getCurrencyEnum(StringUtils.isNotBlank(askedCurrency) ? askedCurrency : DEFAULT_CUR_INR).getCurrencySymbol();
					superPackageCardText = polyglotService.getTranslatedData(SUPER_PACKAGE_CARD_TEXT);
					superPackageCardText = superPackageCardText.replace("{incText}", inclusion.getCode());
					superPackageCardText = superPackageCardText.replace("{1}", slashedPrice);
					superPackageCardText = superPackageCardText.replace("{2}", priceBenfits);
					superPackageCardText = superPackageCardText.replace("{cur}", currencySymbol);
					break;
				}
			}
		}
		return superPackageCardText;
	}

	private void updateSearchRoomsResponseFilters(SearchRoomsResponse searchRoomsResponse, Pair<Boolean, Boolean> bedAndRoomPresent) {
		if(bedAndRoomPresent.getKey() && bedAndRoomPresent.getValue()){
			// It means both sellableType "bed" & "room" is Present then create filter for private rooms only
			RatePlanFilter privateRooms = new RatePlanFilter(polyglotService.getTranslatedData(ROOMS_SELLABLE_LABEL), ROOMS_SELLABLE_TYPE_FILTER_CODE,null,false);
			if(CollectionUtils.isEmpty(searchRoomsResponse.getFilters())){
				searchRoomsResponse.setFilters(new ArrayList<>());
			}
			searchRoomsResponse.getFilters().add(0,privateRooms);
		}
	}

	private void mappingSpaceIdToEachRoomCode(SearchRoomsResponse searchRoomsResponse, LinkedHashMap<String, List<SleepingInfoArrangement>> spaceIdToSleepingInfoArrMap) {
		if (CollectionUtils.isNotEmpty(searchRoomsResponse.getExactRooms())) {
			List<RoomDetails> roomDetailsList = searchRoomsResponse.getExactRooms();
			if (CollectionUtils.isNotEmpty(roomDetailsList)) {
				utility.addSleepingInfoArrangementIntoRoomDetails(roomDetailsList, spaceIdToSleepingInfoArrMap);
			}
		}
		if (CollectionUtils.isNotEmpty(searchRoomsResponse.getRecommendedCombos())) {
			List<RoomDetails> roomDetailsList = searchRoomsResponse.getRecommendedCombos().get(0).getRooms();
			if (CollectionUtils.isNotEmpty(roomDetailsList)) {
				utility.addSleepingInfoArrangementIntoRoomDetails(roomDetailsList, spaceIdToSleepingInfoArrMap);
			}
		}
	}

	private PrimaryOffer getPrimaryOfferForVistaraDeal(HotelRates hotelRates, String client) {
		PrimaryOffer primaryOffer = null;
		Style style = null;
		if (hotelRates != null) {
			primaryOffer = new PrimaryOffer();
			style = new Style();
			primaryOffer.setDescription(polyglotService.getTranslatedData(VISTARA_DESCRIPTION));
			if (DEVICE_OS_DESKTOP.equalsIgnoreCase(client)) {
				primaryOffer.setIconUrl(vistaraPersuasionImageUrlDetail);
			} else {
				primaryOffer.setIconUrl(vistaraPersuasionImageUrlDetailDT);
			}
			primaryOffer.setType(VISTARA_TEXT);
			style.setBgColor(vistaraPersuasionColorDetail);
			primaryOffer.setStyle(style);
		}
		return primaryOffer;
	}

	// Primary offer for Detail page -> Flyer Deals
	private PrimaryOffer getPrimaryOfferForFlyerDeal(HotelRates hotelRates, String client) {
		String region = MDC.get(MDCHelper.MDCKeys.REGION.getStringValue());
		String countryCode = hotelRates != null ? hotelRates.getCountryCode() : MDC.get(MDCHelper.MDCKeys.COUNTRY.getStringValue());
		PrimaryOffer primaryOffer = null;
		Style style = null;
		if (hotelRates != null) {
			primaryOffer = new PrimaryOffer();
			style = new Style();
			if(Utility.isRegionGccOrKsa(region)){
				primaryOffer.setDescription(polyglotService.getTranslatedData(FLYER_DESCRIPTION));
				primaryOffer.setIconUrl(flyerGccPersuasionImageUrlDetail);
				primaryOffer.setType(FLYER_TEXT);
				style.setBgColor(flyerPersuasionColorDetail);
				primaryOffer.setStyle(style);
			} else if(DOM_COUNTRY.equalsIgnoreCase(countryCode)){
				primaryOffer.setDescription(polyglotService.getTranslatedData(FLYER_DESCRIPTION));
				if (DEVICE_OS_DESKTOP.equalsIgnoreCase(client)) {
					primaryOffer.setIconUrl(flyerPersuasionImageUrlDetailDT);
				} else {
					primaryOffer.setIconUrl(flyerPersuasionImageUrlDetail);
				}
				primaryOffer.setType(FLYER_TEXT);
				style.setBgColor(flyerPersuasionColorDetail);
				primaryOffer.setStyle(style);
			}
		}
		return primaryOffer;
	}

	// Primary offer for Detail page -> Bus Deals
	private PrimaryOffer getPrimaryOfferForBusDeal(HotelRates hotelRates, String client) {
		PrimaryOffer primaryOffer = null;
		Style style = null;
		if (hotelRates != null) {
			primaryOffer = new PrimaryOffer();
			style = new Style();
			primaryOffer.setDescription(polyglotService.getTranslatedData(BUS_DESCRIPTION));
			if (DEVICE_OS_DESKTOP.equalsIgnoreCase(client)) {
				primaryOffer.setIconUrl(busPersuasionImageUrlDetailDT);
			} else {
				primaryOffer.setIconUrl(busPersuasionImageUrlDetail);
			}
			primaryOffer.setType(BUS_DEAL_TEXT);
			style.setBgColor(busPersuasionColorDetail);
			primaryOffer.setStyle(style);
		}
		return primaryOffer;
	}

	// Primary offer for Detail page -> Train Deals
	private PrimaryOffer getPrimaryOfferForTrainDeal(HotelRates hotelRates, String client) {
		PrimaryOffer primaryOffer = null;
		Style style = null;
		if (hotelRates != null) {
			primaryOffer = new PrimaryOffer();
			style = new Style();
			primaryOffer.setDescription(polyglotService.getTranslatedData(TRAIN_DESCRIPTION));
			if (DEVICE_OS_DESKTOP.equalsIgnoreCase(client)) {
				primaryOffer.setIconUrl(trainPersuasionImageUrlDetailDT);
			} else {
				primaryOffer.setIconUrl(trainPersuasionImageUrlDetail);
			}
			primaryOffer.setType(TRAIN_TEXT);
			style.setBgColor(trainPersuasionColorDetail);
			primaryOffer.setStyle(style);
		}
		return primaryOffer;
	}

	/**
	 * Build day use response for following nodes:
	 * Header price details,
	 * set room list,
	 * set room plan list,
	 * set available as well as missing slot details,
	 * set day use persuasions.
	 */
	public DayUseRoomsResponse convertSearchSlotsResponse(RoomDetailsResponse roomDetailsResponse, HotelsRoomInfoResponseEntity hotelsRoomInfoResponseEntity,
														  HotelImage hotelImage, DayUseRoomsRequest dayUseRoomsRequest, CommonModifierResponse commonModifierResponse) {
		long startTime = System.currentTimeMillis();
		DayUseRoomsResponse dayUseRoomsResponse = new DayUseRoomsResponse();
		try {
			if (CollectionUtils.isEmpty(roomDetailsResponse.getHotelRates())) {
				return dayUseRoomsResponse;
			}
			String expData = dayUseRoomsRequest.getExpData();
			SearchRoomsCriteria searchSlotCriteria = dayUseRoomsRequest.getSearchCriteria();
			com.mmt.hotels.clientgateway.request.FeatureFlags featureFlags = dayUseRoomsRequest.getFeatureFlags();
			RequestDetails requestDetails = dayUseRoomsRequest.getRequestDetails();

			String askedCurrency = (searchSlotCriteria != null && StringUtils.isNotBlank(searchSlotCriteria.getCurrency())) ? searchSlotCriteria.getCurrency() : Constants.DEFAULT_CUR_INR;
			String checkIn = searchSlotCriteria != null ? searchSlotCriteria.getCheckIn() : String.valueOf(0);
			String checkOut = searchSlotCriteria != null ? searchSlotCriteria.getCheckOut() : String.valueOf(0);
			int los = dateUtil.getDaysDiff(LocalDate.parse(checkIn), LocalDate.parse(checkOut));
			int ap = dateUtil.getDaysDiff(LocalDate.now(), LocalDate.parse(checkIn));
			boolean isBlockPAH = roomDetailsResponse.getExpData() != null && roomDetailsResponse.getExpData().containsKey("blockPAH")
					&& StringUtils.isNotBlank(roomDetailsResponse.getExpData().get("blockPAH")) && Boolean.parseBoolean(roomDetailsResponse.getExpData().get("blockPAH"));

			HotelRates hotelRates = roomDetailsResponse.getHotelRates().get(0);
			boolean isLuxeHotel = utility.isLuxeHotel(hotelRates.getCategories());
			boolean isAltAccoHotel = hotelRates.isAltAcco();
			String propertyType = hotelRates.getPropertyTypeMerged()!=null?hotelRates.getPropertyTypeMerged():hotelRates.getPropertyType();

			Map<String, String> ratePlanCodeAndNameMap = new HashMap<>();

			//Set price details at header
			if(!(hotelRates.getRoomTypeDetails() != null && hotelRates.getOccupencyLessRoomTypeDetails() == null)){
				dayUseRoomsResponse.setSearchType("R");
				dayUseRoomsResponse.setDefaultPriceKey("DEFAULT");
				//search slot lowest rate plan test for 1 night block.
				if(hotelRates.getLowestRate() != null && !hotelRates.getLowestRate().isSlotRate()) {
					//commonResponseTransformer.getPriceDetail(totalPricing, dayUseRoomsResponse, null, true);
					if(hotelRates.getLowestRate().getAvailDetails() != null && hotelRates.getLowestRate().getAvailDetails().getOccupancyDetails() != null) {
						dayUseRoomsResponse.setOccupancyDetails(buildOccupencyDetails(hotelRates.getLowestRate().getAvailDetails().getOccupancyDetails()));
						dayUseRoomsResponse.setAvailCount(hotelRates.getLowestRate().getAvailDetails().getCount());
					}
					dayUseRoomsResponse.setPriceDetail( new DayUsePriceDetail());
					dayUseRoomsResponse.getPriceDetail().setPriceDisplayMsg(polyglotService.getTranslatedData(DAYUSE_PER_NIGHT));
					dayUseRoomsResponse.getPriceDetail().setTotalPrice(hotelRates.getLowestRate().getDiscountedLowestRate());
					dayUseRoomsResponse.getPriceDetail().setTotalTax(hotelRates.getLowestRate().getTax() != null ? hotelRates.getLowestRate().getTax():0);
					if(hotelRates.getLowestRate().getTax() != null && hotelRates.getLowestRate().getTax() > 0){
						String translatedText = polyglotService.getTranslatedData(DAYUSE_PER_NIGHT_TAX);
						translatedText = MessageFormat.format(translatedText, hotelRates.getLowestRate().getTax());
						dayUseRoomsResponse.getPriceDetail().setPriceTaxMsg(translatedText);
					}
				}
			}

			//Set Rooms, Rate Plans and Slot
			if(CollectionUtils.isNotEmpty(roomDetailsResponse.getDayUseRecommendedRoomTypeDetails())){
				for(RoomTypeDetails roomTypeDetails : roomDetailsResponse.getDayUseRecommendedRoomTypeDetails()){
					getDayUseRoom(roomTypeDetails, hotelsRoomInfoResponseEntity, hotelImage, dayUseRoomsResponse,
							hotelRates.getListingType(), expData, false, askedCurrency,
							requestDetails.getFunnelSource(), hotelRates.getWalletSurge(), hotelRates.getSegments(),
							los, hotelRates.getStarRating(), ap, isBlockPAH, hotelRates.getRoomPersuasions(),
							commonModifierResponse, false, ratePlanCodeAndNameMap, isLuxeHotel,
							isAltAccoHotel , dayUseRoomsRequest, hotelRates.isUserGCCAndMmtExclusive(),
							propertyType, searchSlotCriteria.getCountryCode(),
							hotelRates.getMarkUpDetails(),roomDetailsResponse.getFoodRating(), hotelRates, hotelRates.isHighSellingAltAcco());
				}
			}

			//Populate missing slots
			if(dayUseRoomsRequest.getSearchCriteria() != null && dayUseRoomsRequest.getSearchCriteria().getSlot() != null)
				populateMissingSlots(dayUseRoomsResponse, dayUseRoomsRequest.getSearchCriteria().getSlot().getTimeSlot());

			//Set context details
			dayUseRoomsResponse.setContextDetails(getContextDetails(hotelRates));

			//Set feature flags
			dayUseRoomsResponse.setFeatureFlags(getFeatureFlags(hotelRates, null, null));
			dayUseRoomsResponse.getFeatureFlags().setDayUsePersuasion(featureFlags != null && featureFlags.isDayUsePersuasion());

			//Set pokus variant for omniture tracking
			dayUseRoomsResponse.setExpVariantKeys(StringUtils.isNotBlank(dayUseRoomsRequest.getExpVariantKeys()) ? dayUseRoomsRequest.getExpVariantKeys() : null);

			//Set day use persuasions
			dayUseRoomsResponse.setDayUsePersuasions(buildDayUsePersuasion(roomDetailsResponse.getDayUsePersuasions(), featureFlags, dayUseRoomsRequest.getRequestDetails()));
			dayUseRoomsResponse.setCurrency(dayUseRoomsRequest.getSearchCriteria().getCurrency());

		} finally {
			metricAspect.addToTimeInternalProcess(Constants.PROCESS_DETAIL_RESPONSE_PROCESS, DETAIL_SEARCH_SLOTS, System.currentTimeMillis() - startTime);
		}
		return dayUseRoomsResponse;
	}

	private RoomTariff buildOccupencyDetails(OccupancyDetails occupancyDetails) {
		RoomTariff roomTariff = null;
		if(occupancyDetails != null){
			roomTariff = new RoomTariff();
			roomTariff.setRoomCount(occupancyDetails.getBedRoomCount());
			roomTariff.setNumberOfAdults(occupancyDetails.getAdult());
			roomTariff.setNumberOfChildren(occupancyDetails.getChild());
		}
		return roomTariff;
	}

	private void populateMissingSlots(DayUseRoomsResponse dayUseRoomsResponse, Integer slotTime) {
		if(dayUseRoomsResponse == null || CollectionUtils.isEmpty(dayUseRoomsResponse.getSlotPlans())){
			return ;
		}
		List<DayUseSlotPlan> dayUseSlotPlanList = dayUseRoomsResponse.getSlotPlans();
		Set<Integer> slotDetailCount = new HashSet<>();
		for(DayUseSlotPlan dayUseSlotPlan : dayUseSlotPlanList){
			if(dayUseSlotPlan.getSlot() != null) {
				slotDetailCount.add(dayUseSlotPlan.getSlot().getDuration());
			}
		}

		if(!slotDetailCount.isEmpty() && slotDetailCount.size() < 3) {
			DayUseSlotPlan dayUseSlotPlan = null;
			com.mmt.hotels.clientgateway.response.dayuse.Slot slot = null;
			Iterator<Integer> itr = missingSlotDetails.getDuration().iterator();
			while(itr.hasNext()) {
				Integer value = itr.next();
				if(!slotDetailCount.contains(value)){
					dayUseSlotPlan = new DayUseSlotPlan();
					slot = new com.mmt.hotels.clientgateway.response.dayuse.Slot();
					slot.setDuration(value);
					com.mmt.hotels.model.response.dayuse.Slot tempSlot = new com.mmt.hotels.model.response.dayuse.Slot();
					tempSlot.setDuration(slot.getDuration());
					tempSlot.setTimeSlot(String.valueOf(slotTime));
					slot.setTimeSlot(utility.calculateTimeSlot_Meridiem(tempSlot));
					dayUseSlotPlan.setSlot(slot);
					dayUseSlotPlanList.add(dayUseSlotPlan);
				}
			}
			dayUseRoomsResponse.setSlotPlans(dayUseSlotPlanList);
		}
	}

	private void getDayUseRoom(RoomTypeDetails roomTypeDetails, HotelsRoomInfoResponseEntity hotelsRoomInfoResponseEntity,
							   HotelImage hotelImage, DayUseRoomsResponse dayUseRoomsResponse, String listingType,
							   String expData, boolean ratePlanGroup, String askedCurrency, String funnelSource,
							   WalletSurge walletSurge, Segments segments, int days, Integer hotelStarRating, int ap,
							   boolean isBlockPAH, Map<String, JsonNode> roomPersuasionMap, CommonModifierResponse commonModifierResponse,
							   boolean isPackageRoom, Map<String, String> ratePlanCodeAndNameMap, boolean isLuxeHotel,
							   boolean isAltAccoHotel, DayUseRoomsRequest dayUseRoomsRequest, boolean userGCCAndMmtExclusive,
							   String propertyType, String countryCode, final MarkUpDetails markUpDetails,
							   Double foodRating, HotelRates hotelRates, boolean isHighSellingAltAcco) {
		if(roomTypeDetails == null)
			return;

		Map<String, List<String>> roomImageMap = new HashMap<>();
		if (hotelImage != null && hotelImage.getImageDetails() != null && MapUtils.isNotEmpty(hotelImage.getImageDetails().getProfessional())
				&& hotelImage.getImageDetails().getProfessional().containsKey("R")) {
			List<ProfessionalImageEntity> images = hotelImage.getImageDetails().getProfessional().get("R");
			if (CollectionUtils.isNotEmpty(images)) {
				images.forEach( image -> {
					if (StringUtils.isNotBlank(image.getUrl())) {
						roomImageMap.computeIfAbsent(image.getCatCode(), k -> new ArrayList<>());
						roomImageMap.get(image.getCatCode()).add(image.getUrl().startsWith("http") ? image.getUrl() : "https:" + image.getUrl());
					}
				});
			}
		}

		Map<String, RoomInfo> staticRoomInfoMap = new HashMap<>();
		Map<String, DayUseRoom> roomCodeAndRoomMap = new HashMap<>();
		String siteDomain = dayUseRoomsRequest.getRequestDetails() != null ? dayUseRoomsRequest.getRequestDetails().getSiteDomain() : StringUtils.EMPTY;
		for (String roomCode : roomTypeDetails.getRoomType().keySet()) {
			ExtraGuestDetail extraGuestDetail = null;
			String sellableType = null;
			List<SelectRoomRatePlan> roomRatePlanList = null;
			DayUseRoom dayUseRoom = null;
			if(  MapUtils.isNotEmpty(roomTypeDetails.getRoomType().get(roomCode).getRatePlanList())) {
				RatePlan ratePlan = roomTypeDetails.getRoomType().get(roomCode).getRatePlanList().values().stream().findFirst().orElse(null);
				if(ratePlan!=null && ratePlan.getExtraGuestDetail()!=null){
					extraGuestDetail = ratePlan.getExtraGuestDetail();
				}
			}
			if (null!=hotelsRoomInfoResponseEntity && hotelsRoomInfoResponseEntity.getHtlRmInfo()!=null && CollectionUtils.isNotEmpty(hotelsRoomInfoResponseEntity.getHtlRmInfo()))
				staticRoomInfoMap = hotelsRoomInfoResponseEntity.getHtlRmInfo().get(0).getHotelRoomInfo();
			RoomInfo roomInfo = MapUtils.isNotEmpty(staticRoomInfoMap) ? staticRoomInfoMap.get(roomCode) : null;
			List<FacilityGroup> roomAmenities = null;
			if(roomInfo!=null){
				roomAmenities = commonResponseTransformer.buildAmenities(roomInfo.getFacilityWithGrp(), roomInfo.getStarFacilities(), roomInfo.getHighlightedFacilities(), false);
			}
			//Set Room List
			dayUseRoom = setDayUseRoomList(roomTypeDetails.getRoomType().get(roomCode), roomImageMap, roomCode, roomInfo, extraGuestDetail, isAltAccoHotel,(commonModifierResponse != null && utility.isOHSExpEnable(propertyType, commonModifierResponse.getExpDataMap())),roomAmenities, countryCode,expData);

			if (roomInfo != null &&  null != roomInfo.getRoomAttributes())
				sellableType = roomInfo.getRoomAttributes().getSellableType();
			if(FUNNEL_DAYUSE.equalsIgnoreCase(funnelSource) && roomTypeDetails.getTotalDisplayFare() != null &&
					roomTypeDetails.getTotalDisplayFare().getDisplayPriceBreakDown() != null &&
					roomTypeDetails.getTotalDisplayFare().getDisplayPriceBreakDown().getCouponInfo() != null &&
					MapUtils.isNotEmpty(roomTypeDetails.getRoomType().get(roomCode).getRatePlanList()) &&
					roomTypeDetails.getRoomType().get(roomCode).getRatePlanList().entrySet().iterator().next().getValue().getDisplayFare() != null &&
					roomTypeDetails.getRoomType().get(roomCode).getRatePlanList().entrySet().iterator().next().getValue().getDisplayFare().getDisplayPriceBreakDown() != null)
			{
				roomTypeDetails.getRoomType().get(roomCode).getRatePlanList().entrySet().iterator().next().getValue().getDisplayFare().getDisplayPriceBreakDown().setCouponInfo(roomTypeDetails.getTotalDisplayFare().getDisplayPriceBreakDown().getCouponInfo());
			}
			roomRatePlanList = getRatePlans(roomTypeDetails.getRoomType().get(roomCode), listingType,
					expData, ratePlanGroup, askedCurrency, sellableType,funnelSource,
					days, ap , isBlockPAH,commonModifierResponse, isPackageRoom, ratePlanCodeAndNameMap, isLuxeHotel,
					userGCCAndMmtExclusive, false, isAltAccoHotel, null, null,
					markUpDetails, foodRating, false, siteDomain, false, hotelRates, false, isHighSellingAltAcco);

			// Populate unique room based on roomCode in roomCodeAndRoomMap
			if (dayUseRoom != null) {
				roomCodeAndRoomMap.putIfAbsent(dayUseRoom.getRoomCode(), dayUseRoom);
			}

			//Set Slot plans and populate occupancyDetails on unique room after getting it from roomCodeAndRoomMap
			setDayUseSlotPlanList(roomRatePlanList, dayUseRoomsResponse, roomTypeDetails, roomCode, roomCodeAndRoomMap.get(roomCode));

			double slashedPrice = dayUseRoomsResponse.getPriceDetail() != null ? dayUseRoomsResponse.getPriceDetail().getTotalPrice() : 0.0;

			if (!dayUseUtil.shouldSetPriceDetailsForDayUseOnDetailPage(dayUseRoomsResponse.getSlotPlans(), slashedPrice)) {
				dayUseRoomsResponse.setPriceDetail(null);
			}

			if (!dayUseUtil.isXPercentRulePassedOnDetailPage(dayUseRoomsResponse.getSlotPlans(), slashedPrice, thresholdForSlashedAndDefaultHourPrice)) {
				dayUseRoomsResponse.setPriceDetail(null);
			}

			//TODO get(0) or ALL to set roomplans
			if(CollectionUtils.isEmpty(dayUseRoomsResponse.getRatePlans()) && CollectionUtils.isNotEmpty(roomRatePlanList) && roomRatePlanList.get(0) != null){
				dayUseRoomsResponse.setRatePlans(new ArrayList<>());
				dayUseRoomsResponse.getRatePlans().add(roomRatePlanList.get(0));
			}
			else if(CollectionUtils.isNotEmpty(roomRatePlanList) && roomRatePlanList.get(0) != null){
				dayUseRoomsResponse.getRatePlans().add(roomRatePlanList.get(0));
			}
		}

		//Set Room List
		if (MapUtils.isNotEmpty(roomCodeAndRoomMap) && CollectionUtils.isNotEmpty(roomCodeAndRoomMap.values())) {
			dayUseRoomsResponse.setRooms(new ArrayList<>(roomCodeAndRoomMap.values()));
		}

	}

	private DayUseRoom setDayUseRoomList(RoomType roomType, Map<String, List<String>> roomImageMap, String roomCode, RoomInfo roomInfo, ExtraGuestDetail extraGuestDetail, boolean isAltAccoHotel, boolean isOHSExpEnable, List<FacilityGroup> roomAmenities, String countryCode, String expData) {
		if(roomType == null || roomInfo == null){
			return null;
		}
		DayUseRoom dayUseRoom = new DayUseRoom();
		dayUseRoom.setRoomCode(roomType.getRoomTypeCode());
		dayUseRoom.setRoomName(roomType.getRoomTypeName());
		// hardcoded amendRoomHighlights to false as this is not required for dayuse funnel
		dayUseRoom.setAmenities(commonResponseTransformer.buildAmenities(roomInfo.getFacilityWithGrp(), roomInfo.getStarFacilities(), roomInfo.getHighlightedFacilities(), false));
		boolean  pilgrimageBedInfoEnable = utility.isExperimentOn(utility.getExpDataMap(expData), ExperimentKeys.PILGR_IMAGE_BED_INFO.getKey());
		dayUseRoom.setRoomHighlights(getRoomHighlights(roomInfo, extraGuestDetail, isAltAccoHotel,isOHSExpEnable,roomAmenities, countryCode, false,pilgrimageBedInfoEnable));
		dayUseRoom.setImages(MapUtils.isNotEmpty(roomImageMap) ? roomImageMap.get(roomCode) : null);
		return dayUseRoom;
	}

	private void setDayUseSlotPlanList(List<SelectRoomRatePlan> tempRoomRates, DayUseRoomsResponse dayUseRoomsResponse, RoomTypeDetails roomTypeDetails, String roomCode, DayUseRoom dayUseRoom) {
		List<DayUseSlotPlan> dayUseSlotPlanList = new ArrayList<>();
		String priceKey = null;
		if(CollectionUtils.isNotEmpty(dayUseRoomsResponse.getSlotPlans()))
			dayUseSlotPlanList.addAll(dayUseRoomsResponse.getSlotPlans());
		if(CollectionUtils.isNotEmpty(tempRoomRates) && tempRoomRates.get(0) != null){
			//Set Slot
			DayUseSlotPlan slotPlan = new DayUseSlotPlan();
			if(tempRoomRates.get(0).getPayMode() != null){
				slotPlan.setPayMode(tempRoomRates.get(0).getPayMode());
			}
			if(CollectionUtils.isNotEmpty(tempRoomRates.get(0).getTariffs()) && tempRoomRates.get(0).getTariffs().get(0) != null){
				if(tempRoomRates.get(0).getTariffs().get(0).getDefaultPriceKey() != null){
					slotPlan.setDefaultPriceKey(tempRoomRates.get(0).getTariffs().get(0).getDefaultPriceKey());
				}
				if(MapUtils.isNotEmpty(tempRoomRates.get(0).getTariffs().get(0).getPriceMap()) && tempRoomRates.get(0).getTariffs().get(0).getPriceMap().get(slotPlan.getDefaultPriceKey()) != null){
					TotalPricing totalPricing = tempRoomRates.get(0).getTariffs().get(0).getPriceMap().get(slotPlan.getDefaultPriceKey());
					commonResponseTransformer.getPriceDetail(totalPricing, dayUseRoomsResponse, slotPlan, false);
					priceKey = totalPricing.getPricingKey();
				}
			}
			if(roomTypeDetails.getSlot() != null) {
				Slot slotHES = roomTypeDetails.getSlot();
				com.mmt.hotels.clientgateway.response.dayuse.Slot slotCG = new com.mmt.hotels.clientgateway.response.dayuse.Slot();
				slotCG.setDuration(slotHES.getDuration());
				slotCG.setTimeSlot(utility.calculateTimeSlot_Meridiem(roomTypeDetails.getSlot()));
				slotPlan.setSlot(slotCG);
			}

			List<DayUseRoomCriteria> roomCriteriaList = new ArrayList<>();
			SelectRoomRatePlan roomRatePlan = tempRoomRates.get(0);
			if(roomRatePlan != null && CollectionUtils.isNotEmpty(roomRatePlan.getTariffs())){
				DayUseRoomCriteria roomCriteria = new DayUseRoomCriteria();
				roomCriteria.setRatePlanCode(roomRatePlan.getTariffs().get(0).getTariffCode());
				roomCriteria.setMtKey(roomRatePlan.getTariffs().get(0).getMtKey());
				roomCriteria.setRoomCode(roomCode);
				roomCriteria.setSupplierCode(roomRatePlan.getSupplierCode());
				roomCriteria.setPricingKey(priceKey);
				List<DayUseRoomStayCandidate> dayUseRoomStayCandidateList = new ArrayList<>();
				if(CollectionUtils.isNotEmpty(roomRatePlan.getTariffs()) && roomRatePlan.getTariffs().get(0) != null){
					if(CollectionUtils.isNotEmpty(roomRatePlan.getTariffs().get(0).getRoomTariffs())){
						for(RoomTariff roomTariff : roomRatePlan.getTariffs().get(0).getRoomTariffs()) {
							DayUseRoomStayCandidate dayUseRoomStayCandidate = new DayUseRoomStayCandidate();
							dayUseRoomStayCandidate.setAdultCount(roomTariff.getNumberOfAdults());
							dayUseRoomStayCandidate.setChildAges(roomTariff.getChildAges());
							dayUseRoomStayCandidateList.add(dayUseRoomStayCandidate);
						}
					}
				}
				roomCriteria.setRoomStayCandidates(dayUseRoomStayCandidateList);
				roomCriteriaList.add(roomCriteria);
				populateOccupancyDetailsInDayuseRoom(dayUseRoom, roomRatePlan.getTariffs().get(0).getOccupancydetails());
			}
			slotPlan.setRoomCriteria(roomCriteriaList);
			dayUseSlotPlanList.add(slotPlan);
		}
		dayUseRoomsResponse.setSlotPlans(dayUseSlotPlanList);
	}

	private void populateOccupancyDetailsInDayuseRoom(DayUseRoom dayUseRoom, RoomTariff occupancydetails) {
		if (dayUseRoom != null && occupancydetails != null) {
			dayUseRoom.setOccupancydetails(occupancydetails);
		}
	}

	private List<DayUsePersuasion> buildDayUsePersuasion(List<com.mmt.hotels.model.response.dayuse.DayUsePersuasion> dayUsePersuasions,
														 com.mmt.hotels.clientgateway.request.FeatureFlags featureFlags, RequestDetails requestDetails) {
		List<DayUsePersuasion> dayUsePersuasionList = null;
		if(featureFlags != null && featureFlags.isDayUsePersuasion() && requestDetails != null && FUNNEL_DAYUSE.equalsIgnoreCase(requestDetails.getFunnelSource())){
			dayUsePersuasionList = new ArrayList<>();
			utility.transformLateCheckout(dayUseFunnelPersuasions,dayUsePersuasionList);
			if (CollectionUtils.isNotEmpty(dayUsePersuasions)) {
				for (com.mmt.hotels.model.response.dayuse.DayUsePersuasion dayUsePersuasionHES : dayUsePersuasions) {
					DayUsePersuasion dayUsePersuasionCG = new DayUsePersuasion();
					dayUsePersuasionCG.setId(dayUsePersuasionHES.getId());
					dayUsePersuasionCG.setText(dayUsePersuasionHES.getText());
					dayUsePersuasionCG.setIcon(dayUsePersuasionHES.getIcon());
					dayUsePersuasionCG.setIconType(dayUsePersuasionHES.getIconType());
					dayUsePersuasionList.add(dayUsePersuasionCG);
				}
			}
		}
		return dayUsePersuasionList;
	}

	private Card buildPaymentCard(RequestDetails requestDetails, HotelRates hotelRates) {
		if (requestDetails != null && utility.isGroupBookingFunnel(requestDetails.getFunnelSource()) && hotelRates.getRecommendedRoomTypeDetails() != null
				&& hotelRates.getRecommendedRoomTypeDetails().getPaymentPlan() != null && StringUtils.isNotBlank(hotelRates.getRecommendedRoomTypeDetails().getPaymentPlan().getPaymentCardText())) {
			Card paymentCard = new Card();
			paymentCard.setTitle(hotelRates.getRecommendedRoomTypeDetails().getPaymentPlan().getPaymentCardText());
			paymentCard.setCta(polyglotService.getTranslatedData(KNOW_MORE));
			paymentCard.setIconUrl("https://promos.makemytrip.com/Hotels_product/group/ic_rupee_2x.png");
			return paymentCard;
		}
		return null;
	}



	/**
	 * This Function is used to add SellableLabel into SearchResponse on the basis of sellableType("Bed","Room") present in roomDetails
	 * applied into OccupancyRooms and ExactRooms
	 * This is part of A/B Experiment and applicable to propertyType "Hostel"
	 **/
	public Pair<Boolean, Boolean> addSellableLabelFromSellableType(SearchRoomsResponse searchRoomsResponse) {
		if (searchRoomsResponse == null) {
			return new ImmutablePair<>(false, false);
		}
		try {
			boolean bedAvailable = false, roomAvailable = false;
			Pair<Boolean, Boolean> isBedAndRoomAvailable = null;
			if (CollectionUtils.isNotEmpty(searchRoomsResponse.getRecommendedCombos())) {
				for (RecommendedCombo recommendedCombo : searchRoomsResponse.getRecommendedCombos()) {
					if (recommendedCombo != null) {
						isBedAndRoomAvailable = addSellableLabelAndRoomTypeFilterCode(recommendedCombo.getRooms());
						bedAvailable = bedAvailable || isBedAndRoomAvailable.getKey();
						roomAvailable = roomAvailable || isBedAndRoomAvailable.getValue();
					}
				}
			}
			isBedAndRoomAvailable = addSellableLabelAndRoomTypeFilterCode(searchRoomsResponse.getOccupancyRooms());
			bedAvailable = bedAvailable || isBedAndRoomAvailable.getKey();
			roomAvailable = roomAvailable || isBedAndRoomAvailable.getValue();
			isBedAndRoomAvailable = addSellableLabelAndRoomTypeFilterCode(searchRoomsResponse.getExactRooms());
			bedAvailable = bedAvailable || isBedAndRoomAvailable.getKey();
			roomAvailable = roomAvailable || isBedAndRoomAvailable.getValue();
			return new ImmutablePair<>(bedAvailable, roomAvailable);
		} catch (Exception ex) {
			LOGGER.error("Error while adding sellableLabel in Occupancy and exact Rooms", ex);
		}
		return new ImmutablePair<>(false, false);

	}

	/***
	 * This function add SellableLabel and RoomTypeFilterCode for each roomCode
	 * @param rooms
	 * @return boolean true/false basis of {sellableTypeBed,sellableTypeRoom} is available for list of roomDetails.
	 */
	private Pair<Boolean, Boolean> addSellableLabelAndRoomTypeFilterCode(List<RoomDetails> rooms) {
		boolean sellableTypeBed = false, sellableTypeRoom = false;
		if (CollectionUtils.isNotEmpty(rooms))
			for (RoomDetails room : rooms) {
				List<String> filterCode = new ArrayList<>();
				String sellableType = (CollectionUtils.isNotEmpty(room.getRatePlans()) ? room.getRatePlans().get(0).getSellableType() : null);
				if (Constants.SELLABLE_BED_TYPE.equalsIgnoreCase(sellableType)) {
					room.setSellableLabel(polyglotService.getTranslatedData(ConstantsTranslation.BEDS_SELLABLE_LABEL));
					filterCode.add(BEDS_SELLABLE_TYPE_FILTER_CODE);
					sellableTypeBed = true;
				} else if (Constants.SELLABLE_ROOM_TYPE.equalsIgnoreCase(sellableType)) {
					room.setSellableLabel(polyglotService.getTranslatedData(ConstantsTranslation.ROOMS_SELLABLE_LABEL));
					filterCode.add(ROOMS_SELLABLE_TYPE_FILTER_CODE);
					sellableTypeRoom = true;
				}
				room.setFilterCode(filterCode);
			}
		return new ImmutablePair<>(sellableTypeBed, sellableTypeRoom);
	}

	/**
	 * This function is used  sort the SearchResponse on the basis of sellableType and ratePlans
	 * This is part of A/B Experiment and applicable to propertyType "Hostel"
	 * SORTING LOGIC -> Sort Firstly on the Basis of LowestRoomCodeSellableType i.e., beds and rooms having bed comes first only when lowestRoomCodeSellableType is bed else room
	 * And if both having same sellableType example{bed} then sort on the basis of price available in ratePlans[0]
	 **/
	public void sortBySellableType(SearchRoomsResponse searchRoomsResponse) {
		try {
			groupAndSortRooms(searchRoomsResponse.getOccupancyRooms());
			groupAndSortRooms(searchRoomsResponse.getExactRooms());
		} catch (Exception ex) {
			LOGGER.error("error while sorting SearchResponse on the basis of sellableType", ex);
		}


	}

	private void groupAndSortRooms(List<RoomDetails> rooms) {
		if (CollectionUtils.isNotEmpty(rooms)) {
			String sellableTypeOfLowestRatePlan;
			if (isSellableTypePresent(rooms.get(0))) {
				sellableTypeOfLowestRatePlan = rooms.get(0).getRatePlans().get(0).getSellableType();
			} else {
				sellableTypeOfLowestRatePlan = null;
			}
			rooms.sort((room1, room2) -> {
				if (isSellableTypePresent(room1) && isSellableTypePresent(room2) && !room1.getRatePlans().get(0).getSellableType().equalsIgnoreCase(room2.getRatePlans().get(0).getSellableType())) {
					if (SELLABLE_ROOM_TYPE.equalsIgnoreCase(sellableTypeOfLowestRatePlan)) {
						return (room2.getRatePlans().get(0).getSellableType()).compareTo(room1.getRatePlans().get(0).getSellableType());
					}
					return (room1.getRatePlans().get(0).getSellableType()).compareTo(room2.getRatePlans().get(0).getSellableType());
				}
				return getAmountFromLabel(room1, Constants.TOTAL_AMOUNT_KEY) - getAmountFromLabel(room2, Constants.TOTAL_AMOUNT_KEY);
			});
		}
	}

	private boolean isSellableTypePresent(RoomDetails room)
	{
		return CollectionUtils.isNotEmpty(room.getRatePlans()) && StringUtils.isNotBlank(room.getRatePlans().get(0).getSellableType());
	}

	private int getAmountFromLabel(RoomDetails roomDetails, String amountLabelKey) {
		try {
			String defaultPriceKey = roomDetails.getRatePlans().get(0).getTariffs().get(0).getDefaultPriceKey();
			return (int) roomDetails.getRatePlans().get(0).getTariffs().get(0).getPriceMap()
					.get(defaultPriceKey).getDetails().stream().filter(e -> amountLabelKey.equalsIgnoreCase(e.getKey())).findFirst().get().getAmount();
		} catch (Exception ex) {
			LOGGER.error("error while sorting SearchResponse on the basis of sellableType", ex);
		}
		return 0;
	}

	private HotelDetails buildHotelDetails(HotelRates hotelRates, String funnelSrc){

		if(hotelRates!=null) {
			HotelDetails hotelDetails = new HotelDetails();
			hotelDetails.setHotelName(hotelRates.getName());
			hotelDetails.setHotelIcon(hotelRates.getHotelIcon());
			hotelDetails.setHotelType(hotelRates.getHotelType());
			String detailDeepLinkUrl = hotelRates.getDetailDeeplinkUrl();

			if(detailDeepLinkUrl!=null){

				String[] parts = detailDeepLinkUrl.split(Constants.URL_PARAM_BASE_SPLITTER);
				if (parts.length == 2) {
					String base = parts[0];
					String params = parts[1];

					detailDeepLinkUrl =  base + Constants.QUE_MARK + Stream.of(params.split(Constants.AMP))
							.map(p -> p.split(Constants.EQUI))
							.filter(p -> !p[0].equals(Constants.CHECK_AVAILBILITY_PARAM))
							.map(p -> String.join(Constants.EQUI, p))
							.collect(Collectors.joining(Constants.AMP));

					if(Constants.FUNNEL_SOURCE_HOMESTAY.equalsIgnoreCase(funnelSrc)){
						detailDeepLinkUrl+= Constants.AMP+ Constants.FUNNEL_SOURCE_HOMESTAY.toLowerCase()+  Constants.EQUI + "true";
					}
				}
			}

			hotelDetails.setUrl(detailDeepLinkUrl);
			return hotelDetails;
		}
		return null;
	}
	public int getTotalSearchedPaxCount(List<RoomStayCandidate> roomStayCandidates) {
		if(roomStayCandidates == null)
			return 0;
		int totalSearchedPaxCount = 0;
		if(CollectionUtils.isNotEmpty(roomStayCandidates)) {
			for (RoomStayCandidate roomStayCandidate : roomStayCandidates) {
				totalSearchedPaxCount += roomStayCandidate.getAdultCount() + (roomStayCandidate.getChildAges() == null ? 0 : roomStayCandidate.getChildAges().size());
			}
		}
		return totalSearchedPaxCount;
	}

	protected abstract LoginPersuasion buildLoginPersuasion();

	protected abstract void buildLoyaltyCashbackPersuasions(BestCoupon coupon, Map<String, PersuasionResponse> persuasionMap);
	protected abstract void buildLoyaltyCashbackPersuasions(BestCoupon coupon, Map<String, PersuasionResponse> persuasionMap, int myPartnerCashback, HeroTierDetails heroTierDetails);


	private ExtraGuestDetailPersuasion buildExtraGuestDetailPersuasion(boolean isHighDemandPersuasonEnable, RoomTypeDetails roomTypeDetails, boolean isAltAccoHotel) {

		ExtraGuestDetailPersuasion extraGuestDetailPersuasion;
		//High Demand persuasion has highest priority
		if(isHighDemandPersuasonEnable){
			extraGuestDetailPersuasion = new ExtraGuestDetailPersuasion();
			String persuasionText = polyglotService.getTranslatedData(HIGH_DEMAND_PERSUASION);
			extraGuestDetailPersuasion.setText("<font color=\"" + highDemandPersuasionColor + "\">" + persuasionText + "</font>");
			return extraGuestDetailPersuasion;
		}else if (!isAltAccoHotel && roomTypeDetails != null && MapUtils.isNotEmpty(roomTypeDetails.getRoomType())) {
			if (roomTypeDetails.getFreeChildCount() > 0 && StringUtils.isNotEmpty(roomTypeDetails.getFreeChildText())) {
				return getExtraGuestDetailFreeChildText(roomTypeDetails.getFreeChildText());
			}
			RoomType roomType = roomTypeDetails.getRoomType().values().stream().findFirst().orElse(null);
			if (roomType != null && MapUtils.isNotEmpty(roomType.getRatePlanList())) {
				RatePlan ratePlan = roomType.getRatePlanList().values().stream().findFirst().orElse(null);
				if (ratePlan != null && ratePlan.freeChildCount > 0 && StringUtils.isNotEmpty(ratePlan.getFreeChildText())) {
					return getExtraGuestDetailFreeChildText(ratePlan.getFreeChildText());
				}
				if (ratePlan != null && ratePlan.getExtraGuestDetail() != null && StringUtils.isNotEmpty(ratePlan.getExtraGuestDetail().getHotelDetailExtraBedText())) {
					extraGuestDetailPersuasion = new ExtraGuestDetailPersuasion();
					extraGuestDetailPersuasion.setIconUrl(polyglotService.getTranslatedData(DEAL_ICON_URL));
					String translatedText = polyglotService.getTranslatedData(EXTRA_GUEST_DEAL_TEXT_STYLE);
					if (StringUtils.isNotEmpty(translatedText)) {
						translatedText = MessageFormat.format(translatedText.toString(), ratePlan.getExtraGuestDetail().getHotelDetailExtraBedText());
						extraGuestDetailPersuasion.setText(translatedText);
						return extraGuestDetailPersuasion;
					}
				}
			}
		}



		return null;

	}


	private boolean isLengthOfStaySatisfactory(String checkIn, String checkOut) {
		if (StringUtils.isEmpty(checkIn) || StringUtils.isEmpty(checkOut)) {
			return false;
		}
		return dateUtil.getDaysDiff(LocalDate.parse(checkIn), LocalDate.parse(checkOut)) < commonConfigConsul.getLosFosGCCNudgePersuasion();
	}

	private LongStayGCCNudgePersuasion buildLongStayGCCNudgePersuasion(HotelRates hotelRates, String checkIn, String checkOut) {
		if ( commonConfigConsul !=null &&commonConfigConsul.getLosFosGCCNudgePersuasion() == 0) {
			return null;
		}
		LongStayGCCNudgePersuasion longStayGCCNudgePersuasion;
		if (hotelRates != null  &&  hotelRates.isShowOfferType() && !"IN".equalsIgnoreCase(hotelRates.getCountryCode()) && isGccOrKsa() && isLengthOfStaySatisfactory(checkIn, checkOut)) {
			longStayGCCNudgePersuasion = new LongStayGCCNudgePersuasion();
			String persuasionText = polyglotService.getTranslatedData(LONG_STAY_GCC_NUDGE_PERSUASION);
			String formattedPersuasionText = String.format(PERSUASION_TEXT_TEMPLATE, extraGuestFreeChildColor, persuasionText);
			longStayGCCNudgePersuasion.setText(formattedPersuasionText);
			longStayGCCNudgePersuasion.setIconUrl(longStayGccNudgeIconUrl);
			return longStayGCCNudgePersuasion;
		}
		return null;
	}

	private ExtraGuestDetailPersuasion getExtraGuestDetailFreeChildText(String freeChildText) {
		ExtraGuestDetailPersuasion extraGuestDetailPersuasion = null;
		if (StringUtils.isNotEmpty(freeChildText)) {
			extraGuestDetailPersuasion = new ExtraGuestDetailPersuasion();
			extraGuestDetailPersuasion.setIconUrl(singleTickUrl);
			extraGuestDetailPersuasion.setText("<font color=\"" + extraGuestFreeChildColor + "\">" + freeChildText + "</font>");
		}
		return extraGuestDetailPersuasion;
	}

	private RtbPersuasionCard buildRtbPersuasionCard(HotelRates hotelRates) {
		RtbPersuasionCard rtbPersuasionCard;
		if (hotelRates.isRequestToBook() && hotelRates.isRTBRatePlanPreApproved()) {
			rtbPersuasionCard = new RtbPersuasionCard();
			rtbPersuasionCard.setTitle(polyglotService.getTranslatedData(RTB_PRE_APPROVED_TEXT));
			rtbPersuasionCard.setIconText(polyglotService.getTranslatedData(COMPLETE_YOUR_BOOKING));
			rtbPersuasionCard.setIconUrl(rtbCardConfigs.get("rtbPreApprovedIcon"));
			rtbPersuasionCard.setIsPreApproved(true);
			if (hotelRates.getRtbPreApprovedCard() != null) {
				rtbPersuasionCard.setApprovedOn(hotelRates.getRtbPreApprovedCard().getApprovedOn());
				rtbPersuasionCard.setExpiry(hotelRates.getRtbPreApprovedCard().getExpiry());
				rtbPersuasionCard.setIconDeepLink(hotelRates.getRtbPreApprovedCard().getReviewDeeplink());
			}
			return rtbPersuasionCard;
		}
		if (hotelRates.isRequestToBook() && !hotelRates.isRtbPreApproved()) {
			rtbPersuasionCard = new RtbPersuasionCard();
			rtbPersuasionCard.setTitle(polyglotService.getTranslatedData(ConstantsTranslation.RTB_TITLE));
			rtbPersuasionCard.setText(polyglotService.getTranslatedData(ConstantsTranslation.RTB_CARD_TEXT));
			rtbPersuasionCard.setIconUrl(rtbCardConfigs.get("rtbCardIcon"));
			rtbPersuasionCard.setIconDeepLink(rtbCardConfigs.get("rtbDeepLink"));
			rtbPersuasionCard.setIconText(polyglotService.getTranslatedData(KNOW_MORE));
			rtbPersuasionCard.setRtbCard(buildRtbCard());
			return rtbPersuasionCard;
		}
		return null;
	}

	private SpaceData getSpaceData(com.mmt.hotels.model.response.staticdata.SpaceData hesSpaceData, RoomType roomType, String expData) {
		if (hesSpaceData == null)
			return null;
		SpaceData cgSpaceData = new SpaceData();
		int searchedPaxCount = getSearchedPaxedCount(roomType);
		int extraBedCount = 0, totalBaseOccupancy = 0, totalMaxOccupancy = 0;
		if (hesSpaceData != null) {
			cgSpaceData.setDescriptive(hesSpaceData.getDescriptive());
			cgSpaceData.setSharedInfo(buildSharedInfo(hesSpaceData.getSharedInfo()));
			List<Space> spaceList = new ArrayList<>();
			for (com.mmt.hotels.model.response.staticdata.Space hesSpace : hesSpaceData.getSpaces()) {
				if(searchedPaxCount > 0 && hesSpace.getSpaceType() !=null && (hesSpace.getSpaceType().equalsIgnoreCase(Constants.BEDROOM) || hesSpace.getSpaceType().equalsIgnoreCase(Constants.LIVING_ROOM))) {
					hesSpace.setFinalOccupancy(min(hesSpace.getBaseOccupancy(), searchedPaxCount));
					searchedPaxCount -= hesSpace.getFinalOccupancy();
				}
			}
			for(com.mmt.hotels.model.response.staticdata.Space hesSpace: hesSpaceData.getSpaces()){
				Space cgSpace = new Space();
				cgSpace.setAreaText(hesSpace.getAreaText());
				cgSpace.setDescriptionText(hesSpace.getDescriptionText());
				cgSpace.setName(hesSpace.getName());
				String subText = hesSpace.getSubText();
				cgSpace.setSpaceId(hesSpace.getSpaceId());
				cgSpace.setSpaceType(hesSpace.getSpaceType());
				if(hesSpace.getSpaceType() != null && (hesSpace.getSpaceType().equalsIgnoreCase(Constants.BEDROOM) || hesSpace.getSpaceType().equalsIgnoreCase(Constants.LIVING_ROOM))) {
					if (searchedPaxCount > 0) {
						int diff = min(hesSpace.getMaxOccupancy() - hesSpace.getFinalOccupancy(), searchedPaxCount);
						hesSpace.setFinalOccupancy(hesSpace.getFinalOccupancy() + diff);
						searchedPaxCount -= diff;
						extraBedCount += max(0, hesSpace.getFinalOccupancy() - hesSpace.getBaseOccupancy());
					}
					int finalOccupancy = hesSpace.getFinalOccupancy();
					int occupancy = max(finalOccupancy, hesSpace.getBaseOccupancy());
					if (occupancy > 0)
						subText = (occupancy > 1) ? polyglotService.getTranslatedData(ConstantsTranslation.SPACE_OCCUPANCY_TEXT) : polyglotService.getTranslatedData(ConstantsTranslation.SPACE_SINGLE_OCCUPANCY_TEXT);
					else
						subText = null;
					if (subText != null) {
						subText = subText.replace(OCCUPANCY_PARAMETER, String.valueOf(occupancy));
					}
					totalBaseOccupancy += hesSpace.getBaseOccupancy();
					totalMaxOccupancy += hesSpace.getMaxOccupancy();
				}
				cgSpace.setSubText(subText);
				cgSpace.setOpenCardText(buildOpenCardText(hesSpace, expData));
				if(CollectionUtils.isNotEmpty(hesSpace.getMedia())){
					List<MediaData> mediaDataList = new ArrayList<>();
					for(com.mmt.hotels.model.response.staticdata.MediaData hesMediaData: hesSpace.getMedia()){
						MediaData cgMediaData = new MediaData();
						cgMediaData.setMediaType(hesMediaData.getMediaType());
						cgMediaData.setUrl(hesMediaData.getUrl());
						mediaDataList.add(cgMediaData);
					}
					cgSpace.setMedia(mediaDataList);
				}
				spaceList.add(cgSpace);
			}
			cgSpaceData.setSpaces(spaceList);
		}
		cgSpaceData.setBaseGuests(totalBaseOccupancy);
		cgSpaceData.setExtraBeds(extraBedCount);
		cgSpaceData.setMaxGuests(totalMaxOccupancy);
		return cgSpaceData;
	}

	private SpaceData getSpaceDataV2(com.mmt.hotels.model.response.staticdata.SpaceData hesSpaceData, boolean isPrivateSpace) {
		if (hesSpaceData == null)
			return null;
		SpaceData cgSpaceData = new SpaceData();
		int extraBedCount = 0, totalBaseOccupancy = 0, totalMaxOccupancy = 0;
		if (hesSpaceData != null) {
			cgSpaceData.setDescriptive(hesSpaceData.getDescriptive());
			cgSpaceData.setSharedInfo(buildSharedInfo(hesSpaceData.getSharedInfo()));
			List<Space> spaceList = new ArrayList<>();
			for(com.mmt.hotels.model.response.staticdata.Space hesSpace: hesSpaceData.getSpaces()){
				Space cgSpace = new Space();
				cgSpace.setName(hesSpace.getName());
				cgSpace.setSpaceId(hesSpace.getSpaceId());
				cgSpace.setSpaceType(hesSpace.getSpaceType());
				cgSpace.setAreaText(hesSpace.getAreaText());
				cgSpace.setSpaceInclusions(buildSpaceInclusion(hesSpace));
				if(isPrivateSpace){
//					cgSpace.setSubText(hesSpace.getAreaText());
					cgSpace.setAreaText(null);
				} else{
					cgSpace.setDescriptionText(hesSpace.getDescriptionText());
					String subText = hesSpace.getSubText();
					if(hesSpace.getSpaceType() != null && (hesSpace.getSpaceType().equalsIgnoreCase(Constants.BEDROOM) || hesSpace.getSpaceType().equalsIgnoreCase(Constants.LIVING_ROOM))) {
						int finalOccupancy = hesSpace.getFinalOccupancy();
						int occupancy = max(finalOccupancy, hesSpace.getBaseOccupancy());
						if (occupancy > 0)
							subText = (occupancy > 1) ? polyglotService.getTranslatedData(ConstantsTranslation.SPACE_OCCUPANCY_TEXT) : polyglotService.getTranslatedData(ConstantsTranslation.SPACE_SINGLE_OCCUPANCY_TEXT);
						else
							subText = null;
						if (subText != null) {
							subText = subText.replace(OCCUPANCY_PARAMETER, String.valueOf(occupancy));
						}
					}
					cgSpace.setSubText(subText);
				}
				if(CollectionUtils.isNotEmpty(hesSpace.getMedia())){
					List<MediaData> mediaDataList = new ArrayList<>();
					for(com.mmt.hotels.model.response.staticdata.MediaData hesMediaData: hesSpace.getMedia()){
						MediaData cgMediaData = new MediaData();
						cgMediaData.setMediaType(hesMediaData.getMediaType());
						cgMediaData.setUrl(hesMediaData.getUrl());
						mediaDataList.add(cgMediaData);
					}
					cgSpace.setMedia(mediaDataList);
				}
				spaceList.add(cgSpace);
			}
			cgSpaceData.setSpaces(spaceList);
		}
		cgSpaceData.setBaseGuests(totalBaseOccupancy);
		cgSpaceData.setExtraBeds(extraBedCount);
		cgSpaceData.setMaxGuests(totalMaxOccupancy);
		return cgSpaceData;
	}

	/**
	 * * The node is added to show the share property icon and url in share property section  layout, for reference (HTL-37205)
	 * @param hesSharedInfo
	 * @return
	 */
	private SharedInfo buildSharedInfo(com.mmt.hotels.model.response.staticdata.SharedInfo hesSharedInfo) {
		if(hesSharedInfo == null)
			return null;
		SharedInfo sharedInfo = new SharedInfo();
		sharedInfo.setIconUrl(hesSharedInfo.getIconUrl());
		sharedInfo.setInfoText(hesSharedInfo.getInfoText());
		return  sharedInfo;
	}


	private String buildOpenCardText(com.mmt.hotels.model.response.staticdata.Space hesSpace, String expData) {
		Map<String,String> expDataMap = new HashMap<>();
		if (StringUtils.isNotBlank(expData)) {
			expData = expData.replaceAll("^\"|\"$", "");
			Type type = new TypeToken<Map<String, String>>() {
			}.getType();
			expDataMap = gson.fromJson(expData,type);
		}
		if(expDataMap.get("plcnew") != null && StringUtils.equalsIgnoreCase(expDataMap.get("plcnew"),"true")) {
			StringBuilder openCardText = new StringBuilder(hesSpace.getDescriptionText());
			if(StringUtils.isNotBlank(hesSpace.getOpenCardText())) {
				openCardText.append(BREAK_AMENITIES_BOLD).append(hesSpace.getOpenCardText());
			}
			return openCardText.toString();
		}

		return hesSpace.getOpenCardText();
	}

	private List<String> buildSpaceInclusion(com.mmt.hotels.model.response.staticdata.Space hesSpace) {
		StringBuilder responseString = new StringBuilder();
		if (hesSpace != null) {
			if (hesSpace.getSleepingDetails() != null) {
				if (hesSpace.getSleepingDetails().getBedInfo() != null) {
					for (com.mmt.hotels.model.response.staticdata.SleepingBedInfo bedsInfo : hesSpace.getSleepingDetails().getBedInfo()) {
						if (StringUtils.isNotEmpty(responseString.toString())) {
							responseString.append(SPACE).append(AMP).append(SPACE).append(buildBedType(bedsInfo));
						} else {
							responseString.append(buildBedType(bedsInfo));
						}
					}
				}
				if (hesSpace.getSleepingDetails().getExtraBedInfo() != null) {
					for (com.mmt.hotels.model.response.staticdata.SleepingBedInfo bedsInfo : hesSpace.getSleepingDetails().getExtraBedInfo()) {
						responseString.append(COMMA_SPACE).append(EXTRA).append(SPACE).append(buildBedType(bedsInfo)).append(SPACE).append(AVAILABLE.toLowerCase());
					}
				}
			}
			if (StringUtils.isNotEmpty(responseString.toString()) && StringUtils.isNotEmpty(hesSpace.getDescriptionText())) {
				responseString.append(PIPE_SEPARATOR);
			}
			responseString.append(hesSpace.getDescriptionText());
		}
		return StringUtils.isNotEmpty(responseString.toString()) ? Arrays.asList(responseString.toString().split(PIPE_SEPARATOR_WITH_BACKSLASH)) : null;
	}

	private String buildBedType(com.mmt.hotels.model.response.staticdata.SleepingBedInfo bedsInfo) {
		if(bedsInfo != null && StringUtils.isNotEmpty(bedsInfo.getBedType())){
			if(bedsInfo.getBedCount() > 1){
				return bedsInfo.getBedCount() + SPACE + bedsInfo.getBedType() + PLURAL_STRING;
			}else if(bedsInfo.getBedCount() == 1){
				return bedsInfo.getBedCount() + SPACE + bedsInfo.getBedType();
			}else{
				return bedsInfo.getBedType();
			}
		}
		return null;
	}

	private int getSearchedPaxedCount(RoomType roomType) {
		if(roomType != null) {
			Optional<String> rateplanCode = roomType.getRatePlanList().keySet().stream().findFirst();
			if(rateplanCode.isPresent()) {
				String ratePlanCodeStr = rateplanCode.get();
				if(roomType.getRatePlanList().get(ratePlanCodeStr).getAvailDetails() != null && roomType.getRatePlanList().get(ratePlanCodeStr).getAvailDetails().getOccupancyDetails() != null) {
					double totalSearchedPax = roomType.getRatePlanList().get(ratePlanCodeStr).getAvailDetails().getOccupancyDetails().getAdult() +
							roomType.getRatePlanList().get(ratePlanCodeStr).getAvailDetails().getOccupancyDetails().getChild();
					double noOfRooms = roomType.getRatePlanList().get(ratePlanCodeStr).getAvailDetails().getOccupancyDetails().getNumOfRooms();
					return (int) ceil(totalSearchedPax / noOfRooms);
				}
			}
		}
		return 0;
	}

	protected AllInclusiveCard buildAllInclusiveCard(HotelRates hotelRates){
		AllInclusiveCard card=null;
		try {
			card= new AllInclusiveCard();
			card.setData(allInclusiveCard.getData());
			card.setDesc(allInclusiveCard.getDesc());
			card.setTitle(allInclusiveCard.getTitle());
			card.setPersuasionText(allInclusiveCard.getPersuasionText());
			card.setImageUrl(allInclusiveCard.getImageUrl());
			card.setRatePlanCode(hotelRates.getAllInclusiveRpc());
			card.setRoomCode(hotelRates.getAllInclusiveRoomCode());
			if (!hotelRates.isLowestRateAllInclusive()) {
				card.setAmount(hotelRates.getRoomTypeDetails().getRoomType().get(hotelRates.getAllInclusiveRoomCode())
						.getRatePlanList().get(hotelRates.getAllInclusiveRpc()).getDisplayFare().getDisplayPriceBreakDown().getDisplayPrice());
			}
		}catch(Exception e){
			LOGGER.error("Exception while getting allinclusivecard",e);
		}
		return card;
	}

	private List<RtbCard> buildRtbCard() {
		List<RtbCard> rtbCards = new ArrayList<>();
		int index = 1;
		while (index < 3) {
			RtbCard rtbCard = new RtbCard();
			rtbCard.setTitle(polyglotService.getTranslatedData(RTB_CARD_TITLE + index));
			rtbCard.setSubTitle(polyglotService.getTranslatedData(RTB_CARD_DESC + index));
			rtbCard.setIconUrl(rtbCardConfigs.get("rtbTickIcon"));
			rtbCards.add(rtbCard);
			index++;
		}
		return rtbCards;
	}

	private SleepingArrangementRoomInfo buildRoomInfo(HotelRates hotelRates, SearchRoomsResponse searchRoomsResponse, List<RoomStayCandidate> roomStayCandidates, String countryCode, boolean isOHSExpEnable, Pair<Boolean, Boolean> bedAndRoomPresent, boolean isNewDetailPageTrue, HotelsRoomInfoResponseEntity hotelsRoomInfoResponseEntity, boolean isIHAltAccoNodesExp, boolean serviceApartment, Map<String, String> expDataMap) {
		if (hotelRates == null) {
			return null;
		}
		SleepingArrangementRoomInfo roomInfo = new SleepingArrangementRoomInfo();
//		int totalRooms = Optional.ofNullable(hotelRates.getRoomTypeDetails())
//				.map(roomTypeDetails -> roomTypeDetails.getRoomType())
//				.flatMap(roomTypeMap -> roomTypeMap.entrySet().stream().findFirst())
//				.map(Map.Entry::getValue)
//				.map(roomType -> roomType.getRatePlanList())
//				.flatMap(ratePlanMap -> ratePlanMap.entrySet().stream().findFirst())
//				.map(Map.Entry::getValue)
//				.map(ratePlan -> ratePlan.getRoomTariff())
//				.map(List::size)
//				.orElse(0);
		buildRoomInfoTitle(isOHSExpEnable,hotelRates, countryCode, isIHAltAccoNodesExp, roomInfo);
		String client = MDC.get(MDCHelper.MDCKeys.CLIENT.getStringValue());
		if (isIHAltAccoNodesExp && hotelRates.isAltAcco() && !PROPERTY_TYPE_HOSTEL.equalsIgnoreCase(hotelRates.getPropertyType())) {
			buildSleepingArrangementFromBedroomInfo(searchRoomsResponse, hotelsRoomInfoResponseEntity, roomInfo, countryCode);
			if (Constants.DEVICE_OS_DESKTOP.equalsIgnoreCase(client)) {
				roomInfo.setSizeDesc(getRoomSizeWithUnit(searchRoomsResponse, hotelsRoomInfoResponseEntity));
			}
		}
		boolean modifyStayDetailsForIH = Constants.DEVICE_OS_DESKTOP.equalsIgnoreCase(client) && isIHAltAccoNodesExp && (!DOM_COUNTRY.equalsIgnoreCase(countryCode));
		String propertyType = hotelRates.getPropertyTypeMerged()!=null?hotelRates.getPropertyTypeMerged():hotelRates.getPropertyType();
		buildStayDetails(searchRoomsResponse, roomInfo, hotelRates.getSellableCombo(), propertyType,hotelRates, countryCode, serviceApartment, modifyStayDetailsForIH);
//		buildBedInfoText(searchRoomsResponse, roomInfo); // This is of No use, As we are buildingBedInfoText in Utility.applyRoundRobinOnGuestCountAndBedCount
		if (hotelRates.getLowestRate() != null && hotelRates.getLowestRate().getAvailDetails() != null) {
			Map<String, Integer> roomBedCount = new HashMap<>();
			roomBedCount.put(Constants.SELLABLE_ROOM_TYPE, hotelRates.getLowestRate().getAvailDetails().getNumOfRooms());
			roomBedCount.put(Constants.SELLABLE_BED_TYPE, hotelRates.getLowestRate().getAvailDetails().getOccupancyDetails().getBedCount());
			Tuple<String, String> guestRoomKeyValue = utility.getGuestRoomKeyValue(roomBedCount, propertyType, hotelRates.getCountryCode(), hotelRates.isHighSellingAltAcco(), hotelRates.isAltAcco(), null, hotelRates.getListingType(), false, 0, expDataMap, false);
			roomInfo.setGuestRoomKey(guestRoomKeyValue.getX());
			roomInfo.setGuestRoomValue(guestRoomKeyValue.getY());
		}
		String propertyInfoText = roomInfo.getTitle();
		try {
			// TODO : this hack is temporary, would be removed once we start getting property size from hotstore with space details
			roomInfo.setPropertyInfoText("");
			if(StringUtils.isNotBlank(propertyInfoText) && roomInfo.getStayDetail() != null && roomInfo.getStayDetail().getMaxGuests() != null){
				String[] splittedPropertyInfoText = propertyInfoText.split("\\(");
				if(splittedPropertyInfoText.length > 1) {
					propertyInfoText = "(" + splittedPropertyInfoText[1];
					propertyInfoText = propertyInfoText.replace(")", " | " + Constants.FITS + " " + roomInfo.getStayDetail().getMaxGuests() + ")");
					propertyInfoText = propertyInfoText.split("\\)")[0] + ")";
				} else {
					propertyInfoText = "";
				}
				roomInfo.setPropertyInfoText(propertyInfoText);
			}
		} catch (Exception e) {
			LOGGER.error("propertyInfoText could not be added due to : {} ", e.getMessage());
		}
		// baseOccupancy Logic given by product
		/*if(roomInfo.getStayDetail() != null && roomInfo.getStayDetail().getBaseGuests() > 0) {
			int totalSearchedPaxCount = getTotalSearchedPaxCount(roomStayCandidates);
			int totalBaseOccupancy = roomInfo.getStayDetail().getBaseGuests() == null ? 0 : roomInfo.getStayDetail().getBaseGuests();
			roomInfo.getStayDetail().setMaxGuests(Math.max(totalSearchedPaxCount, totalBaseOccupancy));
			// extraBeds count added to bed count
			if(roomInfo.getStayDetail().getExtraBeds() > 0) {
				roomInfo.getStayDetail().setBed(roomInfo.getStayDetail().getBed() + roomInfo.getStayDetail().getExtraBeds());
			}
		}*/

		// Get free child text from recommendedRoomTypeDetails in case of recommended combo and roomTypeDetails for exact match case
		String freeChildText = getFreeChildTextfromHotelRates(hotelRates);
		buildSleepInfoText(roomInfo.getStayDetail(),isOHSExpEnable,hotelRates.getSellableCombo(),bedAndRoomPresent, freeChildText,isNewDetailPageTrue);
		if (!DOM_COUNTRY.equalsIgnoreCase(countryCode) && isIHAltAccoNodesExp) {
			buildStayInfoList(roomInfo.getStayDetail(), hotelsRoomInfoResponseEntity, searchRoomsResponse, hotelRates);
			String roomInfoDescription = null;
			if (roomInfo.getStayDetail() != null && CollectionUtils.isNotEmpty(roomInfo.getStayDetail().getStayInfoList())) {
				if (hotelRates.getRoomCount() > 1) {
					if (hotelRates.getRecommendedRoomTypeDetails() != null && MapUtils.isNotEmpty(hotelRates.getRecommendedRoomTypeDetails().getRoomType()) && hotelRates.getRecommendedRoomTypeDetails().getRoomType().size() == 1) {
						//Anmol Issue 5 fixes
						roomInfoDescription = MessageFormat.format(polyglotService.getTranslatedData(MULTIPLE_ENTIRE_PROPERTY_GENERIC_TEXT), hotelRates.getRoomCount());
					} else {
						roomInfoDescription = MessageFormat.format(polyglotService.getTranslatedData(MULTIPLE_HETERO_PROPERTY_LAYOUT_TEXT), hotelRates.getRoomCount(), hotelRates.getPropertyType());
					}
				} else {
					roomInfoDescription = polyglotService.getTranslatedData(SINGLE_ENTIRE_PROPERTY_GENERIC_TEXT);
				}
				if((roomInfo.getStayDetail() != null && Objects.isNull(roomInfo.getStayDetail().getBedRoom()))  ||
						(roomInfo.getStayDetail() != null && Objects.nonNull(roomInfo.getStayDetail().getBedRoom()) &&
								roomInfo.getStayDetail().getBedRoom() == 0)) {
					if(PROPERTY_TYPE_APPARTMENT.equalsIgnoreCase(hotelRates.getPropertyType())) {
						roomInfoDescription = polyglotService.getTranslatedData(SINGLE_ENTIRE_PROPERTY_ZERO_BEDROOM_TEXT);
					}
				}
				if (PROPERTY_TYPE_HOSTEL.equalsIgnoreCase(hotelRates.getPropertyType())) {
					if ((searchRoomsResponse.getExactRooms() != null && !searchRoomsResponse.getExactRooms().isEmpty()) || (
							CollectionUtils.isNotEmpty(searchRoomsResponse.getRecommendedCombos()) && searchRoomsResponse.getRecommendedCombos().get(0) != null
									&& CollectionUtils.isNotEmpty(searchRoomsResponse.getRecommendedCombos().get(0).getRooms()) && searchRoomsResponse.getRecommendedCombos().get(0).getRooms().size() == 1)) {
						roomInfoDescription = polyglotService.getTranslatedData(HOSTEL_LAYOUT_TEXT_ZERO_BEDROOM_COUNT);
						if((roomInfo.getStayDetail() != null && CollectionUtils.isNotEmpty(roomInfo.getStayDetail().getStayInfoList())
								&& roomInfo.getStayDetail().getStayInfoList().size() ==1 &&
								PRIVATE_ROOMS_AVAILABLE.equalsIgnoreCase(roomInfo.getStayDetail().getStayInfoList().get(0).getInfoText()))){
							roomInfoDescription = null;
						}
					} else {
						roomInfoDescription = null;
					}
				}
				roomInfo.setDescription(roomInfoDescription);
			}
		} else {
			roomInfo.setDescription(isIHAltAccoNodesExp && LISTING_TYPE_ENTIRE.equalsIgnoreCase(hotelRates.getListingType()) && (hotelRates.getRoomCount() > 1) ? polyglotService.getTranslatedData(ROOM_INFO_SUBTITLE): null);
		}
		return roomInfo;
	}


	/**
	 * Builds the title for the room information based on the provided hotel rates, country code, and other parameters.
	 *
	 * @param isOHSExpEnable A flag indicating if the OHS experiment is enabled.
	 * @param hotelRates The hotel rates containing details about the stay type and room count.
	 * @param countryCode The country code to determine specific formatting rules.
	 * @param isIHAltAccoNodesExp A flag indicating if the IH Alt Acco Nodes experiment is enabled.
	 * @param roomInfo The object to which the title will be set.
	 */
	private void buildRoomInfoTitle(boolean isOHSExpEnable, HotelRates hotelRates, String countryCode, boolean isIHAltAccoNodesExp, SleepingArrangementRoomInfo roomInfo) {
		if (isOHSExpEnable && DOM_COUNTRY.equalsIgnoreCase(countryCode)) {
			roomInfo.setTitle(polyglotService.getTranslatedData(HOSTEL_TITLE));
		} else {
			if (StringUtils.isNotEmpty(hotelRates.getStayTypeWithSizeBed()))
				roomInfo.setTitle(hotelRates.getStayTypeWithSizeBed());
			else
				roomInfo.setTitle(hotelRates.getStayTypeWithSize());

			// For countryCode india, if ListingType is Entire or room and RoomCount is greater than 1
			if (DOM_COUNTRY.equalsIgnoreCase(countryCode) && isIHAltAccoNodesExp
					&& hotelRates.getRoomCount() > 1 && hotelRates.isAltAcco()) {
				String title = roomInfo.getTitle();
				if (LISTING_TYPE_ENTIRE.equalsIgnoreCase(hotelRates.getListingType()) && hotelRates.getRoomCount() > 1 && !title.endsWith(PLURAL_STRING)) {
					title += PLURAL_STRING;
				}
				int roomCount = hotelRates.getRoomCount();
				title = roomCount + SPACE + title;
				roomInfo.setTitle(title);
			} else if (LISTING_TYPE_ENTIRE.equalsIgnoreCase(hotelRates.getListingType()) && isIHAltAccoNodesExp
					&& (hotelRates.getRoomCount() > 1) && !ROOM_VALUE.equalsIgnoreCase(hotelRates.getSellableUnit())) {
				String title = roomInfo.getTitle();
				if (hotelRates.getRoomCount() > 1 && !title.endsWith(PLURAL_STRING)) {
					title += PLURAL_STRING;
				}
				int roomCount = hotelRates.getRoomCount();
				title = roomCount + SPACE + title;
				roomInfo.setTitle(title);
			}
		}
	}

	public String getRoomSizeWithUnit(SearchRoomsResponse searchRoomsResponse, HotelsRoomInfoResponseEntity hotelsRoomInfoResponseEntity) {
		if (searchRoomsResponse == null || hotelsRoomInfoResponseEntity == null) {
			return "";
		}

		String roomSizeWithUnit = "";

		// Process Exact Rooms
		if (CollectionUtils.isNotEmpty(searchRoomsResponse.getExactRooms())) {
			for (RoomDetails roomDetails : searchRoomsResponse.getExactRooms()) {
				if (hotelsRoomInfoResponseEntity.getHtlRmInfo() != null && !hotelsRoomInfoResponseEntity.getHtlRmInfo().isEmpty()) {
					Map<String, RoomInfo> roomInfoMap = hotelsRoomInfoResponseEntity.getHtlRmInfo().get(0).getHotelRoomInfo();
					if(roomInfoMap != null && roomDetails.getRoomCode() != null) {
						RoomInfo roomInfo = roomInfoMap.get(roomDetails.getRoomCode());
						if (roomInfo != null && StringUtils.isNotEmpty(roomInfo.getRoomSize())) {
							roomSizeWithUnit = roomInfo.getRoomSize() + " " + roomInfo.getRoomSizeUnit();
							break;
						}
					}
				}
			}
		}

		// Process Recommended Combos
		if (CollectionUtils.isNotEmpty(searchRoomsResponse.getRecommendedCombos())) {
			for (RecommendedCombo recommendedCombo : searchRoomsResponse.getRecommendedCombos()) {
				for (RoomDetails roomDetails : recommendedCombo.getRooms()) {
					if (hotelsRoomInfoResponseEntity.getHtlRmInfo() != null && !hotelsRoomInfoResponseEntity.getHtlRmInfo().isEmpty()) {
						Map<String, RoomInfo> roomInfoMap = hotelsRoomInfoResponseEntity.getHtlRmInfo().get(0).getHotelRoomInfo();
						if(roomInfoMap != null && roomDetails.getRoomCode() != null) {
							RoomInfo roomInfo = roomInfoMap.get(roomDetails.getRoomCode());
							if (roomInfo != null && StringUtils.isNotEmpty(roomInfo.getRoomSize())) {
								roomSizeWithUnit = roomInfo.getRoomSize() + " " + roomInfo.getRoomSizeUnit();
								break;
							}
						}
					}
				}
			}
		}

		return roomSizeWithUnit;
	}

	/*
	This function is used to build Sleeping Arrangement Details for IH-AltAcco, only when private spaces layout(INGO) is absent

	 */
	private void buildSleepingArrangementFromBedroomInfo(SearchRoomsResponse searchRoomsResponse, HotelsRoomInfoResponseEntity hotelsRoomInfoResponseEntity, SleepingArrangementRoomInfo roomInfo, String countryCode) {
		if (!DOM_COUNTRY.equalsIgnoreCase(countryCode) && roomInfo != null && hotelsRoomInfoResponseEntity != null && searchRoomsResponse != null && CollectionUtils.isNotEmpty(hotelsRoomInfoResponseEntity.getHtlRmInfo())) {
			if (searchRoomsResponse.getExactRooms() != null && !searchRoomsResponse.getExactRooms().isEmpty()) {
				List<RoomDetails> roomDetailsList = searchRoomsResponse.getExactRooms();
				RoomDetails roomDetails = roomDetailsList.get(0);
				buildSleepingArrangementDetails(hotelsRoomInfoResponseEntity, roomInfo, roomDetails);
			} else if (CollectionUtils.isNotEmpty(searchRoomsResponse.getRecommendedCombos()) && searchRoomsResponse.getRecommendedCombos().get(0) != null && CollectionUtils.isNotEmpty(searchRoomsResponse.getRecommendedCombos().get(0).getRooms()) && searchRoomsResponse.getRecommendedCombos().get(0).getRooms().size()==1) {
				List<RecommendedCombo> recommendedCombos = searchRoomsResponse.getRecommendedCombos();
				RecommendedCombo recommendedCombo = recommendedCombos.get(0);
				List<RoomDetails> roomDetailsList = recommendedCombo.getRooms();
				RoomDetails roomDetails = roomDetailsList.get(0);
				buildSleepingArrangementDetails(hotelsRoomInfoResponseEntity, roomInfo, roomDetails);
			}
		}
	}

	private void buildSleepingArrangementDetails(HotelsRoomInfoResponseEntity hotelsRoomInfoResponseEntity, SleepingArrangementRoomInfo roomInfo, RoomDetails roomDetails) {
		if (roomDetails != null && roomDetails.getPrivateSpaces() == null && roomDetails.getPrivateSpacesV2() == null && MapUtils.isNotEmpty(hotelsRoomInfoResponseEntity.getHtlRmInfo().get(0).getHotelRoomInfo())) {
			SleepingArrangementDetails sleepingArrangementDetails = null;
			RoomInfo roomInfos = hotelsRoomInfoResponseEntity.getHtlRmInfo().get(0).getHotelRoomInfo().get(roomDetails.getRoomCode());
				if (roomInfos != null && roomInfos.getBedRoomCount() != null && NumberUtils.toInt(roomInfos.getBedRoomCount(),0)>1) {
					List<RoomLayoutDetails> roomLayoutDetailsList = getRoomLayoutDetailsExternalVendor(roomInfos);
					if (CollectionUtils.isNotEmpty(roomLayoutDetailsList)) {
						sleepingArrangementDetails = new SleepingArrangementDetails();
						sleepingArrangementDetails.setHeaderText("Property Layout");
						sleepingArrangementDetails.setRoomLayoutDetailsList(roomLayoutDetailsList);
					}
				}
			roomInfo.setSleepingArrangement(sleepingArrangementDetails);
		}
	}

	private List<RoomLayoutDetails> getRoomLayoutDetailsExternalVendor(RoomInfo roomInfos) {
		if (roomInfos == null || CollectionUtils.isEmpty(roomInfos.getExternalVendorBedRoomInfoList())) {
			return null;
		}
		List<RoomLayoutDetails> roomLayoutDetailsList = new ArrayList<>();
		for (ExternalVendorBedRoomInfo externalVendorBedRoomInfo : roomInfos.getExternalVendorBedRoomInfoList()) {
			RoomLayoutDetails roomLayoutDetails = new RoomLayoutDetails();
			roomLayoutDetails.setTitle(StringUtils.isNotEmpty(externalVendorBedRoomInfo.getBedRoomName()) ? externalVendorBedRoomInfo.getBedRoomName() : polyglotService.getTranslatedData(SINGLE_BEDROOM_TITLE));
			if(Objects.nonNull(roomInfos.getBedRoomCount()) && SINGLE_BEDROOM_COUNT.equalsIgnoreCase(roomInfos.getBedRoomCount()) && Objects.nonNull(externalVendorBedRoomInfo) && BEDROOM_1.equalsIgnoreCase(externalVendorBedRoomInfo.getBedRoomName())) {
				roomLayoutDetails.setTitle(polyglotService.getTranslatedData(SINGLE_BEDROOM_TITLE));
			}
			roomLayoutDetails.setSubTitle(externalVendorBedRoomInfo.getBedRoomDescription());
			if (Constants.BATHROOMS.equalsIgnoreCase(externalVendorBedRoomInfo.getBedRoomName())) {
				roomLayoutDetails.setIconUrl(Constants.IMAGE_URL_BATHROOM_TYPE);
			} else {
				roomLayoutDetails.setIconUrl(IMAGE_URL_DOUBLE_BED);
			}
			if (SINGLE_SOFA_BED.equalsIgnoreCase(externalVendorBedRoomInfo.getBedRoomDescription())) {
				roomLayoutDetails.setTitle("Common Area");
				roomLayoutDetails.setIconUrl(IMAGE_URL_SOFA_BED);
			}
			roomLayoutDetailsList.add(roomLayoutDetails);
		}
		if (CollectionUtils.isNotEmpty(roomLayoutDetailsList)) {
			if (roomLayoutDetailsList.size() == 1 && !Constants.BATHROOMS.equalsIgnoreCase(roomLayoutDetailsList.get(0).getTitle())) {
				roomLayoutDetailsList.get(0).setTitle(polyglotService.getTranslatedData(SINGLE_BEDROOM_TITLE));
			} else if (roomLayoutDetailsList.size() == 2 && Constants.BATHROOMS.equalsIgnoreCase(roomLayoutDetailsList.get(1).getTitle())) {
				roomLayoutDetailsList.get(0).setTitle(polyglotService.getTranslatedData(SINGLE_BEDROOM_TITLE));
			}
		}
		return roomLayoutDetailsList;
	}

	private String getFreeChildTextfromHotelRates(HotelRates hotelRates) {
		String freeChildText = null;
		if (hotelRates != null) {
			if (hotelRates.getRecommendedRoomTypeDetails() != null && hotelRates.getRecommendedRoomTypeDetails().getFreeChildCount() > 0 && StringUtils.isNotEmpty(hotelRates.getRecommendedRoomTypeDetails().getFreeChildText())) {
				freeChildText = hotelRates.getRecommendedRoomTypeDetails().getFreeChildText();
			} else if (hotelRates.getRoomTypeDetails() != null && MapUtils.isNotEmpty(hotelRates.getRoomTypeDetails().getRoomType())) {
				RoomType roomType = hotelRates.getRoomTypeDetails().getRoomType().values().stream().findFirst().orElse(null);
				if (roomType != null && MapUtils.isNotEmpty(roomType.getRatePlanList())) {
					RatePlan ratePlan = roomType.getRatePlanList().values().stream().findFirst().orElse(null);
					if (ratePlan != null && ratePlan.freeChildCount > 0 && StringUtils.isNotEmpty(ratePlan.getFreeChildText())) {
						freeChildText = ratePlan.getFreeChildText();
					}
				}
			}
		}
		return freeChildText;
	}

	private String buildPropertySellableType(RoomDetailsResponse roomDetailsResponse) {
		String propertySellableType = "Stay";
		if (utility.isPropertyHotelOrResort(roomDetailsResponse.getHotelRates())){
			propertySellableType = "Room";
		}
		return propertySellableType;
	}

	private SelectRoomImpInfo getImpInfo(List<RecommendedCombo> recommendedCombos, List<RoomStayCandidate> roomStayCandidates) {
		if (CollectionUtils.isEmpty(roomStayCandidates) || CollectionUtils.isEmpty(recommendedCombos)) {
			return null;
		}

		int requestedRooms = roomStayCandidates.size();
		StringBuilder sb = new StringBuilder();
		List<RoomTariff> requestedRoomTariff = new ArrayList<>();
		for(RoomStayCandidate rsc : roomStayCandidates){
			RoomTariff rt = new RoomTariff();
			rt.setNumberOfAdults(rsc.getAdultCount());
			rt.setNumberOfChildren(CollectionUtils.isNotEmpty(rsc.getChildAges()) ? rsc.getChildAges().size():0);
			requestedRoomTariff.add(rt);
		}

		boolean roomCountMatched = false;
		for(RecommendedCombo recommendedCombo: recommendedCombos){
			int roomSize = 0;
			requestedRoomTariff.forEach(a-> a.setRoomCount(null));
			for(RoomDetails roomDetail: recommendedCombo.getRooms()){
				for(SelectRoomRatePlan selectRoomRatePlan: roomDetail.getRatePlans()){
					for(Tariff tariff: selectRoomRatePlan.getTariffs()){
						if(CollectionUtils.isNotEmpty(tariff.getRoomTariffs())){
							roomSize += tariff.getRoomTariffs().size();
						}
						int index = 0;
						for(RoomTariff rt : tariff.getRoomTariffs()){
							//pick from recommended combo and fill in non null room count of requested occupancy, if all requested occupancy has a room count set, that means occupancy has matched, else one or more of them will remain null which means mismatch
							Optional<RoomTariff> ort =  requestedRoomTariff.stream().filter(a-> a.getNumberOfAdults() == rt.getNumberOfAdults() && a.getNumberOfChildren() == rt.getNumberOfChildren() && a.getRoomCount() == null ).findFirst();
							if(ort.isPresent()){
								ort.get().setRoomCount(index++);
							}
						}
					}
				}
			}

			if(requestedRooms == roomSize && !requestedRoomTariff.stream().filter(a-> a.getRoomCount() == null).findFirst().isPresent()){
				return null;
			}
			//if room has matched but occupancy hasnt then
			if(requestedRooms == roomSize) {
				roomCountMatched = true;
			}
		}
		SelectRoomImpInfo selectRoomImpInfo = new SelectRoomImpInfo();
		selectRoomImpInfo.setTitle(polyglotService.getTranslatedData(ConstantsTranslation.IMPORTANT_TITLE));
		//if room count hasnt matched
		if(!roomCountMatched) {
			sb.append(requestedRooms).append(Constants.SPACE).append(requestedRooms > 1 ? polyglotService.getTranslatedData(ADDITIONAL_FEE_SUBTEXT_ROOMS) : polyglotService.getTranslatedData(ADDITIONAL_FEE_SUBTEXT_ROOM) );
			String message = polyglotService.getTranslatedData(ConstantsTranslation.ROOM_MISMATCH_TEXT).replace("{data}", sb.toString());
			selectRoomImpInfo.setMessage(message);
		} else // if none of the combo matches on room count and occuapancy details then different message
			selectRoomImpInfo.setMessage(polyglotService.getTranslatedData(ConstantsTranslation.OCCUPANCY_MISMATCH_TEXT));
		return selectRoomImpInfo;
	}

	private List<RecommendedCombo> getRecommendedCombos(HotelRates hotelRates, String propertyType,
														HotelsRoomInfoResponseEntity hotelsRoomInfoResponseEntity,
														HotelImage hotelImage,
														String listingType,
														String expData, String askedCurrency, String funnelSource,
														int days, int ap, boolean isBlockPAH,
														Map<String, JsonNode> roomPersuasionMap,
														CommonModifierResponse commonModifierResponse, Map<String, String> ratePlanCodeAndNameMap, boolean isLuxeHotel,
														boolean isAltAccoHotel,boolean isOHSExpEnable, InstantFareInfo instantFareInfo, String corpAlias,
														final MarkUpDetails markUpDetails, Double foodRating, String siteDomain, String selectedRoomCode,
														String selectedRateplanCode, boolean isHighSellingAltAcco) {
		if (hotelRates.getRecommendedRoomTypeDetails()==null && hotelRates.getRoomTypeDetails() == null) {
			return null;
		}

		List<RecommendedCombo> recommendedCombos = new ArrayList<>();
		List<RoomDetails> roomDetails;
		NoCostEmiDetails noCostEmiDetailForRootLevel = new NoCostEmiDetails();

		if (hotelRates.getRecommendedRoomTypeDetails() != null) {
			/* Case 1 : Make Combo from RecommendedRoomTypeDetails */
			roomDetails = getRooms(
					hotelRates.getRecommendedRoomTypeDetails(), hotelsRoomInfoResponseEntity, hotelImage, listingType,
					expData, true, askedCurrency,funnelSource, hotelRates.getWalletSurge(),
					hotelRates.getSegments(), days, hotelRates.getStarRating(), ap,isBlockPAH, roomPersuasionMap,
					commonModifierResponse, false, ratePlanCodeAndNameMap, isLuxeHotel, isAltAccoHotel,
					hotelRates.isUserGCCAndMmtExclusive(), hotelRates.isGroupBookingPrice(),isOHSExpEnable,
					true, instantFareInfo, corpAlias,false, propertyType,
					hotelRates.getCountryCode(), markUpDetails, foodRating,  false, siteDomain,
					selectedRoomCode, selectedRateplanCode, false, hotelRates,false, isHighSellingAltAcco
			);

			String sellableType = Constants.SELLABLE_ROOM_TYPE;
			if (CollectionUtils.isNotEmpty(roomDetails) && CollectionUtils.isNotEmpty(roomDetails.get(0).getRatePlans())) {
				sellableType = roomDetails.get(0).getRatePlans().get(0).getSellableType();
			}
			boolean newPropertyOfferApplicable = commonModifierResponse!=null && MapUtils.isNotEmpty(commonModifierResponse.getExpDataMap()) ? utility.isExperimentOn(commonModifierResponse.getExpDataMap(), ExperimentKeys.NEW_PROPERTY_OFFER.getKey()) : false;

			RecommendedCombo recommendedCombo = buildBasicRecommendedCombo(roomDetails, utility.getComboName(hotelRates.getRecommendedRoomTypeDetails().getComboMealPlan()),
					hotelRates.getRecommendedRoomTypeDetails().isStaycationDeal(), true, funnelSource, hotelRates.getRecommendedRoomTypeDetails().getOccupancyDetails());
			boolean myPartner = Objects.nonNull(commonModifierResponse) && Objects.nonNull(commonModifierResponse.getExtendedUser()) && Utility.isMyPartnerRequest(commonModifierResponse.getExtendedUser().getProfileType(), commonModifierResponse.getExtendedUser().getAffiliateId());
			if(null == recommendedCombo.getComboTariff())
				recommendedCombo.setComboTariff(new Tariff());
			recommendedCombo.setCorpApprovalInfo(commonResponseTransformer.buildCorpApprovalInfo(hotelRates.getRecommendedRoomTypeDetails().getTotalDisplayFare().getCorpMetaData(), utility.isTcsV2FlowEnabled(expData)));
			recommendedCombo.getComboTariff().setPriceMap(commonResponseTransformer.getPriceMap(hotelRates.getRecommendedRoomTypeDetails().getTotalDisplayFare().getDisplayPriceBreakDown(),
					hotelRates.getRecommendedRoomTypeDetails().getTotalDisplayFare().getDisplayPriceBreakDownList(), expData,
					hotelRates.getRecommendedRoomTypeDetails().getOccupancyDetails()!=null?hotelRates.getRecommendedRoomTypeDetails().getOccupancyDetails().getNumOfRooms():null,
					askedCurrency, sellableType, days, false, "", utility.buildToolTip(funnelSource),
					Utility.isGroupBookingFunnel(funnelSource), hotelRates.isGroupBookingPrice(),myPartner,isAltAccoHotel, markUpDetails,
					noCostEmiDetailForRootLevel, null, newPropertyOfferApplicable, isHighSellingAltAcco));
            recommendedCombo.getComboTariff().setEmiPlanDetail(commonResponseTransformer.buildEmiPlanDetails(noCostEmiDetailForRootLevel));
            recommendedCombo.getComboTariff().setDefaultPriceKey(hotelRates.getRecommendedRoomTypeDetails().getTotalDisplayFare().getDisplayPriceBreakDown() != null ? (hotelRates.getRecommendedRoomTypeDetails().getTotalDisplayFare().getDisplayPriceBreakDown().getCouponInfo()!=null?
					hotelRates.getRecommendedRoomTypeDetails().getTotalDisplayFare().getDisplayPriceBreakDown().getCouponInfo().getCouponCode(): "DEFAULT"): null);
			if (hotelRates.getRecommendedRoomTypeDetails().getOccupancyDetails()!=null) {
				recommendedCombo.getComboTariff().setOccupancydetails(new RoomTariff());
				recommendedCombo.getComboTariff().getOccupancydetails().setNumberOfAdults(hotelRates.getRecommendedRoomTypeDetails().getOccupancyDetails().getAdult());
				recommendedCombo.getComboTariff().getOccupancydetails().setNumberOfChildren(hotelRates.getRecommendedRoomTypeDetails().getOccupancyDetails().getChild());
				if (hotelRates.getRecommendedRoomTypeDetails().getOccupancyDetails().getChild()>0)
					recommendedCombo.getComboTariff().getOccupancydetails().setChildAges(hotelRates.getRecommendedRoomTypeDetails().getOccupancyDetails().getChildAges());
				recommendedCombo.getComboTariff().getOccupancydetails().setRoomCount(hotelRates.getRecommendedRoomTypeDetails().getOccupancyDetails().getNumOfRooms());
			}
			recommendedCombo.setPaymentPlan(commonResponseTransformer.buildPaymentPlan(hotelRates.getRecommendedRoomTypeDetails().getPaymentPlan()));

			double baseComboFare = 0.0;
			if (utility.isGroupBookingFunnel(funnelSource)) {
				recommendedCombo.setBaseCombo(true);
				baseComboFare = hotelRates.getRecommendedRoomTypeDetails().getTotalDisplayFare().getDisplayPriceBreakDown().getDisplayPrice();
				recommendedCombo.setComboText("<b>"+recommendedCombo.getComboName()+"</b>");
			}
			// Free stay for X children sent as childOccupancyMsg for the combo
			if (hotelRates.getRecommendedRoomTypeDetails().getFreeChildCount() > 0 && StringUtils.isNotEmpty(hotelRates.getRecommendedRoomTypeDetails().getFreeChildText())) {
				recommendedCombo.setChildOccupancyMsg(hotelRates.getRecommendedRoomTypeDetails().getFreeChildText());
			}
			if (hotelRates.getRecommendedRoomTypeDetails().getFlexiCancelRoomDetail() != null && MapUtils.isNotEmpty(hotelRates.getRecommendedRoomTypeDetails().getFlexiCancelRoomDetail().getAddOnDetails())) {
				recommendedCombo.setAddOnDetails(buildAddOnDetails(hotelRates.getRecommendedRoomTypeDetails().getFlexiCancelRoomDetail().getAddOnDetails(),
						expData, null != hotelRates.getRecommendedRoomTypeDetails().getOccupancyDetails() ? hotelRates.getRecommendedRoomTypeDetails().getOccupancyDetails().getNumOfRooms() : null,
						days, sellableType, utility.isGroupBookingFunnel(funnelSource), isAltAccoHotel, isHighSellingAltAcco));
			}
			recommendedCombos.add(recommendedCombo);

			if (CollectionUtils.isNotEmpty(hotelRates.getOtherRecommendedRooms())) {
				/* Case 2 : Make Combo from OtherRecommendedRooms */
				for (RoomTypeDetails otherRecommendation: hotelRates.getOtherRecommendedRooms()) {
					roomDetails = getRooms(
							otherRecommendation, hotelsRoomInfoResponseEntity, hotelImage, listingType, expData,
							true, askedCurrency,funnelSource, null,null, days, hotelRates.getStarRating(),
							ap , isBlockPAH, roomPersuasionMap, commonModifierResponse, false, ratePlanCodeAndNameMap,
							isLuxeHotel, isAltAccoHotel, hotelRates.isUserGCCAndMmtExclusive(), hotelRates.isGroupBookingPrice(),
							isOHSExpEnable,true, instantFareInfo, corpAlias,false,propertyType,
							hotelRates.getCountryCode(), markUpDetails, foodRating, false, siteDomain, selectedRoomCode,
							selectedRateplanCode, false, hotelRates, false, isHighSellingAltAcco);

					if (CollectionUtils.isNotEmpty(roomDetails) && CollectionUtils.isNotEmpty(roomDetails.get(0).getRatePlans())) {
						sellableType = roomDetails.get(0).getRatePlans().get(0).getSellableType();
					} else {
						sellableType = Constants.SELLABLE_ROOM_TYPE;
					}
					recommendedCombo = buildBasicRecommendedCombo(roomDetails, utility.getComboName(hotelRates.getRecommendedRoomTypeDetails().getComboMealPlan()), otherRecommendation.isStaycationDeal(), false, funnelSource, otherRecommendation.getOccupancyDetails());
					if(null == recommendedCombo.getComboTariff())
						recommendedCombo.setComboTariff(new Tariff());
					recommendedCombo.setCorpApprovalInfo(commonResponseTransformer.buildCorpApprovalInfo(otherRecommendation.getTotalDisplayFare().getCorpMetaData(), utility.isTcsV2FlowEnabled(expData)));
					recommendedCombo.getComboTariff().setPriceMap(commonResponseTransformer.getPriceMap(otherRecommendation.getTotalDisplayFare().getDisplayPriceBreakDown(),
							otherRecommendation.getTotalDisplayFare().getDisplayPriceBreakDownList(), expData,
							otherRecommendation.getOccupancyDetails()!=null?otherRecommendation.getOccupancyDetails().getNumOfRooms():null,
							askedCurrency, sellableType, days, false,"", utility.buildToolTip(funnelSource), utility.isGroupBookingFunnel(funnelSource), hotelRates.isGroupBookingPrice(),myPartner,isAltAccoHotel, markUpDetails,
							null, null, newPropertyOfferApplicable, isHighSellingAltAcco));
					recommendedCombo.getComboTariff().setDefaultPriceKey(otherRecommendation.getTotalDisplayFare().getDisplayPriceBreakDown() != null ? (otherRecommendation.getTotalDisplayFare().getDisplayPriceBreakDown().getCouponInfo()!=null?
							otherRecommendation.getTotalDisplayFare().getDisplayPriceBreakDown().getCouponInfo().getCouponCode(): "DEFAULT"): null);
					if (otherRecommendation.getOccupancyDetails()!=null) {
						recommendedCombo.getComboTariff().setOccupancydetails(new RoomTariff());
						recommendedCombo.getComboTariff().getOccupancydetails().setNumberOfAdults(otherRecommendation.getOccupancyDetails().getAdult());
						recommendedCombo.getComboTariff().getOccupancydetails().setNumberOfChildren(otherRecommendation.getOccupancyDetails().getChild());
						if (otherRecommendation.getOccupancyDetails().getChild()>0)
							recommendedCombo.getComboTariff().getOccupancydetails().setChildAges(otherRecommendation.getOccupancyDetails().getChildAges());
						recommendedCombo.getComboTariff().getOccupancydetails().setRoomCount(otherRecommendation.getOccupancyDetails().getNumOfRooms());
					}
					recommendedCombo.setPaymentPlan(commonResponseTransformer.buildPaymentPlan(otherRecommendation.getPaymentPlan()));
					if (utility.isGroupBookingFunnel(funnelSource)) {
						double otherRecommendationDisplayPrice = otherRecommendation.getTotalDisplayFare().getDisplayPriceBreakDown().getDisplayPrice();
						long differenceInPriceFromBaseCombo = (long) (otherRecommendationDisplayPrice - baseComboFare);
						if(differenceInPriceFromBaseCombo != 0) {
							recommendedCombo.setComboText(getComboText(otherRecommendation.getComboMealPlan(), differenceInPriceFromBaseCombo));
						}
					}
					// Free stay for X children sent as childOccupancyMsg for the combo
					if (otherRecommendation.getFreeChildCount() > 0 && StringUtils.isNotEmpty(otherRecommendation.getFreeChildText())) {
						recommendedCombo.setChildOccupancyMsg(otherRecommendation.getFreeChildText());
					}
					if (otherRecommendation.getFlexiCancelRoomDetail() != null && MapUtils.isNotEmpty(otherRecommendation.getFlexiCancelRoomDetail().getAddOnDetails())) {
						recommendedCombo.setAddOnDetails(buildAddOnDetails(otherRecommendation.getFlexiCancelRoomDetail().getAddOnDetails(), expData, null != otherRecommendation.getOccupancyDetails() ? otherRecommendation.getOccupancyDetails().getNumOfRooms() : null, days, sellableType,
								utility.isGroupBookingFunnel(funnelSource), isAltAccoHotel, isHighSellingAltAcco));
					}
					recommendedCombos.add(recommendedCombo);
				}
			}
		} else if (hotelRates.getRoomTypeDetails() != null) {
			/* Case 3 : Make recommended combos out of the exact matched Rooms */
			roomDetails = createExactCombo(
					hotelRates.getRoomTypeDetails(), hotelsRoomInfoResponseEntity, hotelImage, listingType, expData,
					askedCurrency, funnelSource, days, hotelRates.getStarRating(), ap, isBlockPAH,
					commonModifierResponse,roomPersuasionMap, ratePlanCodeAndNameMap, isLuxeHotel,
					isAltAccoHotel, hotelRates.isUserGCCAndMmtExclusive(), hotelRates.isGroupBookingPrice(),
					isOHSExpEnable, corpAlias, hotelRates.getCountryCode(), markUpDetails, foodRating, hotelRates);
			for (RoomDetails roomDetail: roomDetails) {
				List<RoomDetails> roomDetailList = Collections.singletonList(roomDetail);
				RecommendedCombo recommendedCombo = buildBasicRecommendedCombo(roomDetailList, null, roomDetail.getRatePlans().get(0).isStaycationDeal(),
						false, funnelSource, null);
				if (null == recommendedCombo.getComboTariff())
					recommendedCombo.setComboTariff(new Tariff());
				recommendedCombo.getComboTariff().setPriceMap(roomDetail.getRatePlans().get(0).getTariffs().get(0).getPriceMap());
				recommendedCombo.getComboTariff().setEmiPlanDetail(commonResponseTransformer.buildEmiPlanDetails(noCostEmiDetailForRootLevel));
				recommendedCombo.getComboTariff().setDefaultPriceKey(roomDetail.getRatePlans().get(0).getTariffs().get(0).getDefaultPriceKey());
				if (roomDetail.getRatePlans().get(0).getTariffs().get(0).getOccupancydetails() != null) {
					recommendedCombo.getComboTariff().setOccupancydetails(new RoomTariff());
					recommendedCombo.getComboTariff().getOccupancydetails().setNumberOfAdults(roomDetail.getRatePlans().get(0).getTariffs().get(0).getOccupancydetails().getNumberOfAdults());
					recommendedCombo.getComboTariff().getOccupancydetails().setNumberOfChildren(roomDetail.getRatePlans().get(0).getTariffs().get(0).getOccupancydetails().getNumberOfChildren());
					if (roomDetail.getRatePlans().get(0).getTariffs().get(0).getOccupancydetails().getNumberOfChildren() > 0)
						recommendedCombo.getComboTariff().getOccupancydetails().setChildAges(roomDetail.getRatePlans().get(0).getTariffs().get(0).getOccupancydetails().getChildAges());
					recommendedCombo.getComboTariff().getOccupancydetails().setRoomCount(roomDetail.getRatePlans().get(0).getTariffs().get(0).getOccupancydetails().getRoomCount());
				}
				recommendedCombo.setCorpApprovalInfo(roomDetail.getRatePlans().get(0).getCorpApprovalInfo());
				recommendedCombos.add(recommendedCombo);
			}
		}
		return recommendedCombos;
	}

	private Map<String, AddOnDetails> buildAddOnDetails(Map<String, FlexiCancelAddOnDetails> flexiCancelRoomDetail, String expData, Integer roomCount,
														int nightCount, String sellableType, boolean groupBookingFunnel, boolean isAltAccoHotel, boolean isHighSellingAltAcco) {
		Map<String, AddOnDetails> addOnDetailsMap = null;
		if (flexiCancelRoomDetail.containsKey(FLEXI_CANCEL) && flexiCancelRoomDetail.get(FLEXI_CANCEL) != null) {
			String priceDisplayMessage = (null == roomCount) ? null : commonResponseTransformer.getPriceDisplayMessage(expData, roomCount, sellableType, nightCount,
					groupBookingFunnel, isAltAccoHotel, isHighSellingAltAcco);
			addOnDetailsMap = new HashMap<>();
			AddOnDetails addOnDetails = new AddOnDetails();
			BeanUtils.copyProperties(flexiCancelRoomDetail.get(FLEXI_CANCEL), addOnDetails);
			PolicyDetails appiledPolicyDetails = new PolicyDetails();
			BeanUtils.copyProperties(flexiCancelRoomDetail.get(FLEXI_CANCEL).getApplied(), appiledPolicyDetails);
			String appliedText= polyglotService.getTranslatedData(ConstantsTranslation.FLEXI_CANCEL_DETAIL_SELECTED_PER_NIGHT_TEXT);
			if (StringUtils.isNotEmpty(appliedText)) {
				appliedText = MessageFormat.format(appliedText, priceDisplayMessage);
				appiledPolicyDetails.setPriceDescription(appliedText);
			}
			addOnDetails.setApplied(appiledPolicyDetails);

			PolicyDetails removePolicyDetails = new PolicyDetails();
			BeanUtils.copyProperties(flexiCancelRoomDetail.get(FLEXI_CANCEL).getRemoved(), removePolicyDetails);
			removePolicyDetails.setPriceDescription(priceDisplayMessage);
			addOnDetails.setRemoved(removePolicyDetails);
			addOnDetailsMap.put(FLEXI_CANCEL, addOnDetails);
		}
		return addOnDetailsMap;
	}

	private RecommendedCombo buildBasicRecommendedCombo(List<RoomDetails> roomDetailsList, String comboName, boolean isStaycationDeal, boolean baseCombo, String funnelSource, OccupancyDetails occupancyDetails) {
		RecommendedCombo recommendedCombo = new RecommendedCombo();
		recommendedCombo.setComboId(UUID.randomUUID().toString());
		recommendedCombo.setComboName(comboName);
		recommendedCombo.setRooms(roomDetailsList);
		recommendedCombo.setStaycationDeal(isStaycationDeal);
		boolean isSameCancellationPolicyInAllRatePlans = true;
		boolean isSameMealPlan = true;
		String cancellationType = null;
		int totalTarrifs = 0;
		String mealTypeCode = null;
		String sellableType = null;
		String mealTypeCodeText = "";
		Set<String> mealInclusionCodeList = new HashSet<>();
		boolean index = false;
		//set campaign alert for recommended combo
		for (RoomDetails roomDetails : roomDetailsList) {
			Optional<SelectRoomRatePlan> selectRoomRatePlan = roomDetails.getRatePlans().stream().findFirst();
			if(selectRoomRatePlan.isPresent() && selectRoomRatePlan.get().getCancellationPolicy()!=null){
				// we will find the value of first element of cancellation Policy and Meal Inclusion
				cancellationType = selectRoomRatePlan.get().getCancellationPolicy().getType().name();
			}
			recommendedCombo.setRoomPersuasions(roomDetails.getRoomPersuasions());
			for (SelectRoomRatePlan ratePlan : roomDetails.getRatePlans()) {
				if(ratePlan!=null) {
					if (null != ratePlan.getCancellationPolicy()
							&& BookedCancellationPolicyType.FC == ratePlan.getCancellationPolicy().getType()
							&& CollectionUtils.isNotEmpty(ratePlan.getTariffs())) {
						for (Tariff tariff : ratePlan.getTariffs()) {
							if (null == tariff.getCampaignAlert()) {
								break;
							}
						}
						if (null == recommendedCombo.getComboTariff())
							recommendedCombo.setComboTariff(new Tariff());
						recommendedCombo.getComboTariff().setCampaignAlert(ratePlan.getTariffs().get(0).getCampaignAlert());
					}
					if (ratePlan.getCancellationPolicy() != null) {
						// to check if all the values of cancellation policy are same for all rate plans
						isSameCancellationPolicyInAllRatePlans = isSameCancellationPolicyInAllRatePlans && cancellationType.equalsIgnoreCase(ratePlan.getCancellationPolicy().getType().name());
						cancellationType = ratePlan.getCancellationPolicy().getType().name();
					}
					if (CollectionUtils.isNotEmpty(ratePlan.getTariffs())) {
						totalTarrifs += getRoomTariffInThisRatePlanCount(ratePlan.getTariffs());
					}
					if (CollectionUtils.isNotEmpty(ratePlan.getInclusionsList())) {
//						this function is to check if all the rooms has same meal inclusion in all the rooms of combo
						for (BookedInclusion inclusion : ratePlan.getInclusionsList()) {
							if (inclusion != null && MEAL.equalsIgnoreCase(inclusion.getCategory()) && StringUtils.isNotEmpty(inclusion.getInclusionCode())) {
								mealInclusionCodeList.add(inclusion.getInclusionCode());
							}
						}
					}
					if(!index){
						sellableType = roomDetails.getRoomCategoryText()!=null?roomDetails.getRoomCategoryText():SELLABLE_ROOM_TYPE;
						index = true;
					}
					if(StringUtils.isNotEmpty(ratePlan.getName())){
						String[] arr = ratePlan.getName().split("\\|");
						if (arr != null && arr.length > 1) {
							mealTypeCodeText = arr[1];
						}
						if(StringUtils.isEmpty(mealTypeCodeText)){
							arr = ratePlan.getName().split(WITH);
							if (arr != null && arr.length > 1) {
								mealTypeCodeText = arr[1];
							}
						}
					}
				}
			}
			buildGroupBookingComboText(roomDetails, recommendedCombo, baseCombo, funnelSource, occupancyDetails);
		}
		try {
			recommendedCombo.setComboTitle(getComboTitle(isSameCancellationPolicyInAllRatePlans, cancellationType, totalTarrifs,mealInclusionCodeList,mealTypeCode ,sellableType, mealTypeCodeText));
		} catch (Exception e){
			LOGGER.warn("Error occured in building Combo Title");
		}
		return recommendedCombo;
	}

	private String getComboTitle(boolean isSameCancellationPolicyInAllRatePlans, String cancellationType, int totalTarrifs, Set<String> mealInclusionCodeList, String mealTypeCode, String sellableType, String mealTypeCodeText){
		/* Creation of Polyglot key begins */
		String comboTitle = ROOMS_COMBO;
		if(totalTarrifs == 1){
			comboTitle = SINGLE_ + comboTitle;
		} else {
			comboTitle = PLURAL_ + comboTitle;
		}
		comboTitle += (isSameCancellationPolicyInAllRatePlans && CANCELLATION_TYPE_FC.equalsIgnoreCase(cancellationType))?_FC:_NR;
		String roomTextFromPolyglot = polyglotService.getTranslatedData(comboTitle);
		comboTitle = roomTextFromPolyglot!=null?roomTextFromPolyglot:"";
		comboTitle = comboTitle.replace("{total_tarrifs}", String.valueOf(totalTarrifs));
		comboTitle = comboTitle.replace("{sellable_type}", StringUtils.capitalize(sellableType));
		mealTypeCode = mealInclusionCodeList.size()==1?mealInclusionCodeList.stream().findFirst().get():"";
		mealTypeCode = (CollectionUtils.isNotEmpty(mealPlanCodeList) && mealPlanCodeList.contains(mealTypeCode))?COMBO_TITLE + mealTypeCode:mealTypeCode;
		String mealTextFromPolyglot =  polyglotService.getTranslatedData(mealTypeCode);
		comboTitle += (StringUtils.isNotEmpty(mealTextFromPolyglot))?PIPE_SEPARATOR + mealTextFromPolyglot:"";
		if(!Constants.DEVICE_OS_DESKTOP.equalsIgnoreCase(MDC.get(MDCHelper.MDCKeys.CLIENT.getStringValue()))) {
			comboTitle += (StringUtils.isNotEmpty(mealTypeCodeText)) ? PIPE_SEPARATOR + mealTypeCodeText : "";
		}
		/* Creation of polyglot and getting data from polyglot*/
		return comboTitle;
	}

	int getRoomTariffInThisRatePlanCount(List<Tariff> tariffList){
		for(Tariff tariff: tariffList){
			if(tariff!=null && CollectionUtils.isNotEmpty(tariff.getRoomTariffs())){
				return tariff.getRoomTariffs().size();
			}
		}
		return 0;
	}

	private List<RoomDetails> createExactCombo(RoomTypeDetails roomTypeDetails, HotelsRoomInfoResponseEntity hotelsRoomInfoResponseEntity,
											   HotelImage hotelImage, String listingType, String expData, String askedCurrency,
											   String funnelSource, int days, Integer hotelStarRating, int ap, boolean isBlockPAH,
											   CommonModifierResponse commonModifierResponse, Map<String, JsonNode> roomPersuasionMap,
											   Map<String, String> ratePlanCodeAndNameMap, boolean isLuxeHotel, boolean isAltAccoHotel,
											   boolean isUserGCCAndMmtExclusive, boolean groupBookingPrice, boolean isOHSExpEnable,
											   String corpAlias, String countryCode, final MarkUpDetails markUpDetails,
											   Double foodRating, HotelRates hotelRates) {

		if (roomTypeDetails == null)
			return null;
		boolean isNewDetailPageDesktop = commonModifierResponse != null && utility.isExperimentOn(commonModifierResponse.getExpDataMap(), NEW_DETAIL_PAGE_DESKTOP_EXP);
		/* Make a <RoomCode,ImageURLsList> map */
		Map<String, List<String>> roomImageMap = new HashMap<>();
		if (hotelImage != null && hotelImage.getImageDetails()!= null && MapUtils.isNotEmpty(hotelImage.getImageDetails().getProfessional())
				&& hotelImage.getImageDetails().getProfessional().containsKey("R")) {
			List<ProfessionalImageEntity> images  = hotelImage.getImageDetails().getProfessional().get("R");
			if (CollectionUtils.isNotEmpty(images)) {
				images.forEach( image -> {
					if (StringUtils.isNotBlank(image.getUrl())) {
						roomImageMap.computeIfAbsent(image.getCatCode(), k -> new ArrayList<>());
						roomImageMap.get(image.getCatCode()).add(image.getUrl().startsWith("http") ? image.getUrl() : "https:" + image.getUrl());
					}
				});
			}
		}
		/* Make a <RoomCode,ImageURLsList> map */
		boolean isRO=false, isBF=false, is2Meals=false, isAllMeals=false;
		List<RoomDetails> roomDetail = new ArrayList<>();
		for (String roomCode: roomTypeDetails.getRoomType().keySet()) {
			if(isRO && isBF && is2Meals && isAllMeals)
				break;
			for (String ratePlanCode: roomTypeDetails.getRoomType().get(roomCode).getRatePlanList().keySet()) {
				if(isRO && isBF && is2Meals && isAllMeals)
					break;
				RatePlan ratePlan = roomTypeDetails.getRoomType().get(roomCode).getRatePlanList().get(ratePlanCode);
				String mealPlan = ratePlan.getMealPlans().get(0).getCode();
				String mealPlanCode = Utility.checkMealPlan(mealPlan);
				if("RO".equalsIgnoreCase(mealPlanCode)) {
					if(isRO)
						continue;
					else
						isRO= true;
				}
				if("CP".equalsIgnoreCase(mealPlanCode)){
					if(isBF)
						continue;
					else
						isBF= true;
				}
				if( "MAP".equalsIgnoreCase(mealPlanCode)){
					if(is2Meals)
						continue;
					else
						is2Meals= true;
				}
				if( "AP".equalsIgnoreCase(mealPlanCode)){
					if(isAllMeals)
						continue;
					else
						isAllMeals= true;
				}

				ratePlan.setRatePlanCode(ratePlanCode);
				RoomDetails roomDetails = new RoomDetails();
				roomDetails.setRoomCode(roomCode);
				roomDetails.setRoomName(roomTypeDetails.getRoomType().get(roomCode).getRoomTypeName());
				Map<String, RoomInfo> staticRoomInfoMap = new HashMap<>();
				if(null != hotelsRoomInfoResponseEntity)
					staticRoomInfoMap = hotelsRoomInfoResponseEntity.getHtlRmInfo().get(0).getHotelRoomInfo();
				if(MapUtils.isNotEmpty(roomPersuasionMap)){
					roomDetails.setRoomPersuasions(roomPersuasionMap.get(roomCode));
				}
				RoomInfo roomInfo = staticRoomInfoMap.get(roomCode);

				if (roomInfo != null) {
					boolean amendRoomHighlights = utility.showHighlightsForRoomAmenities(countryCode, funnelSource);
					roomDetails.setAmenities(commonResponseTransformer.buildAmenities(roomInfo.getFacilityWithGrp(), roomInfo.getStarFacilities(), roomInfo.getHighlightedFacilities(), amendRoomHighlights));
					roomDetails.setHighlightedAmenities(commonResponseTransformer.buildHighlightedAmenities(roomInfo.getFacilityHighlights()));
					roomDetails.setMaster(roomInfo.isMaster());
					roomDetails.setMaxAdult(roomInfo.getMaxAdultCount());
					roomDetails.setMaxChild(roomInfo.getMaxChildCount());
					roomDetails.setMaxGuest(roomInfo.getMaxGuestCount());
					/* Below three nodes are duplicate & need to be removed after next client release */
					/* Only Temp addition to fix live bug */
					roomDetails.setMaxGuestCount(roomInfo.getMaxGuestCount());
					roomDetails.setMaxAdultCount(roomInfo.getMaxAdultCount());
					roomDetails.setMaxChildCount(roomInfo.getMaxChildCount());
					/* Above three nodes are duplicate & need to be removed after next client release */
					roomDetails.setBedCount(roomInfo.getBedCount());
					roomDetails.setBathroomCount(roomInfo.getBathroomCount());
					if (StringUtils.isNotBlank(roomInfo.getBedRoomCount()))
						roomDetails.setBedroomCount(NumberUtils.toInt(roomInfo.getBedRoomCount(),0));
					roomDetails.setParentRoomCode(roomInfo.getParentRoomCode());
					roomDetails.setRoomSize(roomInfo.getRoomSize());
					roomDetails.setRoomViewName(roomInfo.getRoomViewName());
					boolean pilgrimageBedInfoEnable = utility.isExperimentOn(utility.getExpDataMap(expData), ExperimentKeys.PILGR_IMAGE_BED_INFO.getKey());
					roomDetails.setRoomHighlights(getRoomHighlights(roomInfo,ratePlan.getExtraGuestDetail(), isAltAccoHotel,isOHSExpEnable,roomDetails.getAmenities(), countryCode, amendRoomHighlights, pilgrimageBedInfoEnable));
				}
				String sellableType = roomTypeDetails.getRoomType().get(roomCode).getSellableType();
				if (StringUtils.isEmpty(sellableType))
					sellableType = Constants.SELLABLE_ROOM_TYPE;
				String roomName = roomTypeDetails.getRoomType().get(roomCode).getRoomTypeName();
				roomDetails.setRatePlans(getRatePlans(
						ratePlan, listingType, expData, true, askedCurrency, sellableType, funnelSource,
						days, ap , isBlockPAH, commonModifierResponse, ratePlanCodeAndNameMap, isLuxeHotel,
						groupBookingPrice,isAltAccoHotel, corpAlias, markUpDetails, foodRating, false, roomName,
						hotelRates));
				roomDetails.setClientViewType(getTariffViewType(roomDetails,hotelStarRating, roomDetail.size() < 1,expData));
				roomDetails.setImages(roomImageMap.get(roomCode));
				roomDetails.setDescription(roomInfo != null ? roomInfo.getDescription() : null);
				roomDetails.setRoomSummary(roomInfo != null ? roomInfo.getRoomSummary() : null);
				roomDetails.setMedia(populateMedia(staticRoomInfoMap, roomDetails.getImages() , roomCode));
				if (roomDetails.getRoomSummary() != null && roomDetails.getRoomSummary().isTopRated() && (!hotelRates.isAltAcco() || !DOM_COUNTRY.equalsIgnoreCase(countryCode))) {
					PersuasionObject persuasionObject =  createTopRatedPersuasion(isNewDetailPageDesktop);
					addPersuasion(roomDetails, persuasionObject);
				}
				if(commonModifierResponse != null && commonModifierResponse.isLiteResponse()){
					utility.removeUnwantedPersuasions(roomDetails);
				}
				if((hotelsRoomInfoResponseEntity != null && CollectionUtils.isNotEmpty(hotelsRoomInfoResponseEntity.getHtlRmInfo()) && hotelsRoomInfoResponseEntity.getHtlRmInfo().get(0).isSpaceDetailsRequired()) && !hotelRates.isHighSellingAltAcco()) {
					if (commonModifierResponse != null && utility.isExperimentOn(commonModifierResponse.getExpDataMap(), EXP_PLV2)) {
						roomDetails.setPrivateSpacesV2(getSpaceDataV2(roomInfo != null ? roomInfo.getPrivateSpaces() : null, true));
					} else {
						roomDetails.setPrivateSpaces(getSpaceData(roomInfo != null ? roomInfo.getPrivateSpaces() : null, roomTypeDetails.getRoomType().get(roomCode), expData));
					}
				}
				roomDetail.add(roomDetails);
			}
		}
		return roomDetail;
	}

	private List<SelectRoomRatePlan> getRatePlans(RatePlan ratePlanHes, String listingType, String expData, boolean ratePlanGroup,
												  String askedCurrency, String sellableType, String funnelSource,
												  int days,int ap, boolean isBlockPAH, CommonModifierResponse commonModifierResponse,
												  Map<String, String> ratePlanCodeAndNameMap, boolean isLuxeHotel, boolean groupBookingPrice,
												  boolean isAltAccoHotel, String corpAlias, final MarkUpDetails markUpDetails,
												  Double foodRating, boolean exactCase, String roomName, HotelRates hotelRates) {
		List<SelectRoomRatePlan> ratePlans = new ArrayList<>();
		boolean enableThemification = commonModifierResponse != null && utility.isExperimentOn(commonModifierResponse.getExpDataMap(), THEMIFICATION_ENABLED);
		SelectRoomRatePlan ratePlan = new SelectRoomRatePlan();
		ratePlan.setRpc(ratePlanHes.getRatePlanCode());
		ratePlan.setFilterCode(getFilterCodes(ratePlanHes,isBlockPAH,ap, commonModifierResponse, isLuxeHotel, roomName));
		ratePlan.setPayMode(ratePlanHes.getPaymentDetails().getPaymentMode().name());
		ratePlan.setInclusionsList(transformInclusions(
				ratePlanHes, mealPlanMapPolyglot, ap, isBlockPAH, expData, foodRating, askedCurrency,
				true, hotelRates));
		ratePlan.setSupplierCode(ratePlanHes.getSupplierDetails().getSupplierCode());
		ratePlan.setCorpApprovalInfo(commonResponseTransformer.buildCorpApprovalInfo(ratePlanHes.getDisplayFare().getCorpMetaData(), utility.isTcsV2FlowEnabled(expData)));
		ratePlan.setAddons(commonResponseTransformer.getAddons(ratePlanHes.getAddOns()));
		ratePlan.setStaycationDeal(ratePlanHes.isStaycationDeal());
		ratePlan.setCheckinPolicy(ratePlanHes.getCheckinPolicy());
        ratePlan.setConfirmationPolicy(ratePlanHes.getConfirmationPolicy());
        ratePlan.setInstantConfirmation(ratePlanHes.getInstantConfirmation());
        ratePlan.setCancellationTimeline(commonResponseTransformer.buildCancellationTimeline(ratePlanHes.getCancellationTimeline(), null));
        ratePlan.setCancellationPolicyTimeline(commonResponseTransformer.buildCancellationPolicyTimeline(ratePlanHes.getCancellationTimeline(), enableThemification, null));
        if (ratePlan.getCancellationTimeline() != null)
            ratePlan.getCancellationTimeline().setTitle(polyglotService.getTranslatedData(ConstantsTranslation.RATEPLAN_CANCELLATION_POLICY));
        ratePlan.setPersuasions(getRatePlanPersuasion(ratePlan, ratePlanHes, funnelSource, commonModifierResponse, isLuxeHotel, corpAlias));
        ratePlan.setSellableType(sellableType);
        boolean myPartner = Objects.nonNull(commonModifierResponse) && Objects.nonNull(commonModifierResponse.getExtendedUser()) && Utility.isMyPartnerRequest(commonModifierResponse.getExtendedUser().getProfileType(), commonModifierResponse.getExtendedUser().getAffiliateId());
		boolean newPropertyOfferApplicable = commonModifierResponse!=null && MapUtils.isNotEmpty(commonModifierResponse.getExpDataMap()) ? utility.isExperimentOn(commonModifierResponse.getExpDataMap(), ExperimentKeys.NEW_PROPERTY_OFFER.getKey()) : false;

		ratePlan.setTariffs(getTariffs(Collections.singletonList(ratePlanHes), expData, askedCurrency, ratePlan.getSellableType(),
				days, funnelSource, groupBookingPrice, myPartner, isAltAccoHotel, markUpDetails, newPropertyOfferApplicable,
				hotelRates.isHighSellingAltAcco()));
        BNPLVariant bnplVariant = ratePlanHes.getBnplVariant();
        // HTL-42803:  TO-DO remove boolean isBnplOneVariant node once BNPLVariant Enum changes are completely live.
        boolean isBnplOneVariant = false;
        Map<String, String> experimentDataMap = utility.getExpDataMap(expData);
        if (MapUtils.isNotEmpty(experimentDataMap)) {
            isBnplOneVariant = experimentDataMap.containsKey(EXP_BNPL_NEW_VARIANT) && Boolean.parseBoolean(experimentDataMap.get(EXP_BNPL_NEW_VARIANT));
        }
        ratePlan.setRatePlanPersuasionsMap(buildRatePlanPersuasionsMap(ratePlanHes, commonModifierResponse, hotelRates.getHeroTierDetails()));
		String partialRefundText = utility.buildPartialRefundDateText(ratePlanHes.getCancellationTimeline());
        ratePlan.setCancellationPolicy(utility.transformCancellationPolicy(ratePlanHes.getCancelPenaltyList(), false, isBnplOneVariant, bnplVariant, null, polyglotService.getTranslatedData(ConstantsTranslation.CANCELLATION_POLICY_NR_NON_INSTANT_TEXT), null, Optional.ofNullable(ratePlanHes.getMpFareHoldStatus()), partialRefundText, false));
		setRatePlanDetailsCta(ratePlanHes, ratePlan);


        ratePlan.setName(utility.getRatePlanName(ratePlanHes.getMealPlans(), ratePlan.getCancellationPolicy(), ratePlan.getSellableType(), listingType, expData));
        ratePlanCodeAndNameMap.put(ratePlanHes.getRatePlanCode(), utility.getRatePlanName(ratePlanHes.getMealPlans(), null, ratePlan.getSellableType(), listingType, expData));
        ratePlan.setReviewDeeplinkUrl(ratePlanHes.getReviewDeeplinkUrl());
		if(StringUtils.isNotEmpty(ratePlan.getReviewDeeplinkUrl()) && !ratePlan.getReviewDeeplinkUrl().contains("mpn"))
		{
			String reviewDeeplinkUrl = ratePlan.getReviewDeeplinkUrl();
			ratePlan.setReviewDeeplinkUrl(reviewDeeplinkUrl.concat(Constants.AND_SEPARATOR + "mpn" + Constants.PR_SEPARATOR + hotelRates.isMaskedPropertyName()));
		}
		setHotelCloudData(ratePlan);
		setHotelCloudPersuasions(ratePlan);
		ratePlan.setAdditionalFees(buildAdditionalFeesForRatePlan(hotelRates, ratePlanHes, commonModifierResponse, experimentDataMap, roomName));
        ratePlans.add(ratePlan);
		return ratePlans;
	}
	private void setHotelCloudData(SelectRoomRatePlan ratePlan) {
		if (Utility.isMyBizRequest() && StringUtils.isNotEmpty(ratePlan.getSegmentId())) {
			ratePlan.setHotelCloudData(buildHotelCloudDataForDetail(HOTEL_CLOUD_RATE_SEGMENT.equals(ratePlan.getSegmentId())));
		}
	}

	private void setHotelCloudPersuasions(SelectRoomRatePlan ratePlan) {
		if (Utility.isMyBizRequest() && StringUtils.isNotEmpty(ratePlan.getSegmentId())
				&& HOTEL_CLOUD_RATE_SEGMENT.equals(ratePlan.getSegmentId())) {
			List<PersuasionResponse> persuasions = new ArrayList<>();
			PersuasionResponse persuasionResponse = new PersuasionResponse();
			persuasionResponse.setId(HOTEL_CLOUD);
			persuasionResponse.setTemplate(IMAGE_TEXT_H);
			Style style = new Style();
			style.setIconHeight(20);
			style.setIconWidth(90);
			persuasionResponse.setStyle(style);
			persuasionResponse.setPlaceholderId(PRICE_TOP);
			persuasionResponse.setIconUrl(HOTEL_CLOUD_PERSUASION_ICON);
			persuasions.add(persuasionResponse);
			ratePlan.setPersuasions(persuasions);
		}
	}
	private HotelCloudData buildHotelCloudDataForDetail(boolean hotelCloud){
		if (!hotelCloud) return null;
		return HotelCloudData.builder()
				.persuasionIcon(HOTEL_CLOUD_DESKTOP_PERSUASION_ICON)
				.persuasionText(polyglotService.getTranslatedData(HOTEL_CLOUD_TITLE_TEXT))
				.build();
	}

	private ResponseContextDetail getContextDetails(HotelRates hotelRates) {
		ResponseContextDetail responseContextDetail = new ResponseContextDetail();
		responseContextDetail.setCurrency(hotelRates.getCurrencyCode());
		responseContextDetail.setMmtHotelCategory(hotelRates.getMmtHotelCategory());
		return responseContextDetail;
	}

	private FeatureFlags getFeatureFlags(HotelRates hotelRates, List<RoomDetails> rooms,LinkedHashMap<String, String> expDataMap) {
		if (hotelRates == null)
			return null;
		FeatureFlags featureFlags = new FeatureFlags();
		featureFlags.setBestPriceGuaranteed(hotelRates.isBestPriceGuaranteed());
		featureFlags.setBnpl(hotelRates.getIsBNPLAvailable());
		featureFlags.setBnplBaseAmount(hotelRates.getBnplBaseAmount());
		featureFlags.setFirstTimeUser(hotelRates.isFirstTimeUser());
		featureFlags.setFreeCancellation(hotelRates.isFreeCancellationAvailable());
		featureFlags.setPahAvailable(hotelRates.getIsPAHAvailable() != null ? hotelRates.getIsPAHAvailable() : false);
		featureFlags.setPahTariffAvailable(hotelRates.getIsPAHTariffAvailable());
		if(Constants.DEVICE_OS_PWA.equalsIgnoreCase(MDC.get(MDCHelper.MDCKeys.CLIENT.getStringValue()))){
			featureFlags.setPwaDetailSelectMerge(hotelRates.isDetailSelectMerge());
		}
		featureFlags.setPahWalletApplicable(hotelRates.isPahWalletApplicable());
		featureFlags.setRequestToBook(hotelRates.isRequestToBook());
		featureFlags.setRtbPreApproved(hotelRates.isRtbPreApproved());
		featureFlags.setRtbAutoCharge(hotelRates.isRtbAutoCharge());
		Set<String> payModes = new HashSet<>();
		if (CollectionUtils.isNotEmpty(rooms)) {
			for (RoomDetails roomDetail : rooms) {
				if (CollectionUtils.isNotEmpty(roomDetail.getRatePlans())) {
					for (SelectRoomRatePlan ratePlan : roomDetail.getRatePlans()) {
						payModes.add(ratePlan.getPayMode());
					}
				}
			}
			if (CollectionUtils.isNotEmpty(payModes))
				featureFlags.setPayModes(payModes);
		}
		if(!utility.isExperimentTrue(expDataMap, Constants.GROUP_FUNNEL_ENHANCEMENT_EXP))
			featureFlags.setGroupBookingPrice(hotelRates.isGroupBookingPrice());
		else
			featureFlags.setGroupBookingPrice(false); // Explicit marking this as false, In case of experiment is groupFunnelEnhancement enabled
		featureFlags.setMaskedPrice(hotelRates.isMaskedPrice());
		featureFlags.setOptimisedSelection(utility.isOHSExpEnable(hotelRates.getPropertyType(),expDataMap));
		featureFlags.setMyPartnerMoveToTdsTaxStructure(MapUtils.isNotEmpty(expDataMap) && TRUE.equalsIgnoreCase(expDataMap.get(MY_PARTNER_MOVE_TO_TDS_TAX_STRUCTURE)));
		if(hotelRates.getMpHotelFareHoldAmount()!=null) {
			featureFlags.setHotelFareHold(true);
		}
		return featureFlags;
	}

	/* This sends out a list of filters in SearchRooms response. This is a superset of all the applicable filters in response */
	private List<RatePlanFilter> getFilters(List<RoomDetails> exactRooms, List<RoomDetails> occupancyRooms,
											List<Filter> filterCriteria, String funnelSource, int ap, boolean isblockPAH,
											BNPLVariant bnplVariant, CommonModifierResponse commonModifierResponse, boolean isLuxeHotel, boolean negotiatedRateFlag, boolean applyFilterToCombo, boolean hideSpecificFilters, String pageContext) {
		if (CollectionUtils.isEmpty(exactRooms) && CollectionUtils.isEmpty(occupancyRooms)) {
			return null;
		}
		List<RoomDetails> rooms = new ArrayList<>();
		if (CollectionUtils.isNotEmpty(exactRooms)) {
			rooms.addAll(exactRooms);
		}
		if (CollectionUtils.isNotEmpty(occupancyRooms)) {
			rooms.addAll(occupancyRooms);
		}

		List<RatePlanFilter> ratePlanFilters = new ArrayList<>();
		boolean pahFilter = false, freeCancellationFilter = false, freeBreakfastFilter = false, fczpnFilter = false, specialDeals = false, staycation = false;
		boolean twomealsFilter = false, allmealsFilter = false, partnerExclusiveFilter = false, packageRoomFilter = false, nonLuxePackageRoomFilter = false;
		boolean contractedFareFilter = false;
		boolean hotelCloudFilter = false;
		boolean mpBookNowFilterFor0 = false;
		boolean mpBookNowFilterFor1 = false;
		boolean instantBookingFilter = false;
		boolean myPartner = Objects.nonNull(commonModifierResponse) && Objects.nonNull(commonModifierResponse.getExtendedUser()) && Utility.isMyPartnerRequest(commonModifierResponse.getExtendedUser().getProfileType(), commonModifierResponse.getExtendedUser().getAffiliateId());
		String region = MDC.get(MDCHelper.MDCKeys.REGION.getStringValue());
		for (RoomDetails roomDetails : rooms) {
			for (SelectRoomRatePlan ratePlan : roomDetails.getRatePlans()) {
				if (CollectionUtils.isNotEmpty(ratePlan.getFilterCode())) {
					if (ratePlan.getFilterCode().contains("FREE_CANCELLATION") && !hideSpecificFilters) {
						freeCancellationFilter = true;
					}
					if (ratePlan.getFilterCode().contains(BOOK_NOW_AT_0) && !hideSpecificFilters) {
						mpBookNowFilterFor0 = true;
					}
					if (ratePlan.getFilterCode().contains(BOOK_NOW_AT_1) && !hideSpecificFilters) {
						mpBookNowFilterFor1 = true;
					}
					if (ratePlan.getFilterCode().contains("FREE_BREAKFAST"))
						freeBreakfastFilter = true;
					if(!myPartner) {
						if (ratePlan.getFilterCode().contains("PAH")) {
							if (isblockPAH && ap < 5) {
								fczpnFilter = true;
							} else {
								pahFilter = true;
							}
						}
						if (ratePlan.getFilterCode().contains("FCZPN") && !hideSpecificFilters)
							fczpnFilter = true;
						if (ratePlan.getFilterCode().contains("SPECIALDEALS"))
							specialDeals = true;
						if(ratePlan.getFilterCode().contains("STAYCATION"))
							staycation = true;
					}
					if (ratePlan.getFilterCode().contains("TWO_MEAL_AVAIL"))
						twomealsFilter = true;
					if (ratePlan.getFilterCode().contains("ALL_MEAL_AVAIL"))
						allmealsFilter = true;
					if (ratePlan.getFilterCode().contains(PACKAGE_RATE))
						packageRoomFilter = true;
					if (ratePlan.getFilterCode().contains("CTA_RATES_AVAIL"))
						partnerExclusiveFilter = true;
					if (negotiatedRateFlag && ratePlan.getFilterCode().contains(Constants.INSTANT_BOOKING))
						instantBookingFilter = true;
					if(Utility.isMyBizRequest()
							&& PAGE_CONTEXT_DETAIL.equalsIgnoreCase(pageContext)
							&& ratePlan.getFilterCode().contains(CONTRACTED_FARE))
						contractedFareFilter = true;

					if(Utility.isMyBizRequest()
							&& PAGE_CONTEXT_DETAIL.equalsIgnoreCase(pageContext)
							&& ratePlan.getFilterCode().contains(HOTEL_CLOUD))
						hotelCloudFilter = true;
				}
			}
		}
		if (partnerExclusiveFilter) {
			boolean selected = filterCriteria!=null && filterCriteria.stream().anyMatch(a -> "CTA_RATES_AVAIL".equalsIgnoreCase(a.getFilterValue()) );
			RatePlanFilter ratePlanFilter = new RatePlanFilter("Partner Exclusive Rate", "CTA_RATES_AVAIL", "", selected);
			ratePlanFilters.add(ratePlanFilter);
		}

		if (freeCancellationFilter) {
			boolean selected = filterCriteria!=null && filterCriteria.stream().anyMatch(a ->"CANCELLATION_AVAIL".equalsIgnoreCase(a.getFilterValue()));
			RatePlanFilter ratePlanFilter = new RatePlanFilter(polyglotService.getTranslatedData(ConstantsTranslation.FREE_CANCELLATION_FILTER), "FREE_CANCELLATION", "", selected);
			ratePlanFilters.add(ratePlanFilter);
		}
		if(mpBookNowFilterFor0) {
			boolean selected = filterCriteria!=null && filterCriteria.stream().anyMatch(a ->"CANCELLATION_AVAIL".equalsIgnoreCase(a.getFilterValue()));
			RatePlanFilter mpBookNowRatePlanFilter = new RatePlanFilter(polyglotService.getTranslatedData(BOOK_NOW_AT_0), BOOK_NOW_AT_0, "", selected);
			ratePlanFilters.add(mpBookNowRatePlanFilter);
		}
		if(mpBookNowFilterFor1) {
			boolean selected = filterCriteria!=null && filterCriteria.stream().anyMatch(a ->"CANCELLATION_AVAIL".equalsIgnoreCase(a.getFilterValue()));
			RatePlanFilter mpBookNowRatePlanFilter = new RatePlanFilter(polyglotService.getTranslatedData(BOOK_NOW_AT_1), BOOK_NOW_AT_1, "", selected);
			ratePlanFilters.add(mpBookNowRatePlanFilter);
		}
		if (freeBreakfastFilter) {
			boolean selected = filterCriteria!=null && filterCriteria.stream().anyMatch(a ->"BREAKFAST_AVAIL".equalsIgnoreCase(a.getFilterValue()) || "BREAKFAST".equalsIgnoreCase(a.getFilterValue()));
			RatePlanFilter ratePlanFilter = new RatePlanFilter(polyglotService.getTranslatedData(ConstantsTranslation.BREAKFAST_INCLUDED_FILTER), "FREE_BREAKFAST", "", selected);
			ratePlanFilters.add(ratePlanFilter);
		}
		if (twomealsFilter) {
			boolean selected = filterCriteria!=null && filterCriteria.stream().anyMatch(a ->"TWO_MEAL_AVAIL".equalsIgnoreCase(a.getFilterValue()) );
			RatePlanFilter ratePlanFilter = new RatePlanFilter(polyglotService.getTranslatedData((myPartner?"BUSINESS_TWO_MEAL_AVAIL_TITLE_MYPARTNER":"BREAKFAST_LUNCH_DINNER_INCLUDED")), "TWO_MEAL_AVAIL", "", selected);
			ratePlanFilters.add(ratePlanFilter);
		}
		if (allmealsFilter) {
			boolean selected = filterCriteria!=null && filterCriteria.stream().anyMatch(a ->"ALL_MEAL_AVAIL".equalsIgnoreCase(a.getFilterValue()) );
			RatePlanFilter ratePlanFilter = new RatePlanFilter(polyglotService.getTranslatedData((myPartner?"BUSINESS_ALL_MEAL_AVAIL_TITLE_MYPARTNER":"ALL_MEALS_INCLUDED")), "ALL_MEAL_AVAIL", "", selected);
			ratePlanFilters.add(ratePlanFilter);
		}
		if(!myPartner) {
			if (pahFilter) {
				boolean selected = filterCriteria!=null && filterCriteria.stream().anyMatch(a ->"PAH".equalsIgnoreCase(a.getFilterValue()) || "PAH_AVAIL".equalsIgnoreCase(a.getFilterValue()));
				RatePlanFilter ratePlanFilter = new RatePlanFilter(polyglotService.getTranslatedData(ConstantsTranslation.PAY_AT_HOTEL_FILTER), "PAH", "", selected);
				ratePlanFilters.add(ratePlanFilter);
			}
			if (fczpnFilter) {
				boolean selected = filterCriteria!=null && filterCriteria.stream().anyMatch(a ->"PAY_LATER".equalsIgnoreCase(a.getFilterValue()));
				RatePlanFilter ratePlanFilter;
				if (isblockPAH) {
					ratePlanFilter = new RatePlanFilter(polyglotService.getTranslatedData(ConstantsTranslation.PAY_LATER_FILTER), "FCZPN", "", selected);
				} else {
					//GCC -> Free Cancellation Zero Payment Now
					if (Utility.isRegionGccOrKsa(region)) {
						ratePlanFilter = new RatePlanFilter(polyglotService.getTranslatedData("FCZPN_FILTER"), "FCZPN", "", selected);
					}
					//BNPL_AT_1 -> Free Cancellation - Book @ ₹ 1
					else if (BNPLVariant.BNPL_AT_1.equals(bnplVariant)) {
						ratePlanFilter = new RatePlanFilter(polyglotService.getTranslatedData(ConstantsTranslation.FCZPN_FILTER_BNPL_NEW_VARIANT), "FCZPN", "", selected);
					}
					//BNPL_AT_0 -> Book with 0 Payment
					else if (BNPLVariant.BNPL_AT_0.equals(bnplVariant)) {
						ratePlanFilter = new RatePlanFilter(polyglotService.getTranslatedData(ConstantsTranslation.FCZPN_FILTER_BNPL_ZERO_VARIANT), "FCZPN", "", selected);
					} else {
						ratePlanFilter = new RatePlanFilter(polyglotService.getTranslatedData(ConstantsTranslation.FCZPN_FILTER), "FCZPN", "", selected);
					}
				}
				ratePlanFilters.add(ratePlanFilter);
			}
			if (specialDeals) {
				RatePlanFilter ratePlanFilter = new RatePlanFilter(polyglotService.getTranslatedData(ConstantsTranslation.SPECIAL_DEALS_FILTER), "SPECIALDEALS", "", false);
				ratePlanFilters.add(ratePlanFilter);
			}

			if (staycation && "GETAWAY".equalsIgnoreCase(funnelSource)) {
				boolean selected = filterCriteria!=null && filterCriteria.stream().anyMatch(a ->"STAYCATION_DEALS".equalsIgnoreCase(a.getFilterValue()));
				RatePlanFilter ratePlanFilter = new RatePlanFilter(polyglotService.getTranslatedData(ConstantsTranslation.GETAWAY_DEALS_FILTER), "STAYCATION", "", selected);
				ratePlanFilters.add(ratePlanFilter);
			}
		}
//Remove MMT PACKAGE and MMT LUXE PACKAGE node value and set to PACKAGE_RATE, confirm with the client LUXE_PACKAGE and NON_LUXE_PACKAGE not required
		if (packageRoomFilter) {
			boolean selected = filterCriteria != null && filterCriteria.stream().anyMatch(
					a -> Constants.PACKAGE_RATE.equalsIgnoreCase(a.getFilterValue()));
			RatePlanFilter ratePlanFilter = null;
			if (isLuxeHotel) {
				ratePlanFilter = new RatePlanFilter(polyglotService.getTranslatedData(FILTER_LUXE_PACKAGE_TEXT),
						Constants.PACKAGE_RATE, "", selected);
			} else {
				ratePlanFilter = new RatePlanFilter(polyglotService.getTranslatedData(FILTER_NON_LUXE_PACKAGE_TEXT),
						Constants.PACKAGE_RATE, "", selected);
			}
			if(setSuperPackagePersuasion(commonModifierResponse)) {
				ratePlanFilter = new RatePlanFilter(polyglotService.getTranslatedData(Constants.SUPER_PACKAGES_TEXT),
						Constants.PACKAGE_RATE, "", selected);
			}
			ratePlanFilter.setOrder(1);
			ratePlanFilters.add(ratePlanFilter);
		}

		// Add INSTANT BOOKING rate plan filter negotiated rate hotel flow.
		if (instantBookingFilter) {
			boolean instantBookingSelected = filterCriteria != null && filterCriteria.stream().anyMatch(a -> Constants.INSTANT_BOOKING.equalsIgnoreCase(a.getFilterValue()));
			RatePlanFilter ratePlanFilter = new RatePlanFilter(polyglotService.getTranslatedData(INSTANT_BOOKING), INSTANT_BOOKING, "", instantBookingSelected);
			ratePlanFilters.add(ratePlanFilter);
		}
		if(contractedFareFilter)
		{
			boolean selected = false;
			RatePlanFilter ratePlanFilter = new RatePlanFilter("Contracted Fare", CONTRACTED_FARE, "", selected);
			ratePlanFilters.add(ratePlanFilter);
		}
		if(hotelCloudFilter)
		{
			boolean selected = false;
			RatePlanFilter ratePlanFilter = new RatePlanFilter(HOTEL_CLOUD_TITLE, HOTEL_CLOUD, "", selected);
			ratePlanFilters.add(ratePlanFilter);
		}

		if (CollectionUtils.isEmpty(ratePlanFilters))
			return null;

		for (RatePlanFilter filter : ratePlanFilters) {
			if (!applyFilterToCombo && occupancyRooms != null && CollectionUtils.isNotEmpty(occupancyRooms)) {
				filter.setSelected(false);
			}
		}
		if (filterCriteria != null && filterCriteria.stream().anyMatch(a -> "SUITE_ROOM".equalsIgnoreCase(a.getFilterValue()))) {
			RatePlanFilter ratePlanFilter = new RatePlanFilter("Suite Room", "SUITE", "", true);
			ratePlanFilters.add(ratePlanFilter);
		}
		ratePlanFilters.sort(Comparator.comparingInt(RatePlanFilter::getOrder));
		return ratePlanFilters;
	}

	private void updatePackageInclusionBaseRatePlanName(Map<String, String> ratePlanCodeAndNameMap, List<RoomDetails> packageRooms){
		if(CollectionUtils.isEmpty(packageRooms)){
			return;
		}
		for(RoomDetails roomDetails: packageRooms){
			if(CollectionUtils.isEmpty(roomDetails.getRatePlans())){
				continue;
			}
			for(SelectRoomRatePlan roomRatePlan: roomDetails.getRatePlans()){
				PackageSelectRoomRatePlan packageSelectRoomRatePlan =  (PackageSelectRoomRatePlan) roomRatePlan;
				if(packageSelectRoomRatePlan.getPackageInclusionDetails() != null){
					String name = ratePlanCodeAndNameMap.get(packageSelectRoomRatePlan.getPackageInclusionDetails().getBaseRatePlanCode());
					if(StringUtils.isNotBlank(name)){
						packageSelectRoomRatePlan.getPackageInclusionDetails().setBaseRatePlanName(name);
					}

				}
			}
		}
	}

	private void setRatePlanDetailsCta(RatePlan ratePlanHes, SelectRoomRatePlan ratePlan) {
		if(Objects.nonNull(ratePlanHes) &&
				ratePlanHes.isMealAvailableAtProperty() && Objects.nonNull(ratePlan.getCancellationPolicy())
				&& ratePlan.getCancellationPolicy().getType() == BookedCancellationPolicyType.NR) {
			ratePlan.setShowRatePlanDetailsCta(false);
		} else {
			ratePlan.setShowRatePlanDetailsCta(true);
		}
	}

	private BgStyle getBgStyleforOccassion(com.mmt.hotels.model.persuasion.response.BgGradient bgGradient) {
		if(Objects.nonNull(bgGradient)) {
			BgStyle bgStyle = new BgStyle();
			bgStyle.setStart(bgGradient.getStart());
			bgStyle.setCenter(bgGradient.getCenter());
			bgStyle.setEnd(bgGradient.getEnd());
			bgStyle.setDirection(DIAGONAL_BOTTOM);
			bgStyle.setAngle(OCCASSION_PACKAGE_ANGLE);
			return bgStyle;
		}
		return null;
	}

	private BgStyle getBgStyle(String start, String end, String direction) {
		BgStyle bgStyle = new BgStyle();
		bgStyle.setStart(start);
		bgStyle.setEnd(end);
		bgStyle.setDirection(direction);
		return bgStyle;
	}

	private List<RoomDetails> getRooms(RoomTypeDetails roomTypeDetails, HotelsRoomInfoResponseEntity hotelsRoomInfoResponseEntity,
									   HotelImage hotelImage, String listingType, String expData, boolean ratePlanGroup,
									   String askedCurrency, String funnelSource, WalletSurge walletSurge,
									   com.mmt.hotels.model.response.searchwrapper.Segments segments, int days,
									   Integer hotelStarRating, int ap, boolean isBlockPAH, Map<String, JsonNode> roomPersuasionMap,
									   CommonModifierResponse commonModifierResponse, boolean isPackageRoom,
									   Map<String, String> ratePlanCodeAndNameMap, boolean isLuxeHotel, boolean isAltAccoHotel,
									   boolean isUserGCCAndMmtExclusive, boolean groupBookingPrice, boolean isOHSExpEnable,
									   boolean comboCase, InstantFareInfo instantFareInfo, String corpAlias, boolean isExactOrOccupancyRoom,
									   String propertyType, String countryCode, final MarkUpDetails markUpDetails,
									   Double foodRating, boolean exactCase, String siteDomain,
									   String selectedRoomCode, String selectedRateplanCode, boolean isRecommended,
									   HotelRates hotelRates, boolean isOccassion, boolean isHighSellingAltAcco) {

		if (roomTypeDetails == null || roomTypeDetails.getRoomType() == null)
			return null;

		boolean isNewSelectRoomPage = commonModifierResponse != null && utility.isExperimentTrue(commonModifierResponse.getExpDataMap(), NEW_SELECT_ROOM_PAGE);
		boolean isNewDetailPageDesktop = commonModifierResponse != null && utility.isExperimentOn(commonModifierResponse.getExpDataMap(), NEW_DETAIL_PAGE_DESKTOP_EXP);
		boolean isShowNRLinkedRates = commonModifierResponse != null && utility.isExperimentTrue(commonModifierResponse.getExpDataMap(), LINKED_RATE_EXPERIMENT_NR);


		/* START <RoomCode,ImageURLsList> map */
		Map<String, List<String>> roomImageMap = new HashMap<>();
		if (hotelImage != null && hotelImage.getImageDetails() != null && MapUtils.isNotEmpty(hotelImage.getImageDetails().getProfessional())
				&& hotelImage.getImageDetails().getProfessional().containsKey("R")) {
			List<ProfessionalImageEntity> images = hotelImage.getImageDetails().getProfessional().get("R");
			if (CollectionUtils.isNotEmpty(images)) {
				images.forEach( image -> {
					if (StringUtils.isNotBlank(image.getUrl())) {
						roomImageMap.computeIfAbsent(image.getCatCode(), k -> new ArrayList<>());
						roomImageMap.get(image.getCatCode()).add(image.getUrl().startsWith("http") ? image.getUrl() : "https:" + image.getUrl());
					}
				});
			}
		}
		/* END <RoomCode,ImageURLsList> map */

		/* START <RoomCode,360Images> map */
		Map<String, List<Image360>> room360ImageMap = new HashMap<>();
		if (hotelImage != null && hotelImage.getImageDetails() != null && MapUtils.isNotEmpty(hotelImage.getImageDetails().getImages360())
				&& hotelImage.getImageDetails().getImages360().containsKey("R")) {
			List<Image360> images = hotelImage.getImageDetails().getImages360().get("R");
			if (CollectionUtils.isNotEmpty(images)) {
				images.forEach( image -> {
					if (StringUtils.isNotBlank(image.getRoomCode())) {
						room360ImageMap.computeIfAbsent(image.getRoomCode(), k -> new ArrayList<>());
						room360ImageMap.get(image.getRoomCode()).add(image);
					}
				});
			}
		}
		/* END <RoomCode,360Images> map */

		boolean mostPopularPersuasion = true;
		List<RoomDetails> roomDetail = new ArrayList<>();
		for (String roomCode : roomTypeDetails.getRoomType().keySet()) {
			RoomDetails roomDetails = new RoomDetails();
			if(isPackageRoom) {
				roomDetails = new PackageRoomDetails();
				setFilterDetails((PackageRoomDetails) roomDetails);
				((PackageRoomDetails) roomDetails).setType(SUPER_PACKAGE_TYPE);
			}
			if(isOccassion) {
				roomDetails = new PackageRoomDetails();
				((PackageRoomDetails) roomDetails).setType(roomTypeDetails.getOccassionDetails().getOccassionType());
			}
			if(isRecommended) {
				roomDetails = new PackageRoomDetails();
				((PackageRoomDetails) roomDetails).setType(MEAL_UPGRADE);
				String mealPlan = getMealPlanCode(roomTypeDetails);
				if(StringUtils.isNotEmpty(mealPlan)) {
					((PackageRoomDetails) roomDetails).setType(MEAL_UPGRADE.replace("{mealPlan}", mealPlan));
				}
				if(StringUtils.isNotEmpty(roomTypeDetails.getTrackingText())) {
					((PackageRoomDetails) roomDetails).setType(roomTypeDetails.getTrackingText());
				}
			}
			roomDetails.setRoomCode(roomCode);
			roomDetails.setRoomName(roomTypeDetails.getRoomType().get(roomCode).getRoomTypeName());
			String client = MDC.get(MDCHelper.MDCKeys.CLIENT.getStringValue());
			if(MapUtils.isNotEmpty(roomPersuasionMap)) {
				if(isExactOrOccupancyRoom && isNewSelectRoomPage && (client.equalsIgnoreCase(ANDROID) ||  client.equalsIgnoreCase(DEVICE_IOS))){
					JsonNode roomPersuasions =  roomPersuasionMap.get(roomCode);
					updateRoomPersuasion(roomPersuasions);
					roomDetails.setRoomPersuasions(roomPersuasions);
				}else{
					roomDetails.setRoomPersuasions(roomPersuasionMap.get(roomCode));
				}
			}
			Map<String, RoomInfo> staticRoomInfoMap = new HashMap<>();
			if (null!=hotelsRoomInfoResponseEntity && hotelsRoomInfoResponseEntity.getHtlRmInfo()!=null && CollectionUtils.isNotEmpty(hotelsRoomInfoResponseEntity.getHtlRmInfo()))
				staticRoomInfoMap = hotelsRoomInfoResponseEntity.getHtlRmInfo().get(0).getHotelRoomInfo();
			RoomInfo roomInfo = MapUtils.isNotEmpty(staticRoomInfoMap) ? staticRoomInfoMap.get(roomCode) : null;
			String sellableType = null;
			if (roomInfo != null &&  null != roomInfo.getRoomAttributes())
				sellableType = roomInfo.getRoomAttributes().getSellableType();
			/*
			 * myPartner change log : commonModifierResponse floated down
			 * */
			List<SelectRoomRatePlan> ratePlans = getRatePlans(
					roomTypeDetails.getRoomType().get(roomCode), listingType, expData, ratePlanGroup, askedCurrency,
					sellableType,funnelSource, days, ap , isBlockPAH,commonModifierResponse, isPackageRoom,
					ratePlanCodeAndNameMap, isLuxeHotel, isUserGCCAndMmtExclusive, groupBookingPrice,
					isAltAccoHotel, instantFareInfo, corpAlias, markUpDetails, foodRating, exactCase,
					siteDomain, isRecommended, hotelRates, isOccassion, isHighSellingAltAcco);
			if(isShowNRLinkedRates){
				linkRatePlans(ratePlans);
			}
			roomDetails.setRatePlans(ratePlans);
			roomDetails.setClientViewType(getTariffViewType(roomDetails,hotelStarRating,roomDetail.size()<1,expData));
			roomDetails.setImages(roomImageMap.get(roomCode));
			roomDetails.setExtraBedAvailable(roomInfo != null && roomInfo.isAllowExtraBed() && roomInfo.isAllowExtraCrib());
			roomDetails.setRoomCategoryText(LISTING_TYPE_ENTIRE.equalsIgnoreCase(listingType)
					?(propertyType != null?propertyType:SELLABLE_ROOM_TYPE)
					:(sellableType!=null?sellableType:SELLABLE_ROOM_TYPE));
			if(CollectionUtils.isNotEmpty(room360ImageMap.get(roomCode))){
				View360Image view360Image = new View360Image();
				view360Image.setImages(room360ImageMap.get(roomCode));
				view360Image.setCtaText(polyglotService.getTranslatedData(ConstantsTranslation.CTA_360_TEXT));
				view360Image.setCtaIcon(view360IconUrl);
				roomDetails.setView360(view360Image);
			}

			if(anyPackageRatePlanAvailable(roomDetails.getRatePlans())) {
				roomDetails.setHighlightImage(superPackageIconUrlSecondary);
			}

			roomDetails.setMedia(populateMedia(staticRoomInfoMap, roomDetails.getImages() , roomCode));

			if(Utility.isRegionGccOrKsa(siteDomain) && utility.isExperimentOn(commonModifierResponse.getExpDataMap(),"GBRP") && MapUtils.isNotEmpty(addOnInfoMostPopularTag) && roomDetails.getRatePlans().size() > 1){
				Iterator<RatePlan> ratePlanIterator = roomTypeDetails.getRoomType().get(roomCode).getRatePlanList().values().iterator();
				while (ratePlanIterator.hasNext()){
					RatePlan hesRatePlan = ratePlanIterator.next();
					Optional<SelectRoomRatePlan> optionslRatePlan = roomDetails.getRatePlans().stream().filter(f -> f.getRpc().equalsIgnoreCase(hesRatePlan.getRatePlanCode()) && !hesRatePlan.getLowest() && hesRatePlan.isMostPopularRateplan()).findFirst();
					if(exactCase && mostPopularPersuasion && optionslRatePlan.isPresent()){
						SelectRoomRatePlan ratePlan = optionslRatePlan.get();
						List<PersuasionResponse> persuasions = new ArrayList<>();
						PersuasionResponse persuasionResponse = new PersuasionResponse();
						persuasionResponse.setPlaceholderId(PRICE_TOP);
						persuasionResponse.setId(POPULAR_PACKAGE);
						persuasionResponse.setPersuasionText(addOnInfoMostPopularTag.get(TITLE));
						persuasionResponse.setTemplate(TEXT_WITH_BG_IMAGE);
						Style style = new Style();
						style.setTextColor(addOnInfoMostPopularTag.get(COLOR));
						style.setBgUrl(addOnInfoMostPopularTag.get(PERSUASION_BG_URL));
						Margin margin = new Margin();
						margin.setHorizontal(addOnInfoMostPopularTag.get(HORIZONTAL_MARGIN));
						margin.setVertical(addOnInfoMostPopularTag.get(VERTICAL_MARGIN));
						style.setMargin(margin);
						style.setFontSize(SMALL);
						persuasionResponse.setStyle(style);
						persuasions.add(persuasionResponse);
						ratePlan.setBgUrl(addOnInfoMostPopularTag.get(BG_URL));
						ratePlan.setPersuasions(persuasions);
						mostPopularPersuasion = false;
						break;
					}
				}
			}
			ExtraGuestDetail extraGuestDetail = null;
			if(  MapUtils.isNotEmpty(roomTypeDetails.getRoomType().get(roomCode).getRatePlanList()))
			{
				RatePlan ratePlan = roomTypeDetails.getRoomType().get(roomCode).getRatePlanList().values().stream().findFirst().orElse(null);
				if(ratePlan!=null && ratePlan.getExtraGuestDetail()!=null){
					extraGuestDetail = ratePlan.getExtraGuestDetail();
				}
				if(isPackageRoom && ratePlan!=null) {
					roomDetails.setIconUrl(superPackageIconUrl);
					roomDetails.setPackageBenefits(buildPackageBenefitsText(((PackageRoomRatePlan) ratePlan).getPackageInclusionDetails(),
							isNewDetailPageDesktop,askedCurrency, null));
					BorderGradient borderGradient = new BorderGradient();
					borderGradient.setStart("#8c671c");
					borderGradient.setEnd("#8c671c");
					borderGradient.setColor(Arrays.asList("#d3aa53", "#ffefcf", "#d3aa53"));
					borderGradient.setDirection("horizontal");
					roomDetails.setBorderGradient(borderGradient);
					roomDetails.setBgStyle(getBgStyle(Constants.FFFFFF, Constants.ffeaa7, DIAGONAL_BOTTOM));
				}
				if(isOccassion && ratePlan!=null && Objects.nonNull(roomTypeDetails.getOccassionDetails())) {
					roomDetails.setIconUrl(roomTypeDetails.getOccassionDetails().getRecommendedRoomsIconUrl());
					roomDetails.setPackageImageUrl(roomTypeDetails.getOccassionDetails().getPackagingImageUrl());
					roomDetails.setPackageBenefits(buildPackageBenefitsText(((PackageRoomRatePlan) ratePlan).getPackageInclusionDetails(),
							isNewDetailPageDesktop,askedCurrency, roomTypeDetails.getOccassionDetails().getOccassionType()));
					BorderGradient borderGradient = new BorderGradient();
					borderGradient.setStart("#d8d8d8");
					borderGradient.setEnd("#d8d8d8");
					borderGradient.setColor(Collections.EMPTY_LIST);
					borderGradient.setDirection("horizontal");
					roomDetails.setBorderGradient(borderGradient);
					roomDetails.setBgStyle(getBgStyleforOccassion(roomTypeDetails.getOccassionDetails().getBgGradient()));
				}
				if(isRecommended) {
					String mealPlanCode = getMealUpgradeType(roomTypeDetails);
					roomDetails.setIconUrl(buildMealUpgradeIcon(mealPlanCode));
					roomDetails.setPackageBenefits(buildMealUpgradeText(mealPlanCode));
					if(StringUtils.isNotEmpty(roomTypeDetails.getRecommendationCommunication())) {
						roomDetails.setPackageBenefits(roomTypeDetails.getRecommendationCommunication());
					}
					String start = FFFFFF;
					String end = D3E7FF;
					if(MapUtils.isNotEmpty(recommendedRoomPropertiesMap) && recommendedRoomPropertiesMap.containsKey(mealPlanCode)) {
						start = (recommendedRoomPropertiesMap.containsKey("start"))?recommendedRoomPropertiesMap.get(mealPlanCode).get("start"):FFFFFF;
						end = (recommendedRoomPropertiesMap.containsKey("end"))?recommendedRoomPropertiesMap.get(mealPlanCode).get("end"):D3E7FF;
					}
					roomDetails.setBgStyle(getBgStyle(start, end, DIAGONAL_BOTTOM));
				}
			}


			if (roomInfo != null) {
				boolean amendRoomHighlights = utility.showHighlightsForRoomAmenities(countryCode, funnelSource);
				roomDetails.setAmenities(commonResponseTransformer.buildAmenities(roomInfo.getFacilityWithGrp(), roomInfo.getStarFacilities(), roomInfo.getHighlightedFacilities(), amendRoomHighlights));
				roomDetails.setHighlightedAmenities(commonResponseTransformer.buildHighlightedAmenities(roomInfo.getFacilityHighlights()));
				roomDetails.setMaster(roomInfo.isMaster());
				roomDetails.setMaxAdult(roomInfo.getMaxAdultCount());
				roomDetails.setMaxChild(roomInfo.getMaxChildCount());
				roomDetails.setMaxGuest(roomInfo.getMaxGuestCount());
				/* Below three nodes are duplicate & need to be removed after next client release */
				/* Only Temp addition to fix live bug */
				roomDetails.setMaxGuestCount(roomInfo.getMaxGuestCount());
				roomDetails.setMaxAdultCount(roomInfo.getMaxAdultCount());
				roomDetails.setMaxChildCount(roomInfo.getMaxChildCount());
				/* Above three nodes are duplicate & need to be removed after next client release */
				roomDetails.setBedCount(roomInfo.getBedCount());
				if (StringUtils.isNotBlank(roomInfo.getBedRoomCount()))
					roomDetails.setBedroomCount(NumberUtils.toInt(roomInfo.getBedRoomCount(),0));
				roomDetails.setBathroomCount(roomInfo.getBathroomCount());
				roomDetails.setParentRoomCode(roomInfo.getParentRoomCode());
				roomDetails.setRoomSize(roomInfo.getRoomSize());
				roomDetails.setRoomViewName(roomInfo.getRoomViewName());
				boolean pilgrimageBedInfoEnable = utility.isExperimentOn(utility.getExpDataMap(expData), ExperimentKeys.PILGR_IMAGE_BED_INFO.getKey());
				roomDetails.setRoomHighlights(getRoomHighlights(roomInfo, extraGuestDetail, isAltAccoHotel,isOHSExpEnable,roomDetails.getAmenities(), countryCode, amendRoomHighlights, pilgrimageBedInfoEnable));
				roomDetails.setBeds(roomInfo.getBeds());
				roomDetails.setBedInfoText(roomInfo.getBedInfoText());
				if(StringUtils.isNotEmpty(roomInfo.getRoomName())) {
					roomDetails.setRoomName(roomInfo.getRoomName());
				}
				roomDetails.setWalletSurge(getWalletSurge(walletSurge));
				roomDetails.setSegments(getSegments(segments));
				roomDetails.setExtraGuestInfo(getExtraGuestInfo(extraGuestDetail,isAltAccoHotel));
				if(roomDetails.getMaster()!= null && roomDetails.getMaster()){
					roomDetails.setSubRoomDetails(populateSubRooms(roomCode, staticRoomInfoMap));
				}
				roomDetails.setUsp(roomInfo.getUsp());
				roomDetails.setDescription(roomInfo.getDescription());
				roomDetails.setRoomSummary(roomInfo.getRoomSummary());
				if (roomDetails.getRoomSummary() != null && roomDetails.getRoomSummary().isTopRated() && (!hotelRates.isAltAcco() || !DOM_COUNTRY.equalsIgnoreCase(countryCode))) {
					PersuasionObject persuasionObject =  createTopRatedPersuasion(isNewDetailPageDesktop);
					addPersuasion(roomDetails, persuasionObject);
				}
				if(hotelsRoomInfoResponseEntity != null && CollectionUtils.isNotEmpty(hotelsRoomInfoResponseEntity.getHtlRmInfo()) && hotelsRoomInfoResponseEntity.getHtlRmInfo().get(0).isSpaceDetailsRequired() && !hotelRates.isHighSellingAltAcco()){
					if (commonModifierResponse != null && utility.isExperimentOn(commonModifierResponse.getExpDataMap(), EXP_PLV2)) {
						roomDetails.setPrivateSpacesV2(getSpaceDataV2(roomInfo.getPrivateSpaces(), true));
					} else {
						roomDetails.setPrivateSpaces(getSpaceData(roomInfo.getPrivateSpaces(), roomTypeDetails.getRoomType().get(roomCode), expData));
					}
				}
				roomDetails.setPropertyLayoutInfo(buildPropertyLayoutInfo(roomTypeDetails,roomCode,(LISTING_TYPE_ENTIRE.equalsIgnoreCase(listingType)?propertyType:SELLABLE_ROOM_TYPE)));
				roomDetails.setExtraBedCount(roomDetails.getPrivateSpaces() != null ? roomDetails.getPrivateSpaces().getExtraBeds() : 0);
				roomDetails.setBaseGuest(roomDetails.getPrivateSpaces() != null ? roomDetails.getPrivateSpaces().getBaseGuests() : 0);
				if (roomDetails.getPrivateSpaces()!=null) {
					roomDetails.setMaxGuest(roomDetails.getPrivateSpaces().getMaxGuests());
				}
				//Only for combos
				buildStayDetails(roomDetails);
				roomDetails.setBaseRoom(roomTypeDetails.getRoomType().get(roomCode).isBaseRoom());
				if(isExactOrOccupancyRoom && roomInfo.getRoomSize()!=null && roomTypeDetails.getRoomType().get(roomCode)!=null
						&& (client.equalsIgnoreCase(ANDROID) ||  client.equalsIgnoreCase(DEVICE_IOS) || client.equalsIgnoreCase(CLIENT_DESKTOP))){
					buildRoomInfoText(roomDetails,roomInfo.getRoomSize(),roomTypeDetails.getRoomType().get(roomCode), askedCurrency);
				}
			}
			if (isPackageRoom || isRecommended || isOccassion) {
				setPackageRoomSpecificInfo((PackageRoomDetails) roomDetails,
						(PackageRoomType) roomTypeDetails.getRoomType().get(roomCode), setSuperPackagePersuasion(commonModifierResponse));
			}

			if(commonModifierResponse != null && commonModifierResponse.isLiteResponse()) {
				utility.removeUnwantedPersuasions(roomDetails);
			}

			roomDetail.add(roomDetails);
		}
		return roomDetail;
	}

	private void linkRatePlans(List<SelectRoomRatePlan> ratePlans) {
		try{
			List<SelectRoomRatePlan> ratePlansCopy = new ArrayList<>(ratePlans);

			for (SelectRoomRatePlan ratePlan : ratePlansCopy) {
				List<LinkedRatePlan> linkedRatePlans = new ArrayList<>();
				if (CollectionUtils.isNotEmpty(ratePlan.getChildLinkedRates())) {
					for (LinkedRate childLinkedRate : ratePlan.getChildLinkedRates()) {
						if(LINKEDRATE_FCNR.equalsIgnoreCase(childLinkedRate.getType())) {
							Iterator<SelectRoomRatePlan> iterator = ratePlans.iterator();
							while (iterator.hasNext()) {
								SelectRoomRatePlan selectRoomRatePlan = iterator.next();
								if (selectRoomRatePlan.getRpc().equalsIgnoreCase(childLinkedRate.getPricingKey())) {
									LinkedRatePlan linkedRatePlan = new LinkedRatePlan();
									selectRoomRatePlan.setRatePlanType(LINKED_RATE_PLAN_TYPE);
									linkedRatePlan.setRatePlan(selectRoomRatePlan);
									RatePlanData ratePlanData = createRatePlanData();
									ratePlanData.setRatePlanName(selectRoomRatePlan.getLinkedRatePlanName());
									linkedRatePlan.setData(ratePlanData);
									BgStyle bgStyle = createBgStyle();
									RatePlanStyle ratePlanStyle = createRatePlanStyle(bgStyle);
									linkedRatePlan.setStyle(ratePlanStyle);
									linkedRatePlans.add(linkedRatePlan);
									iterator.remove();
								}
							}
						}
					}
				}
				ratePlan.setLinkedRatePlans(linkedRatePlans);
			}
		} catch (Exception ex){
			logger.error("Error in linking rate plans", ex);
		}

	}

	private RatePlanData createRatePlanData() {
		RatePlanData ratePlanData = new RatePlanData();
		ratePlanData.setTitle(polyglotService.getTranslatedData(LINKED_RATE_PLAN_TITLE));
		ratePlanData.setDescription(polyglotService.getTranslatedData(LINKED_RATE_PLAN_DESCRIPTION));
		ratePlanData.setCtaText(polyglotService.getTranslatedData(LINKED_RATE_PLAN_CTATEXT));
		ratePlanData.setBottomSheetFooterText(polyglotService.getTranslatedData(LINKED_RATE_PLAN_BOTTOM_SHEET_FOOTER_TEXT));
		return ratePlanData;
	}

	private BgStyle createBgStyle() {
		if (linkedRatePlanStyle == null) {
			return null;
		}
		BgStyle bgStyle = new BgStyle();
		bgStyle.setStart(linkedRatePlanStyle.getStart());
		bgStyle.setEnd(linkedRatePlanStyle.getEnd());
		bgStyle.setCenter(linkedRatePlanStyle.getCenter());
		bgStyle.setDirection(linkedRatePlanStyle.getDirection());
		bgStyle.setAngle(linkedRatePlanStyle.getAngle());
		return bgStyle;
	}

	private RatePlanStyle createRatePlanStyle(BgStyle bgStyle) {
		RatePlanStyle ratePlanStyle = new RatePlanStyle();
		ratePlanStyle.setBgStyle(bgStyle);
		return ratePlanStyle;
	}

	private String getoccassionHighlightImage(RoomType roomType){
		if(roomType != null && MapUtils.isNotEmpty(roomType.getRatePlanList())){
			for(RatePlan ratePlan : roomType.getRatePlanList().values()){
				if(Objects.nonNull(ratePlan.getOccassionRoomPersuasion()) &&
						StringUtils.isNotEmpty(ratePlan.getOccassionRoomPersuasion().getHighlightOccassionImageUrl())){
					return ratePlan.getOccassionRoomPersuasion().getHighlightOccassionImageUrl();
				}
			}
		}
		return null;
	}

	private String getMealPlanCode(RoomTypeDetails roomTypeDetails) {
		return Optional.ofNullable(roomTypeDetails)
				.map(RoomTypeDetails::getRoomType)
				.map(Map::entrySet)
				.flatMap(entries -> entries.stream().findFirst())
				.map(Map.Entry::getValue)
				.map(RoomType::getRatePlanList)
				.map(Map::entrySet)
				.flatMap(entries -> entries.stream().findFirst())
				.map(Map.Entry::getValue)
				.map(RatePlan::getMealPlans)
				.filter(mealPlans -> !mealPlans.isEmpty())
				.map(mealPlans -> mealPlans.get(0))
				.map(MealPlan::getCode)
				.orElse(null);
	}

	private String getMealUpgradeType(RoomTypeDetails roomTypeDetails) {
		if (roomTypeDetails != null && roomTypeDetails.getRoomType() != null) {
			for (Map.Entry<String, RoomType> roomTypeEntry : roomTypeDetails.getRoomType().entrySet()) {
				RoomType roomType = roomTypeEntry.getValue();
				if (roomType.getRatePlanList() != null) {
					for (Map.Entry<String, RatePlan> ratePlanEntry : roomType.getRatePlanList().entrySet()) {
						RatePlan ratePlanForMeal = ratePlanEntry.getValue();
						if (ratePlanForMeal.getMealPlans() != null) {
							for (MealPlan mealPlan : ratePlanForMeal.getMealPlans()) {
								return mealPlan.getCode();
							}
						}
					}
				}
			}
		}
		return EMPTY_STRING;
	}

	private String buildMealUpgradeIcon(String mealPlanCode) {
		if (StringUtils.isNotBlank(mealPlanCode) && recommendedRoomPropertiesMap.containsKey(mealPlanCode) && recommendedRoomPropertiesMap.get(mealPlanCode).containsKey("iconUrl")) {
			return recommendedRoomPropertiesMap.get(mealPlanCode).get("iconUrl");
		}
		return defaultSearchRoomUrl;
	}

	private String buildMealUpgradeText(String mealPlanCode) {
		if (StringUtils.isNotBlank(mealPlanCode) && recommendedRoomPropertiesMap.containsKey(mealPlanCode) && recommendedRoomPropertiesMap.get(mealPlanCode).containsKey("benefitText")) {
			return polyglotService.getTranslatedData(recommendedRoomPropertiesMap.get(mealPlanCode).get("benefitText"));
		}
		return polyglotService.getTranslatedData(CP_MEAL_TEXT);
	}

	private String buildPackageBenefitsText(com.mmt.hotels.model.response.pricing.PackageInclusionDetails packageInclusionDetails, boolean isNewDetailPageDesktop, String askedCurrency, String occassionType) {
		String packageBenefitsText = polyglotService.getTranslatedData(ConstantsTranslation.PACKAGE_BENEFITS_TEXT);
		if(isNewDetailPageDesktop){
			packageBenefitsText = polyglotService.getTranslatedData(ConstantsTranslation.PACKAGE_BENEFITS_TEXT_NEW_PAGE_DT);
		}
		if(packageInclusionDetails!= null && StringUtils.isNotEmpty(packageInclusionDetails.getPackageBenefitsSlashedPrice()) && StringUtils.isNotEmpty(packageInclusionDetails.getPackageBenefitsPrice())) {
			String currencySymbol = Currency.getCurrencyEnum(StringUtils.isNotBlank(askedCurrency) ? askedCurrency : DEFAULT_CUR_INR).getCurrencySymbol();
			packageBenefitsText = packageBenefitsText.replace("{1}", String.valueOf((int)Double.parseDouble(packageInclusionDetails.getPackageBenefitsSlashedPrice())));
			packageBenefitsText = packageBenefitsText.replace("{2}", String.valueOf((int)Double.parseDouble(packageInclusionDetails.getPackageBenefitsPrice())));
			packageBenefitsText = packageBenefitsText.replace("{cur}", currencySymbol);
			return packageBenefitsText;
		}
		if(!StringUtils.isEmpty(occassionType)) {
			return polyglotService.getTranslatedData(occassionType + UNDERSCORE + ConstantsTranslation.OCCASION_PACKAGE_BENEFITS_TEXT);
		}
		return polyglotService.getTranslatedData(ConstantsTranslation.ENJOY_PACKAGE_BENEFITS);
	}

	private void setFilterDetails(PackageRoomDetails packageGenerated) {
		FilterDetailsClient filterDetails = new FilterDetailsClient();
		filterDetails.setTitle(SHOW_ALL_PACKAGES);
		filterDetails.setFilterCode(new ArrayList<>());
		filterDetails.getFilterCode().add(PACKAGE_RATE);
		packageGenerated.setFilterDetails(filterDetails);
	}

	private boolean anyPackageRatePlanAvailable(List<SelectRoomRatePlan> ratePlanList) {
		if(CollectionUtils.isNotEmpty(ratePlanList)) {
			for(SelectRoomRatePlan ratePlan:ratePlanList) {
				if(ratePlan.isPackageRateAvailable()) {
					return true;
				}
			}
		}
		return false;
	}

	private PropertyLayoutInfo buildPropertyLayoutInfo(RoomTypeDetails roomTypeDetails, String roomCode, String sellableType) {
		if (MapUtils.isEmpty(roomTypeDetails.getRoomToSleepingInfoArrangementMap()) || StringUtils.isEmpty(roomCode) || StringUtils.isEmpty(sellableType)) {
			return null;
		}
		PropertyLayoutInfo propertyLayoutInfo = new PropertyLayoutInfo();
		List<String> response = new ArrayList<>();
		int index = 0;
		for (Map.Entry<String, List<RoomSleepingInfoLayout>> roomInfoMap : roomTypeDetails.getRoomToSleepingInfoArrangementMap().entrySet()) {
			String[] roomCodeSplit = roomInfoMap.getKey().split(PIPE_SEPARATOR_WITH_BACKSLASH); //This will split the map key in 0- > RoomCode, 1-> RPC , 2-> RoomNumber
			Optional<RoomSleepingInfoLayout> roomSleepingInfoLayout = roomInfoMap.getValue().stream().findFirst();
			if (roomCodeSplit.length != 3 || !roomSleepingInfoLayout.isPresent()) {
				continue;
			}
			String sleepingInfoRoomCode = roomCodeSplit[0];
			String rpc = roomCodeSplit[1];
			//We will check that the RoomCode and RatePlan code of ratePlan and RoomIdToSleepingInfo map is correct
			if (roomCode.equalsIgnoreCase(sleepingInfoRoomCode) && MapUtils.isNotEmpty(roomTypeDetails.getRoomType()) && roomTypeDetails.getRoomType().containsKey(sleepingInfoRoomCode)
					&& MapUtils.isNotEmpty(roomTypeDetails.getRoomType().get(sleepingInfoRoomCode).getRatePlanList()) && roomTypeDetails.getRoomType().get(sleepingInfoRoomCode).getRatePlanList().containsKey(rpc)) {
				StringBuilder finalString = new StringBuilder();
				if (CollectionUtils.isNotEmpty(roomTypeDetails.getRoomType().get(sleepingInfoRoomCode).getRatePlanList().get(rpc).getRoomTariff()) &&
						roomTypeDetails.getRoomType().get(sleepingInfoRoomCode).getRatePlanList().get(rpc).getRoomTariff().size() == 1) {
					finalString.append(sellableType).append(SPACE).append(COLON).append(SPACE).append(String.valueOf(roomSleepingInfoLayout.get().getAdult())).append(SPACE).append(roomSleepingInfoLayout.get().getAdult() > 1 ? PLURAL_ADULTS : ADULT);
				} else {
					finalString.append(sellableType).append(SPACE).append(String.valueOf(++index)).append(SPACE).append(COLON).append(SPACE).append(String.valueOf(roomSleepingInfoLayout.get().getAdult())).append(SPACE).append(roomSleepingInfoLayout.get().getAdult() > 1 ? PLURAL_ADULTS : ADULT);
				}
				if (roomSleepingInfoLayout.get().getChild() > 0) {
					finalString.append(buildPorpertyLayoutChildText(roomSleepingInfoLayout.get()));
				}
				Map<String, Integer> bedInfoMap = new HashMap<>();
				if (MapUtils.isNotEmpty(roomSleepingInfoLayout.get().getSpaceIdToSleepingInfoArrangementMap())) {
					for (Map.Entry<String, SleepingInfoArrangement> sleepingInfo : roomSleepingInfoLayout.get().getSpaceIdToSleepingInfoArrangementMap().entrySet()) {
						if (sleepingInfo.getValue().getGuest() > 0) {
							if (MapUtils.isEmpty(bedInfoMap)) {
								bedInfoMap = (Map<String, Integer>) sleepingInfo.getValue().getBedInfos().clone();
								continue;
							}
							if (MapUtils.isNotEmpty(sleepingInfo.getValue().getBedInfos())) {
								for (Map.Entry<String, Integer> bedInfos : sleepingInfo.getValue().getBedInfos().entrySet()) {
									if (bedInfoMap.containsKey(bedInfos.getKey())) {
										bedInfoMap.put(bedInfos.getKey(), bedInfoMap.get(bedInfos.getKey()) + bedInfos.getValue());
									} else {
										bedInfoMap.put(bedInfos.getKey(), bedInfos.getValue());
									}
								}
							}
						}
					}
					for (Map.Entry<String, Integer> finalBedMap : bedInfoMap.entrySet()) {
						finalString.append(COMMA_SPACE).append(String.valueOf(finalBedMap.getValue())).append(SPACE).append(finalBedMap.getKey());
					}
				}
				response.add(finalString.toString());
			}
		}
		propertyLayoutInfo.setUnits(response);
		return propertyLayoutInfo;
	}

	private StringBuilder buildPorpertyLayoutChildText(RoomSleepingInfoLayout roomSleepingInfoLayout) {
		return new StringBuilder().append(COMMA_SPACE).append(String.valueOf(roomSleepingInfoLayout.getChild())).append(SPACE).append(roomSleepingInfoLayout.getChild() > 1 ? CHILDREN : CHILD);
	}

	private void buildRoomInfoText(RoomDetails roomDetails,String roomSize,RoomType roomType, String askedCurrency){
		String roomInfoText = "";
		List<RatePlan> sortedRatePlanList = roomType.getRatePlanList().values().stream().collect(Collectors.toList());
		int minPrice = Integer.MAX_VALUE;
		if(CollectionUtils.isNotEmpty(sortedRatePlanList)){
			for(RatePlan ratePlan: sortedRatePlanList){
				if(ratePlan.getDisplayFare()!=null && ratePlan.getDisplayFare().getDisplayPriceBreakDown()!=null && StringUtils.isNotEmpty(roomSize)){
					minPrice = min(minPrice,(int)ratePlan.getDisplayFare().getDisplayPriceBreakDown().getDisplayPrice());
				}
			}
		}
		if(minPrice!=Integer.MAX_VALUE){
			DecimalFormat decimalFormat = new DecimalFormat("#,###");
			String minPriceString =  decimalFormat.format(minPrice);
			String currencySymbol = Currency.getCurrencyEnum(StringUtils.isNotBlank(askedCurrency) ? askedCurrency : DEFAULT_CUR_INR).getCurrencySymbol();
			roomInfoText = STARTS_AT + SPACE + currencySymbol + SPACE + minPriceString + PIPE_SEPARATOR + roomSize + SPACE + SQUARE_FEET;
		}
		roomDetails.setRoomInfoText(roomInfoText);
	}

	private void buildGroupBookingComboText(RoomDetails roomDetails, RecommendedCombo recommendedCombo, boolean baseCombo, String funnelSource, OccupancyDetails occupancyDetails) {
		if (!Constants.CLIENT_DESKTOP.equalsIgnoreCase(MDC.get(MDCHelper.MDCKeys.CLIENT.getStringValue())) && Utility.isGroupBookingFunnel(funnelSource) && baseCombo && recommendedCombo != null && roomDetails.getBaseRoom() != null &&
				roomDetails.getBaseRoom() && occupancyDetails != null) {
			int roomCount = occupancyDetails.getNumOfRooms();
			String comboDisplayText = roomCount + SPACE_X_SPACE + roomDetails.getRoomName();
			roomDetails.setDisplayName(comboDisplayText);
			recommendedCombo.setBaseComboText(comboDisplayText);
		}
		else if(Constants.CLIENT_DESKTOP.equalsIgnoreCase(MDC.get(MDCHelper.MDCKeys.CLIENT.getStringValue())) && Utility.isGroupBookingFunnel(funnelSource)  && recommendedCombo != null && roomDetails.getBaseRoom() != null &&
				roomDetails.getBaseRoom() && occupancyDetails != null){
			int roomCount = occupancyDetails.getNumOfRooms();
			String comboDisplayText = roomCount + SPACE_X_SPACE + roomDetails.getRoomName();
			roomDetails.setDisplayName(comboDisplayText);
			recommendedCombo.setBaseComboText(comboDisplayText);
		}
	}

	private void buildStayDetails(SearchRoomsResponse searchRoomsResponse, SleepingArrangementRoomInfo roomInfo, int sellableCombo, String propertyType, HotelRates hotelRates, String countryCode, boolean serviceApartment, boolean modifyStayDetailsForIH) {
		StayDetail stayDetail = null;
		int bedCount = 0;
		int bedRoomCount = 0;
		int maxGuest = 0;
		int extraBeds = 0;
		int baseOccupancy = 0;
		int maxCapacity=0;
		int bathroomCount = 0;
		LinkedHashMap<String,Integer> bedInfoMap = new LinkedHashMap<>();
		if (CollectionUtils.isNotEmpty(searchRoomsResponse.getExactRooms())) {
			List<RoomDetails> roomDetailsList = searchRoomsResponse.getExactRooms();
			if (CollectionUtils.isNotEmpty(roomDetailsList)) {
				RoomDetails exactRoom = roomDetailsList.get(0);
				if(exactRoom.getPrivateSpaces()!=null){
					// It means It's an Migrated Property , So need to compute On the basis PrivateSpace;
					int bathRoomMultiplier = 0;
					if(CollectionUtils.isNotEmpty(exactRoom.getPrivateSpaces().getSpaces())){
						for(Space space : exactRoom.getPrivateSpaces().getSpaces()){
							if(CollectionUtils.isNotEmpty(space.getSleepingInfoArrangement())){
								if((Constants.BEDROOM.equalsIgnoreCase(space.getSpaceType()) || LIVING_ROOM.equalsIgnoreCase(space.getSpaceType())) && bathRoomMultiplier==0){
									bathRoomMultiplier = space.getSleepingInfoArrangement().size();
								}
								for(SleepingInfoArrangement sleepingInfoArrangement : space.getSleepingInfoArrangement()){
									bedCount+=sleepingInfoArrangement.getBed();
									bedRoomCount+=sleepingInfoArrangement.getBedRoom();
									baseOccupancy+=sleepingInfoArrangement.getGuest();
									maxGuest+=sleepingInfoArrangement.getGuest();
									maxCapacity+=sleepingInfoArrangement.getMaxCapacity();
									for(String bedType : sleepingInfoArrangement.getBedInfos().keySet()){
										if(!bedInfoMap.containsKey(bedType))
											bedInfoMap.put(bedType,0);
										bedInfoMap.put(bedType,bedInfoMap.get(bedType)+sleepingInfoArrangement.getBedInfos().get(bedType));
									}
								}
							}
						}
						bathroomCount += ((exactRoom.getBathroomCount()!=null?exactRoom.getBathroomCount() * bathRoomMultiplier:0));
					}
					stayDetail = new StayDetail();
					stayDetail.setBedRoom(bedRoomCount);
					stayDetail.setBed(bedCount);
					stayDetail.setMaxGuests(maxGuest);
					stayDetail.setBaseGuests(baseOccupancy);
					stayDetail.setExtraBeds(extraBeds);
					stayDetail.setBathroom(bathroomCount);
					stayDetail.setMaxCapacity(maxCapacity);
					stayDetail.setBedInfoMap(bedInfoMap);
					stayDetail.setBedInfoText(utility.createBedInfoTextFromBedInfoMap(bedInfoMap));
				} else if (exactRoom.getPrivateSpacesV2() != null && hotelRates != null && hotelRates.getRoomTypeDetails() != null &&
						MapUtils.isNotEmpty(hotelRates.getRoomTypeDetails().getRoomToSleepingInfoArrangementMap())) {
					int bathroomMultiplier = 0;
					for (Map.Entry<String, List<RoomSleepingInfoLayout>> roomSleepingInfo : hotelRates.getRoomTypeDetails().getRoomToSleepingInfoArrangementMap().entrySet()) {
						String roomCode = roomSleepingInfo.getKey().split(PIPE_SEPARATOR_WITH_BACKSLASH).length == 3 ? roomSleepingInfo.getKey().split(PIPE_SEPARATOR_WITH_BACKSLASH)[0] : StringUtils.EMPTY;
						if (StringUtils.isNotEmpty(roomCode) && roomCode.equalsIgnoreCase(exactRoom.getRoomCode()) && CollectionUtils.isNotEmpty(roomSleepingInfo.getValue())) {
							bathroomMultiplier++;
							for (Map.Entry<String, SleepingInfoArrangement> sleepingInfo : roomSleepingInfo.getValue().get(0).getSpaceIdToSleepingInfoArrangementMap().entrySet()) {
								bedCount += sleepingInfo.getValue().getBed();
								bedRoomCount += sleepingInfo.getValue().getBedRoom();
								baseOccupancy += sleepingInfo.getValue().getGuest();
								maxGuest += sleepingInfo.getValue().getGuest();
								maxCapacity += sleepingInfo.getValue().getMaxCapacity();
								for (String bedType : sleepingInfo.getValue().getBedInfos().keySet()) {
									if (!bedInfoMap.containsKey(bedType))
										bedInfoMap.put(bedType, 0);
									bedInfoMap.put(bedType, bedInfoMap.get(bedType) + sleepingInfo.getValue().getBedInfos().get(bedType));
								}
							}
						}
						bathroomCount = ((exactRoom.getBathroomCount() != null ? exactRoom.getBathroomCount() * bathroomMultiplier : 0));
						stayDetail = new StayDetail();
						stayDetail.setBedRoom(bedRoomCount);
						stayDetail.setBed(bedCount);
						stayDetail.setMaxGuests(maxGuest);
						stayDetail.setBaseGuests(baseOccupancy);
						stayDetail.setExtraBeds(extraBeds);
						stayDetail.setBathroom(bathroomCount);
						stayDetail.setMaxCapacity(maxCapacity);
						stayDetail.setBedInfoMap(bedInfoMap);
						stayDetail.setBedInfoText(utility.createBedInfoTextFromBedInfoMap(bedInfoMap));
					}
				} else
					stayDetail = roomDetailsList.get(0).getStayDetail();
			}
		}
		else if (CollectionUtils.isNotEmpty(searchRoomsResponse.getRecommendedCombos())) {
			if (CollectionUtils.isNotEmpty(searchRoomsResponse.getRecommendedCombos())) {
				List<RoomDetails> roomDetailsList = searchRoomsResponse.getRecommendedCombos().get(0).getRooms();
				boolean zeroBedRoomCount = false;
				if (CollectionUtils.isNotEmpty(roomDetailsList)) {
					if (roomDetailsList.get(0).getPrivateSpaces() != null) {
						// It means It's an Migrated Property , So need to compute On the basis PrivateSpace;
						for (RoomDetails roomDetails : roomDetailsList) {
							int bathRoomMultiplier = 0;
							if (roomDetails.getPrivateSpaces() != null && CollectionUtils.isNotEmpty(roomDetails.getPrivateSpaces().getSpaces())) {
								for (Space space : roomDetails.getPrivateSpaces().getSpaces()) {
									if (CollectionUtils.isNotEmpty(space.getSleepingInfoArrangement())) {
										if ((Constants.BEDROOM.equalsIgnoreCase(space.getSpaceType()) || LIVING_ROOM.equalsIgnoreCase(space.getSpaceType())) && bathRoomMultiplier == 0) {
											bathRoomMultiplier = space.getSleepingInfoArrangement().size();
										}
										for (SleepingInfoArrangement sleepingInfoArrangement : space.getSleepingInfoArrangement()) {
											bedCount += sleepingInfoArrangement.getBed();
											bedRoomCount += sleepingInfoArrangement.getBedRoom();
											maxGuest += sleepingInfoArrangement.getGuest();
											maxCapacity += sleepingInfoArrangement.getMaxCapacity();
											for (String bedType : sleepingInfoArrangement.getBedInfos().keySet()) {
												if (!bedInfoMap.containsKey(bedType))
													bedInfoMap.put(bedType, 0);
												bedInfoMap.put(bedType, bedInfoMap.get(bedType) + sleepingInfoArrangement.getBedInfos().get(bedType));
											}
										}
									}
								}
							}
							bathroomCount += ((roomDetails.getBathroomCount() != null ? roomDetails.getBathroomCount() * bathRoomMultiplier : 0));
						}
					} else
						if (roomDetailsList.get(0).getPrivateSpacesV2() != null && hotelRates != null && hotelRates.getRecommendedRoomTypeDetails() != null &&
							MapUtils.isNotEmpty(hotelRates.getRecommendedRoomTypeDetails().getRoomToSleepingInfoArrangementMap())) {
						for (RoomDetails roomDetails : roomDetailsList) {
							for (Map.Entry<String, List<RoomSleepingInfoLayout>> roomSleepingInfo : hotelRates.getRecommendedRoomTypeDetails().getRoomToSleepingInfoArrangementMap().entrySet()) {
								String roomCode = roomSleepingInfo.getKey().split(PIPE_SEPARATOR_WITH_BACKSLASH).length == 3 ? roomSleepingInfo.getKey().split(PIPE_SEPARATOR_WITH_BACKSLASH)[0] : StringUtils.EMPTY;
								if (StringUtils.isNotEmpty(roomCode) && CollectionUtils.isNotEmpty(roomSleepingInfo.getValue()) && roomCode.equals(roomDetails.getRoomCode())) {
									bathroomCount += (roomDetails.getBathroomCount() != null ? roomDetails.getBathroomCount() : 0);
									for (Map.Entry<String, SleepingInfoArrangement> sleepingInfo : roomSleepingInfo.getValue().get(0).getSpaceIdToSleepingInfoArrangementMap().entrySet()) {
										bedCount += sleepingInfo.getValue().getBed();
										bedRoomCount += sleepingInfo.getValue().getBedRoom();
										baseOccupancy += sleepingInfo.getValue().getGuest();
										maxGuest += sleepingInfo.getValue().getGuest();
										maxCapacity += sleepingInfo.getValue().getMaxCapacity();
										for (String bedType : sleepingInfo.getValue().getBedInfos().keySet()) {
											if (!bedInfoMap.containsKey(bedType))
												bedInfoMap.put(bedType, 0);
											bedInfoMap.put(bedType, bedInfoMap.get(bedType) + sleepingInfo.getValue().getBedInfos().get(bedType));
										}
									}
								}
							}
						}
					} else {
							if (!modifyStayDetailsForIH)
							// It's an Un-Migrated Property So read always from roomInfo Node;
							{
								for (RoomDetails roomDetails : roomDetailsList) {
									StayDetail roomStayDetail = roomDetails.getStayDetail();
									if (roomStayDetail != null) {
										if (roomStayDetail.getBed() != null) {
											bedCount += roomStayDetail.getBed();
										}
										if (roomStayDetail.getBedRoom() != null && roomStayDetail.getBedRoom() > 0) {
											bedRoomCount += roomStayDetail.getBedRoom();
										} else {
											zeroBedRoomCount = true;
										}
										if (roomStayDetail.getMaxGuests() != null) {
											maxGuest += roomStayDetail.getMaxGuests();
										}
										if (roomStayDetail.getExtraBeds() != null) {
											extraBeds += roomStayDetail.getExtraBeds();
										}
										if (roomStayDetail.getBaseGuests() != null) {
											baseOccupancy += roomStayDetail.getBaseGuests();
										}
										if (roomStayDetail.getBathroom() != null) {
											bathroomCount += roomStayDetail.getBathroom();
										}
										if (roomStayDetail.getMaxCapacity() != null) {
											maxCapacity += roomStayDetail.getMaxCapacity();
										}
									}

								}
							}
					}
					stayDetail = new StayDetail();
					if (!zeroBedRoomCount) {
						stayDetail.setBedRoom(bedRoomCount);
					}
					stayDetail.setBed(bedCount);
					stayDetail.setMaxGuests(maxGuest);
					stayDetail.setBaseGuests(baseOccupancy);
					stayDetail.setExtraBeds(extraBeds);
					stayDetail.setMaxCapacity(maxCapacity);
					stayDetail.setBathroom(bathroomCount);
					stayDetail.setBedInfoMap(bedInfoMap);
					stayDetail.setBedInfoText(utility.createBedInfoTextFromBedInfoMap(bedInfoMap));
				}
			}
		}
		if(stayDetail!=null && StringUtils.isNotBlank(propertyType) && (propertyType.equalsIgnoreCase(Constants.PROPERTY_TYPE_HOSTEL) || propertyType.equalsIgnoreCase(Constants.PROPERTY_TYPE_HOMESTAY)) && (sellableCombo==1 || sellableCombo==3))
			stayDetail.setBedRoom(0);
		if(stayDetail!=null && hotelRates!=null){
			// we will pass propertyTypeMergedName in arguments
			stayDetail.setStayTypeInfo(buildStayTypeInfo(hotelRates.getSellableUnit(),propertyType, roomInfo.getTitle(), serviceApartment));
		}
		roomInfo.setStayDetail(stayDetail);
		roomInfo.setBedInfoText((stayDetail!=null?stayDetail.getBedInfoText():null));
	}

	private void buildStayInfoList(StayDetail stayDetail, HotelsRoomInfoResponseEntity hotelsRoomInfoResponseEntity, SearchRoomsResponse searchRoomsResponse, HotelRates hotelRates) {
		if (stayDetail != null && hotelsRoomInfoResponseEntity != null && searchRoomsResponse != null) {
			String client = MDC.get(MDCHelper.MDCKeys.CLIENT.getStringValue());
			boolean isRequestFromApps = Constants.ANDROID.equalsIgnoreCase(client) || Constants.DEVICE_IOS.equalsIgnoreCase(client);

			List<StayInfo> stayInfoList = new ArrayList<>();
			// Cases where exact match was found for eg. 1 room 2 adults
			if (CollectionUtils.isNotEmpty(searchRoomsResponse.getExactRooms()) && searchRoomsResponse.getExactRooms().get(0) != null) {
				RoomDetails roomDetails = searchRoomsResponse.getExactRooms().get(0);
				if (roomDetails != null) {
					if(isRequestFromApps)
					{
						if (roomDetails.getBedroomCount() != null && roomDetails.getBedroomCount() > 0 && (
								Objects.nonNull(hotelRates) &&
								(!PROPERTY_TYPE_HOSTEL.equalsIgnoreCase(hotelRates.getPropertyType())))) {
							StayInfo stayInfo = null;
							if (roomDetails.getBedroomCount() == 1) {
								stayInfo = new StayInfo();
								String stayInfoText = "";
								String polyglotSleepsText = "";
								stayInfoText = "1" + SPACE + polyglotService.getTranslatedData(SINGLE_BEDROOM_TITLE);
								if (roomDetails.getStayDetail() != null && roomDetails.getMaxGuest() != null && roomDetails.getMaxGuest() > 0) {
									polyglotSleepsText = roomDetails.getMaxGuest() == 1 ? polyglotService.getTranslatedData(SPACE_SINGLE_OCCUPANCY_TEXT) : polyglotService.getTranslatedData(SPACE_OCCUPANCY_TEXT);
									polyglotSleepsText = polyglotSleepsText.replace(OCCUPANCY_PARAMETER, String.valueOf(roomDetails.getMaxGuest()));
								}
								RoomInfo roomInfos = hotelsRoomInfoResponseEntity.getHtlRmInfo().get(0).getHotelRoomInfo().get(roomDetails.getRoomCode());
								String bedNamesText = getBedNamesFromExternalVendor(roomInfos);

								if (StringUtils.isNotEmpty(bedNamesText)) {
									stayInfoText += HYPHEN_SPACE + bedNamesText;
								}

								if (StringUtils.isNotEmpty(polyglotSleepsText)) {
									if (StringUtils.isNotEmpty(bedNamesText)) {
										stayInfoText += COMMA_SPACE + polyglotSleepsText;
									} else {
										stayInfoText += HYPHEN_SPACE + polyglotSleepsText;
									}
								}
								stayInfo.setInfoText(stayInfoText);
								stayInfo.setIconUrl(Constants.CLIENT_DESKTOP.equalsIgnoreCase(client) ? bedroomStayInfoIcon : stayInfoIcon);
							} else if (roomDetails.getBedroomCount() > 1) {
								stayInfo = new StayInfo();
								String stayInfoText = "";
								String polyglotSleepsText = "";
								stayInfoText = roomDetails.getBedroomCount() + SPACE + polyglotService.getTranslatedData(BEDROOM_TITLE);
								RoomInfo roomInfos = hotelsRoomInfoResponseEntity.getHtlRmInfo().get(0).getHotelRoomInfo().get(roomDetails.getRoomCode());
								String bedCountText = getBedCountInfoFromRoomInfo(roomInfos, roomDetails.getBedroomCount());
								if (roomDetails.getStayDetail() != null && roomDetails.getMaxGuest() != null && roomDetails.getMaxGuest() > 0) {
									polyglotSleepsText = roomDetails.getMaxGuest() == 1 ? polyglotService.getTranslatedData(SPACE_SINGLE_OCCUPANCY_TEXT) : polyglotService.getTranslatedData(SPACE_OCCUPANCY_TEXT);
									polyglotSleepsText = polyglotSleepsText.replace(OCCUPANCY_PARAMETER, String.valueOf(roomDetails.getMaxGuest()));
								}

								if (StringUtils.isNotEmpty(bedCountText)) {
									stayInfoText += HYPHEN_SPACE + bedCountText;
								}

								if (StringUtils.isNotEmpty(polyglotSleepsText)) {
									if (StringUtils.isNotEmpty(bedCountText)) {
										stayInfoText += COMMA_SPACE + polyglotSleepsText;
									} else {
										stayInfoText += HYPHEN_SPACE + polyglotSleepsText;
									}
								}
								stayInfo.setInfoText(stayInfoText);
								stayInfo.setIconUrl(Constants.CLIENT_DESKTOP.equalsIgnoreCase(client) ? bedroomStayInfoIcon : stayInfoIcon);
							}
							if (stayInfo != null) {
								stayInfoList.add(stayInfo);
							}
						} else {
							RoomInfo roomInfos = hotelsRoomInfoResponseEntity.getHtlRmInfo().get(0).getHotelRoomInfo().get(roomDetails.getRoomCode());
							String bedNamesText= "";
							if(Objects.nonNull(hotelRates) && !PROPERTY_TYPE_HOSTEL.equalsIgnoreCase(hotelRates.getPropertyType())) {
								bedNamesText = getBedNamesFromExternalVendor(roomInfos);
							}
							String polyglotSleepsText = "";

							if (roomDetails.getStayDetail() != null && roomDetails.getMaxGuest() != null && roomDetails.getMaxGuest() > 0) {
								polyglotSleepsText = roomDetails.getMaxGuest() == 1 ? polyglotService.getTranslatedData(SPACE_SINGLE_OCCUPANCY_TEXT) : polyglotService.getTranslatedData(SPACE_OCCUPANCY_TEXT);
								polyglotSleepsText = polyglotSleepsText.replace(OCCUPANCY_PARAMETER, String.valueOf(roomDetails.getMaxGuest()));
							}
							StayInfo stayInfo = new StayInfo();
							String stayInfoText = "";
							if (StringUtils.isNotEmpty(bedNamesText)) {
								stayInfoText += bedNamesText;
							}
							if (StringUtils.isNotEmpty(polyglotSleepsText)) {
								if (StringUtils.isNotEmpty(bedNamesText)) {
									stayInfoText += COMMA_SPACE + polyglotSleepsText;
								} else {
									stayInfoText += polyglotSleepsText;
								}
							}
							if (StringUtils.isNotEmpty(stayInfoText)) {
								stayInfo.setInfoText(stayInfoText);
								stayInfo.setIconUrl(Constants.CLIENT_DESKTOP.equalsIgnoreCase(client) ? bedroomStayInfoIcon : stayInfoIcon);
							}
							stayInfoList.add(stayInfo);
						}
					}
					int bathroomCount = 0;
					if (roomDetails.getBathroomCount() != null && roomDetails.getBathroomCount() > 0) {
						bathroomCount = roomDetails.getBathroomCount();
					} else {
						RoomInfo roomInfos = hotelsRoomInfoResponseEntity.getHtlRmInfo().get(0).getHotelRoomInfo().get(roomDetails.getRoomCode());
						bathroomCount = getBathroomCountFromRoomInfo(roomInfos);
					}
					if (bathroomCount > 0 &&
							(Objects.nonNull(hotelRates) && !PROPERTY_TYPE_HOSTEL.equalsIgnoreCase(hotelRates.getPropertyType()))) {
						StayInfo bathRoomInfo = new StayInfo();
						String stayInfoText = "";
						if (bathroomCount == 1) {
							stayInfoText = bathroomCount + SPACE + Constants.BATHROOM;
						} else {
                            stayInfoText = bathroomCount + SPACE + Constants.BATHROOMS;
                        }
						bathRoomInfo.setInfoText(stayInfoText);
						bathRoomInfo.setIconUrl(Constants.CLIENT_DESKTOP.equalsIgnoreCase(client) ? bathroomInfoIcon : stayInfoIcon);
						stayInfoList.add(bathRoomInfo);
					}
					addKitchenRoomInfoOrLivingRoomInfo(hotelsRoomInfoResponseEntity, roomDetails, client, stayInfoList);
					if (Constants.PROPERTY_TYPE_HOSTEL.equalsIgnoreCase(hotelRates.getPropertyType()) && hotelRates.getSellableCombo() == 1) {
						StayInfo stayInfo = new StayInfo();
						stayInfo.setInfoText(polyglotService.getTranslatedData(HOSTEL_ROOMS_AVAILABLE_TEXT));
						stayInfo.setIconUrl(Constants.CLIENT_DESKTOP.equalsIgnoreCase(client) ? bedroomStayInfoIcon : stayInfoIcon);
						stayInfoList.add(stayInfo);
					}

				}

			}
			else if (CollectionUtils.isNotEmpty(searchRoomsResponse.getRecommendedCombos())) {
				//Homogenous Combo (same type of rooms size == 1)
				if (searchRoomsResponse.getRecommendedCombos().get(0).getRooms().size() == 1) {
					RoomDetails roomDetails = searchRoomsResponse.getRecommendedCombos().get(0).getRooms().get(0);
					if (roomDetails != null) {
						if (isRequestFromApps) {
						if (roomDetails.getBedroomCount() != null && roomDetails.getBedroomCount() > 0 && (
								Objects.nonNull(hotelRates) &&
								(!PROPERTY_TYPE_HOSTEL.equalsIgnoreCase(hotelRates.getPropertyType())))) {
							StayInfo stayInfo = null;
							if (roomDetails.getBedroomCount() == 1) {
								stayInfo = new StayInfo();
								String stayInfoText = "";
								String polyglotSleepsText = "";
								stayInfoText = "1" + SPACE + polyglotService.getTranslatedData(SINGLE_BEDROOM_TITLE);
								RoomInfo roomInfos = hotelsRoomInfoResponseEntity.getHtlRmInfo().get(0).getHotelRoomInfo().get(roomDetails.getRoomCode());
								String bedNamesText = getBedNamesFromExternalVendor(roomInfos);
								if (roomDetails.getStayDetail() != null && roomDetails.getMaxGuest() != null && roomDetails.getMaxGuest() > 0) {
									polyglotSleepsText = roomDetails.getMaxGuest() == 1 ? polyglotService.getTranslatedData(SPACE_SINGLE_OCCUPANCY_TEXT) : polyglotService.getTranslatedData(SPACE_OCCUPANCY_TEXT);
									polyglotSleepsText = polyglotSleepsText.replace(OCCUPANCY_PARAMETER, String.valueOf(roomDetails.getMaxGuest()));
								}
								if (StringUtils.isNotEmpty(bedNamesText)) {
									stayInfoText += HYPHEN_SPACE + bedNamesText;
								}

								if (StringUtils.isNotEmpty(polyglotSleepsText)) {
									if (StringUtils.isNotEmpty(bedNamesText)) {
										stayInfoText += COMMA_SPACE + polyglotSleepsText;
									} else {
										stayInfoText += HYPHEN_SPACE + polyglotSleepsText;
									}
								}
								stayInfo.setInfoText(stayInfoText);
									stayInfo.setIconUrl(Constants.CLIENT_DESKTOP.equalsIgnoreCase(client) ? bedroomStayInfoIcon : stayInfoIcon);
								} else if (roomDetails.getBedroomCount() > 1) {
								stayInfo = new StayInfo();
								String stayInfoText = "";
								String polyglotSleepsText = "";
								stayInfoText = roomDetails.getBedroomCount() + SPACE + polyglotService.getTranslatedData(BEDROOM_TITLE);
								RoomInfo roomInfos = hotelsRoomInfoResponseEntity.getHtlRmInfo().get(0).getHotelRoomInfo().get(roomDetails.getRoomCode());
								String bedCountText = getBedCountInfoFromRoomInfo(roomInfos, roomDetails.getBedroomCount());
								if (roomDetails.getStayDetail() != null && roomDetails.getMaxGuest() != null && roomDetails.getMaxGuest() > 0) {
									polyglotSleepsText = roomDetails.getMaxGuest() == 1 ? polyglotService.getTranslatedData(SPACE_SINGLE_OCCUPANCY_TEXT) : polyglotService.getTranslatedData(SPACE_OCCUPANCY_TEXT);
									polyglotSleepsText = polyglotSleepsText.replace(OCCUPANCY_PARAMETER, String.valueOf(roomDetails.getMaxGuest()));
								}
								if (StringUtils.isNotEmpty(bedCountText)) {
									stayInfoText += HYPHEN_SPACE + bedCountText;
								}
								if (StringUtils.isNotEmpty(polyglotSleepsText)) {
									if (StringUtils.isNotEmpty(bedCountText)) {
										stayInfoText += COMMA_SPACE + polyglotSleepsText;
									} else {
										stayInfoText += HYPHEN_SPACE + polyglotSleepsText;
									}
								}
								stayInfo.setInfoText(stayInfoText);
								stayInfo.setIconUrl(Constants.CLIENT_DESKTOP.equalsIgnoreCase(client) ? bedroomStayInfoIcon : stayInfoIcon);
							}
							if (stayInfo != null) {
								stayInfoList.add(stayInfo);
							}
						} else {
							RoomInfo roomInfos = hotelsRoomInfoResponseEntity.getHtlRmInfo().get(0).getHotelRoomInfo().get(roomDetails.getRoomCode());
							String bedNamesText= "";
							if(Objects.nonNull(hotelRates) && !PROPERTY_TYPE_HOSTEL.equalsIgnoreCase(hotelRates.getPropertyType())) {
								bedNamesText = getBedNamesFromExternalVendor(roomInfos);
							}
							String polyglotSleepsText = "";

							if (roomDetails.getStayDetail() != null && roomDetails.getMaxGuest() != null && roomDetails.getMaxGuest() > 0) {
								polyglotSleepsText = roomDetails.getMaxGuest() == 1 ? polyglotService.getTranslatedData(SPACE_SINGLE_OCCUPANCY_TEXT) : polyglotService.getTranslatedData(SPACE_OCCUPANCY_TEXT);
								polyglotSleepsText = polyglotSleepsText.replace(OCCUPANCY_PARAMETER, String.valueOf(roomDetails.getMaxGuest()));
							}
							StayInfo stayInfo = new StayInfo();
							String stayInfoText = "";
							if (StringUtils.isNotEmpty(bedNamesText)) {
								stayInfoText += bedNamesText;
							}
							if (StringUtils.isNotEmpty(polyglotSleepsText)) {
								if (StringUtils.isNotEmpty(bedNamesText)) {
									stayInfoText += COMMA_SPACE + polyglotSleepsText;
								} else {
									stayInfoText += polyglotSleepsText;
								}
							}
							if (StringUtils.isNotEmpty(stayInfoText)) {
								stayInfo.setInfoText(stayInfoText);
									stayInfo.setIconUrl(Constants.CLIENT_DESKTOP.equalsIgnoreCase(client) ? bedroomStayInfoIcon : stayInfoIcon);
							}
							stayInfoList.add(stayInfo);
						}
						}
						int bathroomCount = 0;
						if (roomDetails.getBathroomCount() != null && roomDetails.getBathroomCount() > 0) {
							bathroomCount = roomDetails.getBathroomCount();
						} else {
							RoomInfo roomInfos = hotelsRoomInfoResponseEntity.getHtlRmInfo().get(0).getHotelRoomInfo().get(roomDetails.getRoomCode());
							bathroomCount = getBathroomCountFromRoomInfo(roomInfos);
						}
						if (bathroomCount > 0 &&
								(Objects.nonNull(hotelRates) && !PROPERTY_TYPE_HOSTEL.equalsIgnoreCase(hotelRates.getPropertyType()))) {
							StayInfo bathRoomInfo = new StayInfo();
							String stayInfoText = "";
							if (bathroomCount == 1) {
								stayInfoText = bathroomCount + SPACE + Constants.BATHROOM;
							} else {
								stayInfoText = bathroomCount + SPACE + Constants.BATHROOMS;
							}
							bathRoomInfo.setInfoText(stayInfoText);
							bathRoomInfo.setIconUrl(Constants.CLIENT_DESKTOP.equalsIgnoreCase(client) ? bathroomInfoIcon : stayInfoIcon);
							stayInfoList.add(bathRoomInfo);
						}
						addKitchenRoomInfoOrLivingRoomInfo(hotelsRoomInfoResponseEntity, roomDetails, client, stayInfoList);
						if (Constants.PROPERTY_TYPE_HOSTEL.equalsIgnoreCase(hotelRates.getPropertyType()) && hotelRates.getSellableCombo() == 1) {
							StayInfo stayInfo = new StayInfo();
							stayInfo.setInfoText(polyglotService.getTranslatedData(HOSTEL_ROOMS_AVAILABLE_TEXT));
							stayInfo.setIconUrl(Constants.CLIENT_DESKTOP.equalsIgnoreCase(client) ? bedroomStayInfoIcon : stayInfoIcon);
							stayInfoList.add(stayInfo);

						}
					}
				}
				//Heterogeneous Combo (different type of rooms size > 1)
				else {
					stayInfoList = new ArrayList<>();
					if (!Constants.CLIENT_DESKTOP.equalsIgnoreCase(client)) {
						for (RoomDetails roomDetails : searchRoomsResponse.getRecommendedCombos().get(0).getRooms()) {
							StayInfo stayInfo = new StayInfo();
							Integer roomCount = getPropertyCountFromRoomTariff(roomDetails);
							stayInfo.setInfoText(roomCount != null && roomCount > 1 ? roomCount + SPACE_X_SPACE + roomDetails.getRoomName() : roomDetails.getRoomName());
							stayInfo.setIconUrl(stayInfoIcon);
							if (stayInfo != null) {
								stayInfoList.add(stayInfo);
							}
						}
					}

				}
			}

			stayDetail.setStayInfoList(stayInfoList);
		}
	}

	private void addKitchenRoomInfoOrLivingRoomInfo(HotelsRoomInfoResponseEntity hotelsRoomInfoResponseEntity, RoomDetails roomDetails, String client, List<StayInfo> stayInfoList) {
		boolean isKitchenPresent = checkIfKitchenPresentAmenities(hotelsRoomInfoResponseEntity.getHtlRmInfo().get(0).getHotelRoomInfo().get(roomDetails.getRoomCode()));
		Boolean livingRoomPresent = hotelsRoomInfoResponseEntity.getHtlRmInfo().get(0).getHotelRoomInfo().get(roomDetails.getRoomCode()).getLivingRoomPresent();
		String	livingRoomTitle = polyglotService.getTranslatedData(LIVING_ROOM_TITLE);
		if (isKitchenPresent) {
			String	kitchenRoomTitle = polyglotService.getTranslatedData(KITCHENETTE_TITLE);
			StayInfo kitchenInfo = new StayInfo();
			kitchenInfo.setInfoText(kitchenRoomTitle);
			kitchenInfo.setIconUrl(Constants.CLIENT_DESKTOP.equalsIgnoreCase(client) ? kitchenStayInfoIcon : stayInfoIcon);
			stayInfoList.add(kitchenInfo);
		}
		if (livingRoomPresent!=null && livingRoomPresent) {
				StayInfo livingRoom = new StayInfo();
				livingRoom.setIconUrl(Constants.CLIENT_DESKTOP.equalsIgnoreCase(client) ? livingroomStayInfoIcon : stayInfoIcon);
				livingRoom.setInfoText(livingRoomTitle);
				stayInfoList.add(livingRoom);
		}
	}

	private boolean checkIfKitchenPresentAmenities(RoomInfo roomInfo) {
		if (roomInfo != null && CollectionUtils.isNotEmpty(roomInfo.getFacilityWithGrp())) {
			for (com.mmt.model.FacilityGroup facilityWithGrp : roomInfo.getFacilityWithGrp()) {
				if (facilityWithGrp != null && StringUtils.isNotEmpty(facilityWithGrp.getName()) && facilityWithGrp.getName().toLowerCase().contains("kitchen")) {
					return true;
				}
			}
		}
		return false;
	}

	private Integer getPropertyCountFromRoomTariff(RoomDetails roomDetails) {
		Integer roomCount = 0;
		if (roomDetails != null && CollectionUtils.isNotEmpty(roomDetails.getRatePlans()) && CollectionUtils.isNotEmpty(roomDetails.getRatePlans().get(0).getTariffs())) {
			Tariff tariff = roomDetails.getRatePlans().get(0).getTariffs().get(0);
			if (tariff.getOccupancydetails() != null && tariff.getOccupancydetails().getRoomCount() != null) {
				roomCount = tariff.getOccupancydetails().getRoomCount();
			}
		}
		return roomCount;
	}

	private int getBathroomCountFromRoomInfo(RoomInfo roomInfos) {
		if (roomInfos != null && CollectionUtils.isNotEmpty(roomInfos.getExternalVendorBedRoomInfoList())) {
			for (ExternalVendorBedRoomInfo externalVendorBedRoomInfo : roomInfos.getExternalVendorBedRoomInfoList()) {
				if (externalVendorBedRoomInfo != null && StringUtils.isNotEmpty(externalVendorBedRoomInfo.getBedRoomName()) && externalVendorBedRoomInfo.getBedRoomName().toLowerCase().contains("bathroom")) {
					if (StringUtils.isNotEmpty(externalVendorBedRoomInfo.getBedRoomDescription())) {
						return Integer.parseInt(externalVendorBedRoomInfo.getBedRoomDescription());
					}
				}
			}
		}
		return 0;
	}

	private String getBedCountInfoFromRoomInfo(RoomInfo roomInfos, Integer bedroomCount) {
		String bedCountText = "";
		if (roomInfos != null && CollectionUtils.isNotEmpty(roomInfos.getBeds())) {
			int bedCount = 0;
			for (SleepingArrangement sleepingArrangement : roomInfos.getBeds()) {
				bedCount += sleepingArrangement.getCount();
			}
			bedCount *= bedroomCount;
			if (bedCount > 0) {
				bedCountText = bedCount + SPACE + (bedCount > 1 ? BEDS.toLowerCase() : BED.toLowerCase());
			}

		}
		return bedCountText;
	}

	private String getBedNamesFromExternalVendor(RoomInfo roomInfos) {
		String bedNamesText = "";
		if (roomInfos != null) {
			List<ExternalVendorBedRoomInfo> externalVendorBedRoomInfoList = roomInfos.getExternalVendorBedRoomInfoList();
			if(CollectionUtils.isNotEmpty(externalVendorBedRoomInfoList)) {
				for (ExternalVendorBedRoomInfo externalVendorBedRoomInfo : externalVendorBedRoomInfoList) {
					if (externalVendorBedRoomInfo != null && StringUtils.isNotEmpty(externalVendorBedRoomInfo.getBedRoomDescription()) && externalVendorBedRoomInfo.getBedRoomDescription().toLowerCase().contains("bed")) {
						if (StringUtils.isNotBlank(bedNamesText)) {
							bedNamesText += COMMA_SPACE;
						}
						bedNamesText += externalVendorBedRoomInfo.getBedRoomDescription();
					}
				}
			}
		}
		return bedNamesText;
	}

	private StayTypeInfo buildStayTypeInfo(String propertySellableUnit, String propertyType, String title, boolean serviceApartment) {
		if (MapUtils.isEmpty(actionInfoMap) || StringUtils.isEmpty(propertySellableUnit)) {
			return null;
		}
		StayTypeInfo stayTypeInfo = null;
		if (SELLABLE_UNIT_ENTIRE.equalsIgnoreCase(propertySellableUnit)) {
			stayTypeInfo = new StayTypeInfo();
			if (StringUtils.isNotEmpty(propertyType) && PROPERTY_TYPE_APARTMENT.contains(propertyType.toLowerCase()) && serviceApartment) {
				stayTypeInfo = actionInfoMap.get(APARTMENT_SELLABLE_UNIT_ENTIRE_CONSUL_KEY);
			} else {
				stayTypeInfo = actionInfoMap.get(SELLABLE_UNIT_ENTIRE_CONSUL_KEY);
				stayTypeInfo.setTitle(MessageFormat.format(PROPERTY_STAY_TYPE_TITLE_ENTIRE_V1, propertyType));
			}
		} else if (SELLABLE_UNIT_ROOM.equalsIgnoreCase(propertySellableUnit) && !PROPERTY_TYPE_HOSTEL.equalsIgnoreCase(propertyType)) {
			stayTypeInfo = new StayTypeInfo();
			if (StringUtils.isNotEmpty(propertyType) && PROPERTY_TYPE_APARTMENT.contains(propertyType.toLowerCase()) && serviceApartment) {
				stayTypeInfo = actionInfoMap.get(APARTMENT_SELLABLE_UNIT_ROOM_CONSUL_KEY);
			} else {
				stayTypeInfo = actionInfoMap.get(SELLABLE_UNIT_ROOM_CONSUL_KEY);
				stayTypeInfo.setTitle(MessageFormat.format(PROPERTY_STAY_TYPE_TITLE_ROOM_V1, utility.buildStringTypeWithVowel(propertyType)));
			}
		}
		return stayTypeInfo;
	}



	/***
	 * to build sleepInfoText(&additionalSleepInfoText) from stayDetail(maxGuests & maxCapacity) node.
	 * maxCapacity: the maximum number of people that could be accommodated in all selected rooms
	 * maxGuests: max(base guests, requested guests) in hotel
	 * stayDetail must be built before calling this method.
	 * for desktop, two nodes are used sleepInfoText(sleeps x guests) & additionalSleepInfoText(can accommodate y more..)
	 * for all other clients, single node sleepInfoText contains entire data
	 * @param stayDetail : hotel stay detail node containing maxGuests & maxCapacity nodes
	 * @param sellableCombo
	 * @return
	 */

	/*** Possible combination value of sellableCombo, Also sellableCombo represent the combination of bedroom and bed possibility in the lowestRoomTypeCode
	 *  sellableType[Bed] | sellableType[Bedroom] | sellableComboValue
	 *  false             |   false               |  0
	 *  true              |   false               |  1
	 *  false             |   true                |  2
	 *  true              |   true                |  3
	 */
	private void buildSleepInfoText(StayDetail stayDetail, boolean isOHSExpEnable, int sellableCombo, Pair<Boolean, Boolean> bedAndRoomPresent, String freeChildText,boolean isNewDetailPageTrue) {
		String client = MDC.get(MDCHelper.MDCKeys.CLIENT.getStringValue());
		if (StringUtils.isNotBlank(client) && stayDetail != null && stayDetail.getMaxGuests() != null && stayDetail.getMaxGuests() > 0) {
			String polyglotSleepsText = stayDetail.getMaxGuests() == 1 ? polyglotService.getTranslatedData(SPACE_SINGLE_OCCUPANCY_TEXT) : polyglotService.getTranslatedData(SPACE_OCCUPANCY_TEXT);
			if (StringUtils.isNotBlank(polyglotSleepsText)) {
				String sleepsText = polyglotSleepsText.replace(OCCUPANCY_PARAMETER, String.valueOf(stayDetail.getMaxGuests()));
				if (Constants.CLIENT_DESKTOP.equalsIgnoreCase(client) || isNewDetailPageTrue) {
					stayDetail.setSleepInfoText(sleepsText);
				} else {
					if(isNewDetailPageTrue && (Constants.ANDROID.equalsIgnoreCase(client) || Constants.DEVICE_IOS.equalsIgnoreCase(client) || Constants.DEVICE_OS_PWA.equalsIgnoreCase(client))) {
						stayDetail.setSleepInfoText(sleepsText);
					}else{
						stayDetail.setSleepInfoText(OPEN_BOLD_TAG + sleepsText + CLOSE_BOLD_TAG);
					}
				}

				if ((stayDetail.getMaxCapacity() != null && stayDetail.getMaxCapacity() > stayDetail.getMaxGuests()) || StringUtils.isNotEmpty(freeChildText)) {
					int extraGuests = stayDetail.getMaxCapacity() - stayDetail.getMaxGuests();
					String polyglotExtraGuestsText = extraGuests == 1 ? polyglotService.getTranslatedData(SPACE_SINGLE_OCCUPANCY_EXTRA_GUESTS) : polyglotService.getTranslatedData(SPACE_OCCUPANCY_EXTRA_GUESTS);
					if (StringUtils.isNotBlank(polyglotSleepsText)) {
						String extraGuestsText = StringUtils.isNotEmpty(freeChildText) ? freeChildText : polyglotExtraGuestsText.replace(EXTRA_PARAMETER, String.valueOf(extraGuests));
						if (Constants.CLIENT_DESKTOP.equalsIgnoreCase(MDC.get(MDCHelper.MDCKeys.CLIENT.getStringValue()))) {
							stayDetail.setSleepInfoText(sleepsText);
							stayDetail.setAdditionalSleepInfoText(extraGuestsText);
						} else {
							if(isNewDetailPageTrue && (Constants.ANDROID.equalsIgnoreCase(client) || Constants.DEVICE_IOS.equalsIgnoreCase(client) || Constants.DEVICE_OS_PWA.equalsIgnoreCase(client))) {
								stayDetail.setSleepInfoText(sleepsText + COMMA + SPACE + extraGuestsText);
							} else{
								stayDetail.setSleepInfoText(OPEN_BOLD_TAG + sleepsText + CLOSE_BOLD_TAG + SPACE + BULLET_HTML + SPACE + extraGuestsText);
							}
						}
					}
				}
				if(isOHSExpEnable){
					if(sellableCombo==1){
						// sellableType bed available in lowestRoomTypeCode, So Suggest private rooms
						stayDetail.setBedInfoText(null);
						if (Constants.CLIENT_DESKTOP.equalsIgnoreCase(MDC.get(MDCHelper.MDCKeys.CLIENT.getStringValue()))) {
							stayDetail.setSleepInfoText(null);
							stayDetail.setAdditionalSleepInfoText(null);
							if(bedAndRoomPresent.getValue()){
								stayDetail.setBedAndRoomAvailabilityText(polyglotService.getTranslatedData(HOSTEL_ROOMS_AVAILABLE_TEXT));
							}
						}else{
							if(bedAndRoomPresent.getValue()){
								stayDetail.setSleepInfoText(polyglotService.getTranslatedData(HOSTEL_ROOMS_AVAILABLE_TEXT));
							}else{
								stayDetail.setSleepInfoText(null);
							}
						}
					}else if(sellableCombo==2){
						// sellableType bedRoom available in lowestRoomTypeCode so suggest shared dorm
						if (Constants.CLIENT_DESKTOP.equalsIgnoreCase(MDC.get(MDCHelper.MDCKeys.CLIENT.getStringValue()))){
							if(bedAndRoomPresent.getKey()){
								stayDetail.setBedAndRoomAvailabilityText(polyglotService.getTranslatedData(HOSTEL_BEDS_AVAILABLE_TEXT));
							}
						}else{
							stayDetail.setBedInfoText(sleepsText);
							if(bedAndRoomPresent.getKey()){
								stayDetail.setSleepInfoText(polyglotService.getTranslatedData(HOSTEL_BEDS_AVAILABLE_TEXT));
							}else{
								stayDetail.setSleepInfoText(null);
							}
						}
					}else{
						stayDetail.setSleepInfoText(null);
						stayDetail.setAdditionalSleepInfoText(null);
						stayDetail.setBedInfoText(null);
					}
				}
			}
		}
	}



	/**
	 * To build BedInfoText, It is added to summarize the bed types and count for property Layout
	 * @param searchRoomsResponse
	 * @param roomInfo
	 */
	@Deprecated
	private void buildBedInfoText(SearchRoomsResponse searchRoomsResponse,SleepingArrangementRoomInfo roomInfo){
		String bedInfoText= null;
		if (searchRoomsResponse!=null && CollectionUtils.isNotEmpty(searchRoomsResponse.getExactRooms())) {
			bedInfoText = searchRoomsResponse.getExactRooms().get(0).getBedInfoText();
		}
		roomInfo.setBedInfoText(bedInfoText);
	}

	private void buildStayDetails(RoomDetails roomDetails) {
		int roomCount = 1;
		if (CollectionUtils.isNotEmpty(roomDetails.getRatePlans()) && CollectionUtils.isNotEmpty(roomDetails.getRatePlans().get(0).getTariffs())) {
			Tariff tariff = roomDetails.getRatePlans().get(0).getTariffs().get(0);
			if (tariff.getOccupancydetails() != null && tariff.getOccupancydetails().getRoomCount() != null) {
				roomCount = tariff.getOccupancydetails().getRoomCount();
			}
		}
		StayDetail stayDetail = new StayDetail();
		if (roomDetails.getBedCount() != null) {
			stayDetail.setBed(roomCount * roomDetails.getBedCount());
		}
		if (roomDetails.getMaxGuest() != null) {
			stayDetail.setMaxGuests(roomCount * roomDetails.getMaxGuest());
		}
		if (roomDetails.getBedroomCount() != null) {
			stayDetail.setBedRoom(roomCount * roomDetails.getBedroomCount());
		}
		if(roomDetails.getExtraBedCount() != null) {
			stayDetail.setExtraBeds(roomCount * roomDetails.getExtraBedCount());
		}
		if(roomDetails.getBaseGuest() != null) {
			stayDetail.setBaseGuests(roomCount * roomDetails.getBaseGuest());
		}
		if (roomDetails.getBathroomCount() != null) {
			stayDetail.setBathroom(roomCount * roomDetails.getBathroomCount());
		}
		if (roomDetails.getMaxGuest() != null) {
			stayDetail.setMaxCapacity(roomCount * roomDetails.getMaxGuest());
		}
		roomDetails.setStayDetail(stayDetail);
	}

	private void setPackageRoomSpecificInfo(PackageRoomDetails roomDetails, PackageRoomType roomType, boolean isSuperPackage){
		if(roomType==null){
			return;
		}
		roomDetails.setAnimationType(roomType.getAnimationType());
		roomDetails.setCtaText(roomType.getCtaText());
		roomDetails.setDescriptionText(roomType.getDescriptionText());
		roomDetails.setHeader(roomType.getHeader());
		roomDetails.setTitle(roomType.getTitle());
		roomDetails.setIsExtendedStayPackage(roomType.getIsExtendedStayPackage());
		roomDetails.setRecommendText(roomType.getRecommendText());
		roomDetails.setShowGreatValuePackage(!isSuperPackage);
	}

	private List<MediaData> populateMedia(Map<String, RoomInfo> staticRoomInfoMap, List<String> images, String roomCode) {

		List<MediaData> mediaList = new ArrayList<>();

		if (MapUtils.isNotEmpty(staticRoomInfoMap) && staticRoomInfoMap.containsKey(roomCode) && CollectionUtils.isNotEmpty(staticRoomInfoMap.get(roomCode).getRoomLevelVideos())) {
			RoomInfo roomInfo = staticRoomInfoMap.get(roomCode);

			for (VideoInfo videoInfo : roomInfo.getRoomLevelVideos()) {

				MediaData mediaData = new MediaData();
				mediaData.setMediaType(Constants.VIDEO_TYPE);
				mediaData.setUrl(videoInfo.getUrl());
				mediaData.setThumbnailUrl(videoInfo.getThumbnailUrl());
				mediaData.setText(videoInfo.getText());
				mediaList.add(mediaData);
			}


		}

		if (CollectionUtils.isNotEmpty(images)) {

			for (String url : images) {
				MediaData mediaData = new MediaData();
				mediaData.setMediaType(Constants.IMAGE_TYPE);
				mediaData.setUrl(url);
				mediaList.add(mediaData);
			}
		}
		return mediaList;
	}

	protected abstract PersuasionObject createTopRatedPersuasion(boolean isNewDetailsPageDesktop);

	protected abstract PersuasionResponse buildDelayedConfirmationPersuasion(String corpAlias, boolean isMyBizNewDetailsPage);

	protected abstract PersuasionResponse buildSpecialFareTagPersuasion(String corpAlias);

	protected abstract PersuasionResponse buildSpecialFareTagWithInfoPersuasion(String corpAlias,boolean isNewSelectRoomPage);

	protected abstract PersuasionResponse buildConfirmationTextPersuasion(String corpAlias,boolean isNewSelectRoomPage, boolean isMyBizNewDetailsPage);

	protected PersuasionObject createTopRatedPersuasionForMoblie() {
		PersuasionObject persuasionObject = new PersuasionObject();
		persuasionObject.setData(new ArrayList<>());
		persuasionObject.setPlaceholder(Constants.PLACEHOLDER_SELECT_TOP_R1);
		persuasionObject.setTemplate("IMAGE_TEXT_H");
		PersuasionData persuasionData = new PersuasionData();
		PersuasionStyle style = new PersuasionStyle();
		style.setFontSize("SMALL");
		style.setTextColor("#b8860b");
		style.setFontType("B");
		style.setBorderColor("#b8860b");
		persuasionData.setStyle(style);
		persuasionData.setPersuasionType("PEITHO");
		persuasionData.setText(polyglotService.getTranslatedData(ConstantsTranslation.TOP_RATED));
		persuasionObject.setData(Arrays.asList(persuasionData));
		return persuasionObject;
	}

	private void addPersuasion(RoomDetails roomDetails, PersuasionObject persuasionObject) {
		if (roomDetails == null || persuasionObject == null) {
			return;
		}
		try {
			Map<Object, Object> map = new HashMap<>();
			map.put(persuasionObject.getPlaceholder(), persuasionObject);
			if (roomDetails.getRoomPersuasions() == null) {
				roomDetails.setRoomPersuasions(map);
			} else {
				((ObjectNode)roomDetails.getRoomPersuasions()).putPOJO(persuasionObject.getPlaceholder(), persuasionObject);
			}
		} catch (ClassCastException e) {
			LOGGER.error("Persuasion could not be added due to ClassCastException : {} ", e.getMessage());
		} catch (Exception e) {
			LOGGER.error("Persuasion could not be added due to : {} ", e.getMessage());
		}
	}

	private List<RoomDetails> populateSubRooms(String parentRoomCode, Map<String, RoomInfo> staticRoomInfoMap){
		if(MapUtils.isNotEmpty(staticRoomInfoMap) && CollectionUtils.isNotEmpty(staticRoomInfoMap.values())) {
			List<RoomInfo> childRoomInfos = staticRoomInfoMap.values().stream().filter(a-> parentRoomCode.equalsIgnoreCase(a.getParentRoomCode())).collect(Collectors.toList());
			if(CollectionUtils.isNotEmpty(childRoomInfos)){
				List<RoomDetails> roomDetailsList = new ArrayList<>();
				for(RoomInfo roomInfo: childRoomInfos){
					RoomDetails roomDetails = new RoomDetails();
					roomDetails.setMaster(roomInfo.isMaster());
					roomDetails.setMaxAdult(roomInfo.getMaxAdultCount());
					roomDetails.setMaxChild(roomInfo.getMaxChildCount());
					roomDetails.setMaxGuest(roomInfo.getMaxGuestCount());
					/* Below three nodes are duplicate & need to be removed after next client release */
					/* Only Temp addition to fix live bug */
					roomDetails.setMaxGuestCount(roomInfo.getMaxGuestCount());
					roomDetails.setMaxAdultCount(roomInfo.getMaxAdultCount());
					roomDetails.setMaxChildCount(roomInfo.getMaxChildCount());
					/* Above three nodes are duplicate & need to be removed after next client release */
					roomDetails.setBedCount(roomInfo.getBedCount());
					if (StringUtils.isNotBlank(roomInfo.getBedRoomCount()))
						roomDetails.setBedroomCount(NumberUtils.toInt(roomInfo.getBedRoomCount(),0));
					roomDetails.setParentRoomCode(roomInfo.getParentRoomCode());
					roomDetails.setRoomSize(roomInfo.getRoomSize());
					roomDetails.setRoomName(roomInfo.getRoomName());
					roomDetails.setRoomViewName(roomInfo.getRoomViewName());
					roomDetails.setBeds(roomInfo.getBeds());
					roomDetailsList.add(roomDetails);
				}
				return  roomDetailsList;
			}
			return null;
		}
		return null;
	}

	private com.mmt.hotels.clientgateway.response.rooms.Segments getSegments(com.mmt.hotels.model.response.searchwrapper.Segments segments) {
		if (segments == null) {
			return null;
		}
		com.mmt.hotels.clientgateway.response.rooms.Segments segmentsCG = new com.mmt.hotels.clientgateway.response.rooms.Segments();
		segmentsCG.setSegmentsCount(segments.getSegmentsCount());
		segmentsCG.setSegmentList(getSegmentsList(segments.getSegmentList()));
		return segmentsCG;
	}

	private Map<String, com.mmt.hotels.clientgateway.response.rooms.Segment> getSegmentsList(Map<String,com.mmt.hotels.model.response.searchwrapper.Segment> segmentList) {
		if (MapUtils.isEmpty(segmentList))
			return null;
		Map<String, com.mmt.hotels.clientgateway.response.rooms.Segment> segmentMap = new HashMap<>();
		for (Map.Entry<String,com.mmt.hotels.model.response.searchwrapper.Segment> entry : segmentList.entrySet()) {
			segmentMap.put(entry.getKey(), getSegment(entry.getValue()));
		}
		return segmentMap;
	}

	private com.mmt.hotels.clientgateway.response.rooms.Segment getSegment(com.mmt.hotels.model.response.searchwrapper.Segment segment) {
		if (segment == null) {
			return null;
		}
		com.mmt.hotels.clientgateway.response.rooms.Segment segmentCG = new Segment();
		segmentCG.setId(segment.getId());
		segmentCG.setUserSegment(segment.getUserSegment());
		segmentCG.setChannelSegment(segment.getChannelSegment());
		return segmentCG;
	}

	private com.mmt.hotels.clientgateway.response.rooms.WalletSurge getWalletSurge(WalletSurge walletSurge) {
		if (walletSurge == null) {
			return null;
		}
		com.mmt.hotels.clientgateway.response.rooms.WalletSurge walletSurgeCG = new com.mmt.hotels.clientgateway.response.rooms.WalletSurge();
		walletSurgeCG.setSurge(walletSurge.isSurge());
		walletSurgeCG.setEndTime(walletSurge.getEndTime());
		walletSurgeCG.setPersuasionText(walletSurge.getPersuasionText());
		walletSurgeCG.setStartTime(walletSurge.getStartTime());
		return walletSurgeCG;
	}

	private String buildRoomSizeText(RoomInfo roomInfo) {
		String roomSizeText = null;
		if (roomInfo != null && StringUtils.isNotBlank(roomInfo.getRoomSize()) && StringUtils.isNotBlank(roomInfo.getRoomSizeUnit())) {
			roomSizeText = roomInfo.getRoomSize() + SPACE + roomInfo.getRoomSizeUnit();
			if (SQUARE_FEET_V2.equalsIgnoreCase(roomInfo.getRoomSizeUnit())) {
				try {
					double roomSize = Double.parseDouble(roomInfo.getRoomSize());
					roomSize = roomSize * SQUARE_FEET_TO_SQUARE_METER_CONVERSION_FACTOR;
					long roundedRoomSize = Math.round(roomSize); // Round off to the nearest integer
					String roomSizeInMeterSquare = String.valueOf(roundedRoomSize);
					roomSizeText = roomSizeText + SPACE + AMENITIES_OPEN_BRACE + roomSizeInMeterSquare + SPACE + SQUARE_METER + AMENITIES_CLOSING_BRACE;
				} catch (Exception e) {
					LOGGER.error("Error while parsing room size text : {}", e.getMessage());
				}
			}
		}
		return roomSizeText;
	}

	private List<RoomHighlight> getRoomHighlights(RoomInfo roomInfo, ExtraGuestDetail extraGuestDetail, boolean altAccoHotel, boolean isOHSExpEnable, List<FacilityGroup> roomAmenities, String countryCode, boolean amendRoomHighlights, boolean pilgrimageBedInfoEnable) {
		List<RoomHighlight> roomHighlights = new ArrayList<>();
		if(!isOHSExpEnable){
			if(StringUtils.isNotBlank(roomInfo.getRoomSize())) {
				RoomHighlight roomHighlight = new RoomHighlight();
				roomHighlight.setIconUrl(Constants.IMAGE_URL_ROOM_SIZE);
				//Changed 252 sq.ft 252  to  sq.ft (23.4 sq mt)
				roomHighlight.setText(buildRoomSizeText(roomInfo));
				roomHighlight.setDescription(roomHighlight.getText());
				roomHighlights.add(roomHighlight);
			}

			if(StringUtils.isNotBlank(roomInfo.getRoomViewName())) {
				RoomHighlight roomHighlight = new RoomHighlight();
				roomHighlight.setIconUrl(Constants.IMAGE_URL_ROOM_NAME);
				roomHighlight.setText(roomInfo.getRoomViewName());
				roomHighlight.setDescription(roomInfo.getRoomViewName());
				roomHighlights.add(roomHighlight);
			}
		}

		if (!DOM_COUNTRY.equalsIgnoreCase(countryCode) && CollectionUtils.isNotEmpty(roomInfo.getExternalVendorBedRoomInfoList())) {
			roomInfo.getExternalVendorBedRoomInfoList().stream()
					.filter(bedRoomInfo -> StringUtils.isNotBlank(bedRoomInfo.getBedRoomDescription()))
					.forEach(bedRoomInfo -> {
						String bedTypeText = null;
						if (StringUtils.isNotEmpty(bedRoomInfo.getBedRoomName())) {
							bedTypeText = new StringBuilder(bedRoomInfo.getBedRoomName()).append(Constants.SPACE).append(Constants.HYPEN).append(Constants.SPACE).append(bedRoomInfo.getBedRoomDescription()).toString();
						} else {
							bedTypeText = bedRoomInfo.getBedRoomDescription();
						}
						RoomHighlight roomHighlight = new RoomHighlight();
						if (Constants.BATHROOMS.equalsIgnoreCase(bedRoomInfo.getBedRoomName())) {
							roomHighlight.setIconUrl(Constants.IMAGE_URL_BATHROOM_TYPE);
						} else {
							roomHighlight.setIconUrl(Constants.IMAGE_URL_ROOM_TYPE);
						}
						roomHighlight.setText(bedTypeText);
						roomHighlights.add(roomHighlight);
					});
		} else {
			if (CollectionUtils.isNotEmpty(roomInfo.getBathrooms())) {
				RoomHighlight roomHighlight = new RoomHighlight();
				roomHighlight.setIconUrl(Constants.IMAGE_URL_BATHROOM_TYPE);
				BathroomArrangement bathroomArrangement= roomInfo.getBathrooms().get(0);
					String bedTypeText;
					if (bathroomArrangement.getCount() > 1) {
						bedTypeText = bathroomArrangement.getCount() + SPACE + polyglotService.getTranslatedData(ROOM_DETAILS_BATHROOMS_TEXT);
					} else {
						bedTypeText = bathroomArrangement.getCount() + SPACE + polyglotService.getTranslatedData(ROOM_DETAILS_BATHROOM_TEXT);
					}
				roomHighlight.setText(bedTypeText);
				roomHighlight.setDescription(bedTypeText);
				roomHighlights.add(roomHighlight);
			}

			if (pilgrimageBedInfoEnable) {
				if (CollectionUtils.isNotEmpty(roomInfo.getBeds()) || CollectionUtils.isNotEmpty(roomInfo.getAlternateBeds())) {
					RoomHighlight roomHighlight = new RoomHighlight();
					roomHighlight.setIconUrl(Constants.IMAGE_URL_ROOM_TYPE);
					List<String> bedTypeList = new ArrayList<>();
					List<String> alternateBedTypeList = new ArrayList<>();

					if (CollectionUtils.isNotEmpty(roomInfo.getBeds())) {
						roomInfo.getBeds().forEach(bedType -> {
							String bedTypeText = bedType.getCount() + SPACE + bedType.getType();
							bedTypeList.add(bedTypeText);
						});
					}
					if (CollectionUtils.isNotEmpty(roomInfo.getAlternateBeds())) {
						roomInfo.getAlternateBeds().forEach(bedType -> {
							String bedTypeText= bedType.getCount() + SPACE + bedType.getType();
							alternateBedTypeList.add(bedTypeText);
						});
					}
					String bedTypeListString = "";
					if (!bedTypeList.isEmpty()) {
						bedTypeListString = String.join(COMMA_SPACE, bedTypeList);
						if (!alternateBedTypeList.isEmpty()) {
							bedTypeListString = bedTypeListString + SPACE + polyglotService.getTranslatedData(ROOM_DETAILS_ALTERNATE_BED_TYPE_OR_TEXT) + SPACE + String.join(COMMA_SPACE, alternateBedTypeList);
						}
					} else {
						bedTypeListString = String.join(COMMA_SPACE, alternateBedTypeList);
					}

					roomHighlight.setText(bedTypeListString);
					roomHighlight.setDescription(bedTypeListString);
					if (extraGuestDetail != null && StringUtils.isNotEmpty(extraGuestDetail.getRoomSelectionExtraBedText()) && !altAccoHotel) {
						if (altAccoHotel && roomInfo.getBeds().size() == 1) {
							roomHighlight.setSubText(extraGuestDetail.getRoomSelectionExtraBedText());
						} else if (!altAccoHotel) {
							roomHighlight.setSubText(extraGuestDetail.getRoomSelectionExtraBedText());
						}
					}
					roomHighlights.add(roomHighlight);
				}
			}else {
				if (CollectionUtils.isNotEmpty(roomInfo.getBeds())) {
					RoomHighlight roomHighlight = new RoomHighlight();
					roomHighlight.setIconUrl(Constants.IMAGE_URL_ROOM_TYPE);
					List<String> bedTypeList = new ArrayList<>();
					roomInfo.getBeds().forEach(bedType -> {
						String bedTypeText;
						if (bedType.getCount() > 1) {
							bedTypeText = bedType.getCount() + SPACE_X_SPACE + bedType.getType();
						} else {
							bedTypeText = bedType.getType();
						}
						bedTypeList.add(bedTypeText);
					});
					roomHighlight.setText(String.join(COMMA_SPACE, bedTypeList));
					roomHighlight.setDescription(String.join(COMMA_SPACE, bedTypeList));
					if (extraGuestDetail != null && StringUtils.isNotEmpty(extraGuestDetail.getRoomSelectionExtraBedText()) && !altAccoHotel) {
						if (altAccoHotel && roomInfo.getBeds().size() == 1) {
							roomHighlight.setSubText(extraGuestDetail.getRoomSelectionExtraBedText());
						} else if (!altAccoHotel) {
							roomHighlight.setSubText(extraGuestDetail.getRoomSelectionExtraBedText());
						}
					}
					roomHighlights.add(roomHighlight);
				}
			}
		}
		String client = MDC.get(MDCHelper.MDCKeys.CLIENT.getStringValue());
		if(amendRoomHighlights && CollectionUtils.isNotEmpty(roomInfo.getHighlightedFacilities())) {
			com.mmt.model.Facility lowestSequenceFacility = null;
			for(com.mmt.model.FacilityGroup facilityGroup: roomInfo.getHighlightedFacilities()) {
				List<com.mmt.model.Facility> facilities = facilityGroup.getFacilities();
				if (CollectionUtils.isNotEmpty(facilities)) {
					for (com.mmt.model.Facility facility : facilities) {
						if (lowestSequenceFacility == null || facility.getSequence() < lowestSequenceFacility.getSequence()) {
							lowestSequenceFacility = facility;
						}
					}
				}
			}
			if (lowestSequenceFacility != null) {
				RoomHighlight roomHighlight = buildRoomHighlightsBasicOfRoomAmenities(lowestSequenceFacility.getName());
				roomHighlight.setIconUrl("https://promos.makemytrip.com/images/CDN_upload/blacktick_with_circle.png");
				roomHighlights.add(roomHighlight);
			}
		}

		if(isOHSExpEnable && !amendRoomHighlights && !CLIENT_DESKTOP.equalsIgnoreCase(client) && CollectionUtils.isNotEmpty(roomAmenities)){
			int amenitiesToShow = 2;
			for(FacilityGroup facilityGroup: roomAmenities){
				List<Facility>  facilities = facilityGroup.getFacilities();
				if(CollectionUtils.isNotEmpty(facilities)){
					for(Facility facility: facilities){
						if(amenitiesToShow==0){
							break;
						}
						roomHighlights.add(buildRoomHighlightsBasicOfRoomAmenities(facility.getName()));
						amenitiesToShow--;
					}
				}
				if(amenitiesToShow==0){
					break;
				}
			}
		}
		return roomHighlights;
	}

	private RoomHighlight buildRoomHighlightsBasicOfRoomAmenities(String facilityName) {
		RoomHighlight roomHighlight = new RoomHighlight();
		roomHighlight.setText(facilityName);
		roomHighlight.setDescription(facilityName);
		return roomHighlight;
	}

    private ExtraGuestInfo getExtraGuestInfo(ExtraGuestDetail extraGuestDetail, boolean isAltAccoHotel) {

		ExtraGuestInfo extraGuestInfo = null;
		if (!isAltAccoHotel && extraGuestDetail != null && StringUtils.isNotEmpty(extraGuestDetail.getRoomSelectionDetailExtraBedText())
				&& extraGuestDetail.getRoomSelectionDetailExtraBedText().contains(Constants.HASH_SEPARATOR)) {
			extraGuestInfo = new ExtraGuestInfo();
			String[] tokens = extraGuestDetail.getRoomSelectionDetailExtraBedText().split(Constants.HASH_SEPARATOR);
			extraGuestInfo.setExtraGuestInfoHeading(polyglotService.getTranslatedData(ConstantsTranslation.ROOM_DETAILS_EXTRA_GUEST_INFO_HEADING));
			extraGuestInfo.setExtraGuestInfoDscr(tokens[1]);

		}
		return extraGuestInfo;
	}

	private TariffViewType getTariffViewType(RoomDetails roomDetails, Integer starRating, boolean isFirstRoom, String expData) {
		int totalRatePlans = roomDetails.getRatePlans().size();

		TariffViewType clientViewType = new TariffViewType();
		clientViewType.setTotalTariffs(roomDetails.getRatePlans().size());
		clientViewType.setBaseTariffText(polyglotService.getTranslatedData(ConstantsTranslation.STARTING_PRICE_AT));
		if (totalRatePlans == 1) {
			clientViewType.setInitialVisible(1);
			return clientViewType;
		}

		Map<String,String> expDataMap = new HashMap<>();
		if (StringUtils.isNotBlank(expData)) {
			expData = expData.replaceAll("^\"|\"$", "");
			Type type = new TypeToken<Map<String, String>>() {
			}.getType();
			expDataMap = gson.fromJson(expData,type);
		}

		/* Search-Rooms UAT JIRA HTL-29300 */
		if (MapUtils.isNotEmpty(ratePlanDisplayLogic) && MapUtils.isNotEmpty(expDataMap) ) {
			Map<String,Integer> map;
			if (("true").equalsIgnoreCase(expDataMap.get("Premium_SR")) && starRating!=null && starRating >=4) {
				if (("true").equalsIgnoreCase(expDataMap.get("Room_Count_SR"))) {
					/* Premium_SR = true && Room_Count_SR = true */
					map = ratePlanDisplayLogic.get("PREMIUM").get(getKeyAccordingToCount(ratePlanDisplayLogic.get("PREMIUM"),totalRatePlans));
				} else {
					/* Premium_SR = true && Room_Count_SR = false */
					map = ratePlanDisplayLogic.get("PREMIUM").get("DEFAULT");
				}
				if (isFirstRoom) {
					clientViewType.setInitialVisible(map.get("FIRST_ROOM_RPC_COUNT"));
				} else {
					clientViewType.setInitialVisible(map.get("OTHER_ROOM_RPC_COUNT"));
				}
			} else if (("false").equalsIgnoreCase(expDataMap.get("Premium_SR"))
					|| (("true").equalsIgnoreCase(expDataMap.get("Premium_SR")) && starRating!=null && starRating <4 )) {
				if (("true").equalsIgnoreCase(expDataMap.get("Room_Count_SR"))) {
					/* Premium_SR = false && Room_Count_SR = true */
					map = ratePlanDisplayLogic.get("BUDGET").get(getKeyAccordingToCount(ratePlanDisplayLogic.get("BUDGET"),totalRatePlans));
				} else {
					/* Premium_SR = false && Room_Count_SR = false */
					map = ratePlanDisplayLogic.get("BUDGET").get("DEFAULT");
				}
				if (isFirstRoom) {
					clientViewType.setInitialVisible(map.get("FIRST_ROOM_RPC_COUNT"));
				} else {
					clientViewType.setInitialVisible(map.get("OTHER_ROOM_RPC_COUNT"));
				}
			} else {
				clientViewType.setInitialVisible(ratePlanMoreOptionsLimit);
			}
		} else {
			clientViewType.setInitialVisible(ratePlanMoreOptionsLimit);
		}
		/* Search-Rooms UAT JIRA HTL-29300 */

		/* Check and correct initialVisibleCount for case when : (initialVisibleCount > TotalTariffsCount) */
		if (clientViewType.getInitialVisible()>clientViewType.getTotalTariffs())
			clientViewType.setInitialVisible(clientViewType.getTotalTariffs());

		return clientViewType;
	}

	private String getKeyAccordingToCount(Map<String,Map<String,Integer>> map, int roomCount) {
		String key = "DEFAULT";
		if (map.containsKey(String.valueOf(roomCount))) {
			key = String.valueOf(roomCount);
		} else {
			List<String> list = new ArrayList<>(map.keySet());
			List<Integer> arr = list.stream().filter(NumberUtils::isCreatable).map(Integer::valueOf).sorted(Comparator.comparingInt(Integer::intValue)).collect(Collectors.toList());
			Optional<Integer> k = arr.stream().sequential().filter(i->i>roomCount).findFirst();
			if (k.isPresent()) {
				key = String.valueOf(k.get());
			} else if (CollectionUtils.isNotEmpty(arr) && (arr.get(arr.size() - 1) < roomCount)) {
				key = String.valueOf(arr.get(arr.size()-1));
			}
		}
		return key;
	}

	private List<SelectRoomRatePlan> getRatePlans(RoomType roomType, String listingType, String expData, boolean ratePlanGroup, String askedCurrency,
												  String sellableType,String funnelSource, int days,int ap, boolean isBlockPAH,
												  CommonModifierResponse commonModifierResponse, boolean isPackageRoom,
												  Map<String, String> ratePlanCodeAndNameMap, boolean isLuxeHotel,
												  boolean isUserGCCAndMmtExclusive, boolean groupBookingPrice,boolean isAltAccoHotel,
												  InstantFareInfo instantFareInfo, String corpAlias, final MarkUpDetails markUpDetails,
												  Double foodRating, boolean exactCase, String siteDomain, boolean isRecommended,
												  HotelRates hotelRates, boolean isOccassionPackage, boolean isHighSellingAltAcco) {
		if (roomType == null)
			return null;
		List<SelectRoomRatePlan> ratePlans = new ArrayList<>();
		Map<String, List<com.mmt.hotels.model.response.pricing.RatePlan>> rpcRatePlanMap = new LinkedHashMap<>();
		boolean myPartner = Objects.nonNull(commonModifierResponse) && Objects.nonNull(commonModifierResponse.getExtendedUser()) && Utility.isMyPartnerRequest(commonModifierResponse.getExtendedUser().getProfileType(), commonModifierResponse.getExtendedUser().getAffiliateId());
        Comparator<RatePlan> compRatePlanPrice = (h1, h2) -> Double.valueOf(h1.getDisplayFare().getDisplayPriceBreakDown().getDisplayPrice()).compareTo(Double.valueOf(h2.getDisplayFare().getDisplayPriceBreakDown().getDisplayPrice()));
        List<RatePlan> sortedRatePlanList = roomType.getRatePlanList().values().stream().sorted(compRatePlanPrice).collect(Collectors.toList());
		boolean enableThemification = commonModifierResponse != null && utility.isExperimentTrue(commonModifierResponse.getExpDataMap(), THEMIFICATION_ENABLED);
		boolean isNewSelectRoomPage = commonModifierResponse != null && utility.isExperimentTrue(commonModifierResponse.getExpDataMap(), NEW_SELECT_ROOM_PAGE);
		boolean isNewDetailsPageDesktop = commonModifierResponse != null && utility.isExperimentOn(commonModifierResponse.getExpDataMap(), NEW_DETAIL_PAGE_DESKTOP_EXP);
		boolean isMyBizNewDetailsPage = commonModifierResponse != null && utility.isExperimentTrue(commonModifierResponse.getExpDataMap(), Constants.MYB_NEW_DETAILS_EXP_KEY);
		boolean isShowNRLinkedRates = commonModifierResponse != null && utility.isExperimentTrue(commonModifierResponse.getExpDataMap(), LINKED_RATE_EXPERIMENT_NR);
		String client = MDC.get(MDCHelper.MDCKeys.CLIENT.getStringValue());
		for (RatePlan ratePlanHes : sortedRatePlanList) {
			// we will have one boolean that this ratePlan is mealUpgrade rate plan and only it is to be added
			String ratePlanCode = ratePlanHes.getRatePlanCode();
			ratePlanHes.setRatePlanCode(ratePlanCode);
			if (ratePlanGroup) {
				rpcRatePlanMap.computeIfAbsent(ratePlanCode, k -> new ArrayList<>());
				rpcRatePlanMap.get(ratePlanCode).add(ratePlanHes);
			}else {
				String rpccCancelPolicy = "";
				String cancelPolicy = CollectionUtils.isNotEmpty(ratePlanHes.getCancelPenaltyList()) && ratePlanHes.getCancelPenaltyList().get(0) != null
						&& ratePlanHes.getCancelPenaltyList().get(0).getPenaltyDescription() != null?ratePlanHes.getCancelPenaltyList().get(0).getPenaltyDescription().getName():"";
				if(StringUtils.isNotEmpty(cancelPolicy)){
					rpccCancelPolicy = ratePlanHes.getRpcc() + ":" + cancelPolicy;
				}
				else{
					rpccCancelPolicy = ratePlanHes.getRpcc();
				}
				rpcRatePlanMap.computeIfAbsent(rpccCancelPolicy, k -> new ArrayList<>());
				rpcRatePlanMap.get(rpccCancelPolicy).add(ratePlanHes);
			}

			/** HTL-40907: Set lowest instant room rate for which rpBookingModel != RTB_EMAIL. RTB_EMAIL indicates value of negotiated rate plan flag.
			 * Negotiated rate means one-on-one rates that are directly negotiated between the organization and the hotel. This value is obtained from pricer to orchestrator.
			 * Once the user chooses the negotiated rate and makes the payment, a communication will be sent to the hotelier asking to approve/ deny it.
			 * It will be a delayed booking and confirmation will be provided within 4 hrs of the request.
			 */
			if (!RTB_EMAIL.equalsIgnoreCase(ratePlanHes.getRpBookingModel()) && instantFareInfo != null &&
					instantFareInfo.getSubheader() != null && instantFareInfo.getSubheader().contains("{INSTANT_FARE}") && ratePlanHes.getDisplayFare() != null && ratePlanHes.getDisplayFare().getDisplayPriceBreakDown() != null) {
				int amount = (int) ratePlanHes.getDisplayFare().getDisplayPriceBreakDown().getTotalAmount();
				instantFareInfo.setSubheader(StringUtils.replace(instantFareInfo.getSubheader(), "{INSTANT_FARE}", String.valueOf(amount)));
			}
		}

		for (String rpc : rpcRatePlanMap.keySet()) {
			SelectRoomRatePlan ratePlan = new SelectRoomRatePlan();
			AddOnPolicies addOnPolicies = new AddOnPolicies();
			if (isPackageRoom || isRecommended || isOccassionPackage) {
				ratePlan = new PackageSelectRoomRatePlan();
			}
			ratePlan.setRpc(rpc);
			RatePlan ratePlanHes = rpcRatePlanMap.get(rpc).get(0);
			ratePlan.setVendorRatePlanCode(ratePlanHes.getRpcc());
			ratePlan.setLucky(ratePlanHes.isLuckyRateAvailable());
			String roomName = roomType.getRoomTypeName();
			ratePlan.setFilterCode(getFilterCodes(ratePlanHes, isBlockPAH, ap, commonModifierResponse, isLuxeHotel, roomName));
			if(null != ratePlanHes.getPaymentDetails())
				ratePlan.setPayMode(ratePlanHes.getPaymentDetails().getPaymentMode().name());
			//ratePlan.setInclusionsList(getInclusionsList(rpcRatePlanMap.get(rpc).get(0).getInclusions()));
 			ratePlan.setInclusionsList(transformInclusions(
					ratePlanHes, mealPlanMapPolyglot, ap, isBlockPAH, expData,
					foodRating,askedCurrency, true, hotelRates));
			//updateInclusion(ratePlan.getInclusionsList(), rpcRatePlanMap.get(rpc).get(0).getInclusions());
			if(null != ratePlanHes.getSupplierDetails())
				ratePlan.setSupplierCode(ratePlanHes.getSupplierDetails().getSupplierCode());
			ratePlan.setReviewDeeplinkUrl(ratePlanHes.getReviewDeeplinkUrl());
			if(StringUtils.isNotEmpty(ratePlan.getReviewDeeplinkUrl()) && !ratePlan.getReviewDeeplinkUrl().contains("mpn"))
			{
				String reviewDeeplinkUrl = ratePlan.getReviewDeeplinkUrl();
				ratePlan.setReviewDeeplinkUrl(reviewDeeplinkUrl.concat(Constants.AND_SEPARATOR + "mpn" + Constants.PR_SEPARATOR + hotelRates.isMaskedPropertyName()));
			}
			ratePlan.setCorpApprovalInfo(commonResponseTransformer.buildCorpApprovalInfo(ratePlanHes.getDisplayFare().getCorpMetaData(), utility.isTcsV2FlowEnabled(expData)));
			ratePlan.setAddons(commonResponseTransformer.getAddons(ratePlanHes.getAddOns()));
			ratePlan.setStaycationDeal(ratePlanHes.isStaycationDeal());
			ratePlan.setCheckinPolicy(ratePlanHes.getCheckinPolicy());
			ratePlan.setConfirmationPolicy(ratePlanHes.getConfirmationPolicy());
            ratePlan.setInstantConfirmation(ratePlanHes.getInstantConfirmation());
			BNPLVariant bnplVariant = ratePlanHes.getBnplVariant();
            ratePlan.setCancellationTimeline(commonResponseTransformer.buildCancellationTimeline(ratePlanHes.getCancellationTimeline(), bnplVariant));
			ratePlan.setCancellationPolicyTimeline(commonResponseTransformer.buildCancellationPolicyTimeline(ratePlanHes.getCancellationTimeline(), enableThemification, bnplVariant));
            if (ratePlan.getCancellationTimeline() != null)
                ratePlan.getCancellationTimeline().setTitle(polyglotService.getTranslatedData(ConstantsTranslation.RATEPLAN_CANCELLATION_POLICY));
            /*
             * myPartner change log : commonModifierResponse floated down
             * */
            ratePlan.setPersuasions(getRatePlanPersuasion(ratePlan, ratePlanHes, funnelSource, commonModifierResponse, isLuxeHotel, corpAlias));
			boolean newPropertyOfferApplicable = commonModifierResponse!=null && MapUtils.isNotEmpty(commonModifierResponse.getExpDataMap()) ? utility.isExperimentOn(commonModifierResponse.getExpDataMap(), ExperimentKeys.NEW_PROPERTY_OFFER.getKey()) : false;

			ratePlan.setTariffs(getTariffs(rpcRatePlanMap.get(rpc), expData, askedCurrency, sellableType, days, funnelSource, groupBookingPrice, myPartner, isAltAccoHotel, markUpDetails, newPropertyOfferApplicable, isHighSellingAltAcco));
            // HTL-42803:  TO-DO remove boolean isBnplOneVariant node once BNPLVariant Enum changes are completely live.
            boolean isBnplOneVariant = false;
            Map<String, String> experimentDataMap = utility.getExpDataMap(expData);
            if (MapUtils.isNotEmpty(experimentDataMap)) {
                isBnplOneVariant = experimentDataMap.containsKey(EXP_BNPL_NEW_VARIANT) && Boolean.parseBoolean(experimentDataMap.get(EXP_BNPL_NEW_VARIANT));
            }
            ratePlan.setRatePlanPersuasionsMap(buildRatePlanPersuasionsMap(ratePlanHes, commonModifierResponse, hotelRates.getHeroTierDetails()));
			String partialRefundText = utility.buildPartialRefundDateText(ratePlanHes.getCancellationTimeline());
            ratePlan.setCancellationPolicy(utility.transformCancellationPolicy(ratePlanHes.getCancelPenaltyList(), false, isBnplOneVariant, bnplVariant, null, polyglotService.getTranslatedData(ConstantsTranslation.CANCELLATION_POLICY_NR_NON_INSTANT_TEXT), ap, Optional.ofNullable(ratePlanHes.getMpFareHoldStatus()), partialRefundText, false));
			if(MapUtils.isNotEmpty(rpcRatePlanMap) && rpcRatePlanMap.containsKey(rpc) && Objects.nonNull(ratePlanHes)) {
				setRatePlanDetailsCta(ratePlanHes, ratePlan);
			}

			if (utility.isFlexiCancelAddOnAvailable(ratePlanHes)) {
				addOnPolicies.setInclusionsList(transformInclusions(
						ratePlanHes, mealPlanMapPolyglot, ap, isBlockPAH, expData,
						foodRating, askedCurrency,false, hotelRates));
				addOnPolicies.setCancellationTimeline(commonResponseTransformer.buildCancellationTimeline(ratePlanHes.getFlexiCancelAddOn().getAddOnPolicies().get(FLEXI_CANCEL).getCancellationTimeline(), bnplVariant));
				addOnPolicies.setCancellationPolicyTimeline(getCancellationPolicyTimeline(ratePlanHes.getFlexiCancelAddOn().getAddOnPolicies().get(FLEXI_CANCEL).getCancellationTimeline(), enableThemification));
				if (addOnPolicies.getCancellationTimeline() != null)
					addOnPolicies.getCancellationTimeline().setTitle(polyglotService.getTranslatedData(ConstantsTranslation.RATEPLAN_CANCELLATION_POLICY));
				addOnPolicies.setCancellationPolicy(utility.transformCancellationPolicy(ratePlanHes.getFlexiCancelAddOn().getAddOnPolicies().get(FLEXI_CANCEL).getCancelPenaltyList(), false, isBnplOneVariant, bnplVariant, null, polyglotService.getTranslatedData(ConstantsTranslation.CANCELLATION_POLICY_NR_NON_INSTANT_TEXT), ap, Optional.ofNullable(ratePlanHes.getMpFareHoldStatus()), partialRefundText, false));
				if(MapUtils.isNotEmpty(rpcRatePlanMap) && rpcRatePlanMap.containsKey(rpc) && Objects.nonNull(ratePlanHes)) {
					setRatePlanDetailsCta(ratePlanHes, ratePlan);
				}
				addOnPolicies.getCancellationPolicy().setIconUrl(flexiCancelStaticDetail != null ? flexiCancelStaticDetail.getFcInclusionIconUrl() : null);
				if (MapUtils.isEmpty(ratePlan.getAddOnPolicies())) {
					ratePlan.setAddOnPolicies(new HashMap<>());
				}
				ratePlan.getAddOnPolicies().put(FLEXI_CANCEL, addOnPolicies);
			}
            if(isNewSelectRoomPage && ratePlan.getCancellationPolicy()!=null && StringUtils.isNotEmpty(ratePlan.getCancellationPolicy().getText())
					&& (client.equalsIgnoreCase(ANDROID) ||  client.equalsIgnoreCase(DEVICE_IOS))){
				String s = ratePlan.getCancellationPolicy().getText();
				s = s.replace("Free Cancellation", "<font color=\"#007E7D\">Free Cancellation</font>");
				ratePlan.getCancellationPolicy().setText(s);
				if(ratePlan.getCancellationPolicy().getIconType() == IconType.DOUBLETICK){
					ratePlan.getCancellationPolicy().setIconType(IconType.GreenTickIcon);
				}
			}
			if(isNewSelectRoomPage && addOnPolicies.getCancellationPolicy()!=null && StringUtils.isNotEmpty(addOnPolicies.getCancellationPolicy().getText())
					&& (client.equalsIgnoreCase(ANDROID) ||  client.equalsIgnoreCase(DEVICE_IOS))){
				String s = addOnPolicies.getCancellationPolicy().getText();
				s = s.replace("Free Cancellation", "<font color=\"#007E7D\">Free Cancellation</font>");
				addOnPolicies.getCancellationPolicy().setText(s);
				if(addOnPolicies.getCancellationPolicy().getIconType() == IconType.DOUBLETICK){
					addOnPolicies.getCancellationPolicy().setIconType(IconType.GreenTickIcon);
				}
			}
			if (utility.isAddOnDetailsAvailable(ratePlanHes)) {
				Map<String, AddOnDetails> flexiCanAddOnDetailsMap = buildAddOnDetails(ratePlanHes.getFlexiCancelDetails().getAddOnDetails(), expData,
						null != ratePlanHes.getAvailDetails() ? ratePlanHes.getAvailDetails().getOccupancyDetails().getNumOfRooms() : null,
						days, sellableType, utility.isGroupBookingFunnel(funnelSource), isAltAccoHotel, isHighSellingAltAcco);
				if (ratePlan.getAddOnDetails() == null) {
					ratePlan.setAddOnDetails(new HashMap<>());
				}
				ratePlan.getAddOnDetails().putAll(flexiCanAddOnDetailsMap);
			}
			ratePlan.setName(utility.getRatePlanName(ratePlanHes.getMealPlans(), ratePlan.getCancellationPolicy(), sellableType, listingType, expData));
			ratePlanCodeAndNameMap.put(rpc, utility.getRatePlanName(ratePlanHes.getMealPlans(), null, sellableType, listingType, expData));
			if(isShowNRLinkedRates && CollectionUtils.isNotEmpty(ratePlanHes.getLinkedRates())){

				String parent = ratePlanHes.getLinkedRates().get(0).getPricingKey();
				if(CollectionUtils.isNotEmpty(rpcRatePlanMap.get(parent))){
					RatePlan parentRatePlan = rpcRatePlanMap.get(parent).get(0);
					if(Objects.nonNull(parentRatePlan)){
						String parentPartialRefundText = utility.buildPartialRefundDateText(parentRatePlan.getCancellationTimeline());
						BookedCancellationPolicy parentCancellationPolicy = utility.transformCancellationPolicy(parentRatePlan.getCancelPenaltyList(), false, isBnplOneVariant, bnplVariant, null, polyglotService.getTranslatedData(ConstantsTranslation.CANCELLATION_POLICY_NR_NON_INSTANT_TEXT), ap, Optional.ofNullable(parentRatePlan.getMpFareHoldStatus()), parentPartialRefundText, false);
						String linkedRatePlanName = utility.getRatePlanName(parentRatePlan.getMealPlans(), parentCancellationPolicy, sellableType, listingType, expData);
						ratePlan.setLinkedRatePlanName(utility.replaceWithFreeCancellation(linkedRatePlanName));
						buildParentLinkedRates(ratePlan, ratePlanHes.getLinkedRates());
					}
				}
			}

			if(isShowNRLinkedRates && CollectionUtils.isNotEmpty(ratePlanHes.getChildLinkedRates())){
				buildChildLinkedRates(ratePlan, ratePlanHes.getChildLinkedRates());
			}

			ratePlan.setSellableType(StringUtils.isNotBlank(sellableType)?sellableType: "Room");
			if(SELLABLE_ROOM_TYPE.equalsIgnoreCase(ratePlan.getSellableType())){
				ratePlan.setSellableText(polyglotService.getTranslatedData(ROOM_SELLABLE_TYPE));
			} else if(SELLABLE_BED_TYPE.equalsIgnoreCase(ratePlan.getSellableType())){
				ratePlan.setSellableText(polyglotService.getTranslatedData(BED_SELLABLE_TYPE));
			} else{
				ratePlan.setSellableText(sellableType);
			}
			if (isPackageRoom) {
				setPackageRoomSpecificRatePlanInfo((PackageSelectRoomRatePlan) ratePlan,
						(PackageRoomRatePlan) ratePlanHes);
			}
			ratePlan.setAllInclusiveRate(ratePlanHes.isAllInclusiveRate());

			//Creating Node For MmtExclusive detail Page
			if(isUserGCCAndMmtExclusive) {
				Map<String, MmtExclusive> card = new HashMap<>();
				MmtExclusive mmtExclusive = utility.buildMmtExclusiveNode(ratePlanHes.getInclusions(),experimentDataMap);
				if (isNewDetailsPageDesktop) {
					mmtExclusive.setImageUrl(MMT_EXCLUSIVE_IMAGE_URL_NEW_DT_PAGE);
				}
				card.put(Constants.GCC_EXCLUSIVE, mmtExclusive);
				if(MapUtils.isNotEmpty(experimentDataMap) && experimentDataMap.containsKey("gcclpg")
						&& !experimentDataMap.get("gcclpg").equals("2") && !experimentDataMap.get("gcclpg").equals("3")){
					ratePlan.setCards(card);
				}
			}

			if (CollectionUtils.isNotEmpty(rpcRatePlanMap.get(rpc)) && RTB_EMAIL.equalsIgnoreCase(ratePlanHes.getRpBookingModel())) {
				ratePlan.setSpecialFare(true);
			}

			if (CollectionUtils.isNotEmpty(rpcRatePlanMap.get(rpc))) {
				ratePlan.setSegmentId(ratePlanHes.getSegmentId());
			}
			if(CollectionUtils.isNotEmpty(rpcRatePlanMap.get(rpc))) {
				//create persuasion
				if(ratePlanHes != null && Objects.nonNull(ratePlanHes.getOccassionRoomPersuasion()) &&
						StringUtils.isNotEmpty(ratePlanHes.getOccassionRoomPersuasion().getOccassionPersuaionId())) {
					List<PersuasionResponse> persuasionResponses = new ArrayList<>();
					persuasionResponses.add(getOccassionPackagePersuasion(ratePlanHes));
					ratePlan.setPersuasions(persuasionResponses);
				} else if ( ratePlanHes.getPackageRoomRatePlan() && setSuperPackagePersuasion(commonModifierResponse)) {
					if (CollectionUtils.isNotEmpty(ratePlan.getPersuasions())) {
						// add Persuasion here
						ratePlan.getPersuasions().add(getSuperPackagePersuasion());
					} else {
						ratePlan.setPersuasions(new ArrayList<>());
						ratePlan.getPersuasions().add(getSuperPackagePersuasion());
					}
				}
			}

			if(ratePlanHes.getPackageRoomRatePlan()) {
				ratePlan.setPackageRateAvailable(true);
			}


			// If pricemap is available, we will send the rateplan, If not we wont, so that app wont crash.
			if (isPriceMapAvailable(ratePlan))
				ratePlans.add(ratePlan);

			if (ratePlanHes.getDisplayFare() != null && ratePlanHes.getDisplayFare().getDisplayPriceBreakDown() != null
					&& ratePlanHes.getDisplayFare().getDisplayPriceBreakDown().getHotelierCouponDiscount() > 0
					&& StringUtils.isNotEmpty(ratePlanHes.getDisplayFare().getDisplayPriceBreakDown().getHCPEncrypted())) {
				ratePlan.setHCP(ratePlanHes.getDisplayFare().getDisplayPriceBreakDown().getHCPEncrypted());
			}
			setHotelCloudData(ratePlan);
			setHotelCloudPersuasions(ratePlan);
			ratePlan.setAdditionalFees(buildAdditionalFeesForRatePlan(hotelRates, ratePlanHes, commonModifierResponse, experimentDataMap, roomName));
		}

		// Add additional special fare persuasions for the cheapest rate plan if it's negotiated rate plan.
		if (CollectionUtils.isNotEmpty(ratePlans)) {
			SelectRoomRatePlan ratePlan = ratePlans.get(0);
			if (ratePlan.isSpecialFare()) {
				List<PersuasionResponse> persuasions = ratePlan.getPersuasions();
				if (persuasions == null) {
					persuasions = new ArrayList<>();
				}
				PersuasionResponse delayedConfirmationPersuasion = buildDelayedConfirmationPersuasion(corpAlias, isMyBizNewDetailsPage);
				CollectionUtils.addIgnoreNull(persuasions, delayedConfirmationPersuasion);
			}

			if (corpPreferredRateSegmentId != null && corpPreferredRateSegmentId.equals(ratePlan.getSegmentId())) {
				List<PersuasionResponse> persuasions = ratePlan.getPersuasions();
				if (persuasions == null) {
					persuasions = new ArrayList<>();
				}
				PersuasionResponse specialFarePersuasion = buildSpecialFareTagPersuasion(corpAlias);
				CollectionUtils.addIgnoreNull(persuasions, specialFarePersuasion);
			}
		}
		return ratePlans;
	}

	private void buildParentLinkedRates(SelectRoomRatePlan ratePlan, List<com.mmt.hotels.model.response.pricing.LinkedRate> linkedRates) {

		if(CollectionUtils.isNotEmpty(linkedRates)){
			List<LinkedRate> parentLinkedRates = new ArrayList<>();
			for(com.mmt.hotels.model.response.pricing.LinkedRate hesLinkedRate : linkedRates){
				LinkedRate cgLinkedRate = new LinkedRate();
				cgLinkedRate.setType(hesLinkedRate.getType());
				cgLinkedRate.setPricingKey(hesLinkedRate.getPricingKey());
				parentLinkedRates.add(cgLinkedRate);
			}
			ratePlan.setParentLinkedRates(parentLinkedRates);
		}
	}

	private void buildChildLinkedRates(SelectRoomRatePlan ratePlan, List<com.mmt.hotels.model.response.pricing.LinkedRate> linkedRates) {

		if(CollectionUtils.isNotEmpty(linkedRates)){
			List<LinkedRate> childLinkedRates = new ArrayList<>();
			for(com.mmt.hotels.model.response.pricing.LinkedRate hesLinkedRate : linkedRates){
				LinkedRate cgLinkedRate = new LinkedRate();
				cgLinkedRate.setType(hesLinkedRate.getType());
				cgLinkedRate.setPricingKey(hesLinkedRate.getPricingKey());
				childLinkedRates.add(cgLinkedRate);
			}
			ratePlan.setChildLinkedRates(childLinkedRates);
		}
	}

	private CancellationPolicyTimeline getCancellationPolicyTimeline(CancellationTimeline cancellationTimeline, boolean enableThemification) {
		CancellationPolicyTimeline cancellationPolicyTimeline = commonResponseTransformer.buildCancellationPolicyTimeline(cancellationTimeline, enableThemification, null);
		return cancellationPolicyTimeline;
	}

	private boolean setSuperPackagePersuasion(CommonModifierResponse commonModifierResponse) {
		return commonModifierResponse!=null && commonModifierResponse.getExpDataMap()!=null && commonModifierResponse.getExpDataMap().containsKey(EXP_SPKG) && Constants.EXP_TRUE_VALUE.equalsIgnoreCase(commonModifierResponse.getExpDataMap().get(EXP_SPKG));
	}

	private PersuasionResponse getOccassionPackagePersuasion(com.mmt.hotels.model.response.pricing.RatePlan ratePlan) {
		if(ratePlan != null && Objects.nonNull(ratePlan.getOccassionRoomPersuasion()) &&
				StringUtils.isNotEmpty(ratePlan.getOccassionRoomPersuasion().getOccassionPersuaionId())) {
			PersuasionResponse persuasion = new PersuasionResponse();
			persuasion.setPlaceholderId(PRICE_TOP);
			persuasion.setStyle(new Style());
			persuasion.setId(ratePlan.getOccassionRoomPersuasion().getOccassionPersuaionId());
			persuasion.setPersuasionText(ratePlan.getOccassionRoomPersuasion().getOccassionPersuasionTag());
			persuasion.setTemplate(Constants.TEXT_WITH_BG_IMAGE);
			persuasion.getStyle().setTextColor(ratePlan.getOccassionRoomPersuasion().getOccassionPersuasionColour());
			persuasion.getStyle().setBgUrl(ratePlan.getOccassionRoomPersuasion().getHighlightOccassionImageUrl());
			persuasion.getStyle().setFontSize(SMALL);
			return persuasion;
		}
		return null;
	}

	private PersuasionResponse getSuperPackagePersuasion() {
		String id = MMT_SUPER_PACKAGE;
		PersuasionResponse persuasion = new PersuasionResponse();
		persuasion.setPlaceholderId(PRICE_TOP);
		persuasion.setId(id);
		persuasion.setPersuasionText(SUPER_PACKAGE);
		persuasion.setTemplate(Constants.TEXT_WITH_BG_IMAGE);
		persuasion.setStyle(new Style());
		persuasion.getStyle().setTextColor(COLOR_A47313);
		persuasion.getStyle().setBgUrl("https://promos.makemytrip.com/Hotels_product/Details/Packages/gold-cut-border-empty.png");
		persuasion.getStyle().setFontSize(SMALL);
		return persuasion;
	}

	private boolean isPriceMapAvailable(SelectRoomRatePlan ratePlan) {
		if (CollectionUtils.isNotEmpty(ratePlan.getTariffs())) {
			return ratePlan.getTariffs()
					.stream()
					.allMatch(tariff -> tariff.getPriceMap() != null && !tariff.getPriceMap().isEmpty());
		}
		return false;
	}

	private void setPackageRoomSpecificRatePlanInfo(PackageSelectRoomRatePlan ratePlan,
													PackageRoomRatePlan packageRoomRatePlan){
		if (packageRoomRatePlan == null) {
			return;
		}
		ratePlan.setExtendedCheckInDate(packageRoomRatePlan.getExtendedCheckInDate());
		ratePlan.setExtendedCheckOutDate(packageRoomRatePlan.getExtendedCheckOutDate());
		ratePlan.setPackageInclusionDetails(convertPackageInclusionDetails(packageRoomRatePlan.getPackageInclusionDetails()));
	}

	private PackageInclusionDetails convertPackageInclusionDetails(com.mmt.hotels.model.response.pricing.PackageInclusionDetails packageInclusionDetails){
		if(packageInclusionDetails == null){
			return null;
		}
		PackageInclusionDetails packageInclusionDetailsCG = new PackageInclusionDetails();
		BeanUtils.copyProperties(packageInclusionDetails, packageInclusionDetailsCG);
		return packageInclusionDetailsCG;
	}

	private Map<String,PersuasionResponse> buildRatePlanPersuasionsMap(RatePlan ratePlanHes, CommonModifierResponse commonModifierResponse, HeroTierDetails heroTierDetails) {

		Map<String,PersuasionResponse> persuasionMap = new HashMap<>();
		if(ratePlanHes!=null && ratePlanHes.getMpFareHoldStatus()!=null && ratePlanHes.getMpFareHoldStatus().isHoldEligible()) {
			LOGGER.debug("MPFAREHOLD Building details page persuasion for  {}", ratePlanHes.getMpFareHoldStatus());
			int bookingValue = (int)(ratePlanHes.getMpFareHoldStatus().getBookingAmount());

			PersuasionResponse fareHoldPersuasion = new PersuasionResponse();
			fareHoldPersuasion.setTitle(MessageFormat.format(polyglotService.getTranslatedData(ConstantsTranslation.BOOK_NOW_PERSUASION_TITLE),
					String.valueOf(bookingValue)));
			Hover hover = new Hover();
			if(ratePlanHes.getMpFareHoldStatus().getExpiry()!=null) {
				long expiry = ratePlanHes.getMpFareHoldStatus().getExpiry();
				hover.setTitleText(MessageFormat.format(polyglotService.getTranslatedData(ConstantsTranslation.BOOK_NOW_PERSUASION_HOVER_TITLE),
						dateUtil.convertEpochToDateTime(expiry, dateUtil.DD_MMM_hh_mm_a)));
			}
			hover.setSubText(polyglotService.getTranslatedData(ConstantsTranslation.BOOK_NOW_PERSUASION_HOVER_SUB_TITLE));
			fareHoldPersuasion.setHover(hover);
			persuasionMap.put(Constants.BOOK_NOW_PERSUASION_KEY, fareHoldPersuasion);
		}
		boolean isMyPartnerRequest = (commonModifierResponse!=null) && (commonModifierResponse.getExtendedUser()!=null) && Utility.isMyPartnerRequest(commonModifierResponse.getExtendedUser().getProfileType(), commonModifierResponse.getExtendedUser().getAffiliateId());
		if(isMyPartnerRequest)
		{
			if(null!=ratePlanHes.getDisplayFare() && null != ratePlanHes.getDisplayFare().getDisplayPriceBreakDown() && null!= ratePlanHes.getDisplayFare().getDisplayPriceBreakDown().getCouponInfo()) {
				BestCoupon coupon = ratePlanHes.getDisplayFare().getDisplayPriceBreakDown().getCouponInfo();
				//If manthan is sending rewardbonus in mmtVals node, coupon will be set as CTW->Cashback Amount(Cashback offer persuasion condition)
				LOGGER.debug("Manthan HybridDiscounts {}",coupon.getHybridDiscounts());
				boolean isCashbackAmtAvailable=MapUtils.isNotEmpty(coupon.getHybridDiscounts()) && coupon.getHybridDiscounts().containsKey("CTW");
				int myPartnerCashback = ratePlanHes.getDisplayFare().getDisplayPriceBreakDown().getMyPartnerCashback();
				if(StringUtils.isNotBlank(coupon.getLoyaltyOfferMessage()) || isCashbackAmtAvailable || myPartnerCashback > 0) {
					buildLoyaltyCashbackPersuasions(coupon,persuasionMap, myPartnerCashback, heroTierDetails);
				}
			}
		}
		return persuasionMap;
	}
	private List<PersuasionResponse> getRatePlanPersuasion(SelectRoomRatePlan ratePlan,RatePlan ratePlanHES,String funnelSource, CommonModifierResponse commonModifierResponse, boolean isLuxeHotel, String corpAlias) {
		//adding CTRIP noninstant confirmation in rateplan persuasion for now. later to be replaced with  persuasions from PErsuasion engine
		List<PersuasionResponse> persuasions = null;
		boolean isBelowRatePlanAdded = false;
		boolean isNewSelectRoomPage = commonModifierResponse != null && MapUtils.isNotEmpty(commonModifierResponse.getExpDataMap()) ? utility.isExperimentTrue(commonModifierResponse.getExpDataMap(), NEW_SELECT_ROOM_PAGE) : false;
		boolean isMyBizNewDetailsPage = commonModifierResponse != null && MapUtils.isNotEmpty(commonModifierResponse.getExpDataMap()) ? utility.isExperimentTrue(commonModifierResponse.getExpDataMap(), Constants.MYB_NEW_DETAILS_EXP_KEY) : false;


		/*
		 * myPartner change log :
		 * 	Adding myPartner persuasion. It depends on the segmentId of the ratePlan
		 * 	These are added only for CTA profile
		 *
		 * 	The values are added in accordance with the other persuasion object standards. Moving forward, some persuasions
		 *  object will be extracted out to a function since they've been duplicated across conditions
		 *
		 * 	Not null constraints are added to handle test cases
		 * */

		if(Objects.nonNull(commonModifierResponse) && Objects.nonNull(commonModifierResponse.getExtendedUser()) &&  Utility.isMyPartnerRequest(commonModifierResponse.getExtendedUser().getProfileType(), commonModifierResponse.getExtendedUser().getAffiliateId()) &&
				mypatExclusiveRateSegmentIdList.contains(ratePlanHES.getSegmentId())) {
			persuasions = new ArrayList<>();
			PersuasionResponse persuasion = new PersuasionResponse();
			persuasion.setPersuasionText("Partner Exclusive Rate");
			persuasion.setId("MY_PARTNER_SEGMENTS");
			persuasion.setPlaceholderId("PC_RIGHT_3");
			persuasion.setTemplate("PARTNER_PERSUASION");
			persuasion.setStyle(new Style());
			persuasion.getStyle().setTextColor("#fff");
			persuasion.getStyle().setBgGradient(new BGGradient());
			persuasion.getStyle().getBgGradient().setAngle("H");
			persuasion.getStyle().getBgGradient().setColor(Arrays.asList("#f5515f","#9f0469"));
			persuasion.setSubText("Get the best rates possible");
			persuasions.add(persuasion);
		}
		if(ratePlan.getConfirmationPolicy()!= null && StringUtils.isNotBlank(ratePlan.getConfirmationPolicy().getShortDescription())){
			if(persuasions == null)
				persuasions = new ArrayList<>();
			PersuasionResponse persuasion = new PersuasionResponse();
			persuasion.setPersuasionText(ratePlan.getConfirmationPolicy().getShortDescription());
			persuasion.setId("CONFIRMATION_POLICY");
			persuasion.setPlaceholderId("belowRatePlan");
			persuasion.setTemplate("IMAGE_TEXT_H");
			persuasion.setStyle(new Style());
			persuasion.getStyle().setTextColor("#8b572a");
			persuasion.getStyle().setFontSize("SMALL");
			persuasions.add(persuasion);
			isBelowRatePlanAdded  = true;

		}

		if (!isBelowRatePlanAdded && CollectionUtils.isNotEmpty(ratePlan.getInclusionsList()) &&  ratePlan.getInclusionsList().stream().filter(a-> a.isOnOffer()).findFirst().isPresent()){
			BookedInclusion inclusion = ratePlan.getInclusionsList().stream().filter(a-> a.isOnOffer()).findFirst().get();
			if(persuasions == null)
				persuasions = new ArrayList<>();
			PersuasionResponse persuasion = new PersuasionResponse();
			persuasion.setPersuasionText(inclusion.getText());
			persuasion.setId("PACKAGE");
			persuasion.setTemplate("IMAGE_TEXT_H");
			persuasion.setPlaceholderId("belowRatePlan");
			persuasion.setStyle(new Style());
			persuasion.getStyle().setTextColor("#8b572a");
			persuasion.getStyle().setFontSize("SMALL");
			persuasions.add(persuasion);
		}

		if(ratePlanHES!= null && ratePlanHES.getCorpMetaData() != null && ratePlanHES.getCorpMetaData().getValidationPayload()!= null){

			if(!ratePlanHES.getCorpMetaData().getValidationPayload().isWithinPolicy()) {
				if(persuasions == null)
					persuasions = new ArrayList<>();
				PersuasionResponse persuasion = new PersuasionResponse();
				persuasion.setPersuasionText(polyglotService.getTranslatedData(ConstantsTranslation.OUT_OF_POLICY_BOLD));
				persuasion.setPlaceholderId("topRight");
				persuasion.setHtml(true);
				persuasion.setId("OOP");
				persuasion.setTemplate("OVAL");
				persuasion.setStyle(new Style());
				persuasion.getStyle().setTextColor("#ffffff");
				persuasion.getStyle().setFontSize("SMALL");
				persuasion.getStyle().setBgColor("#d0021b");
				persuasions.add(persuasion);
			}
		}


		if(ratePlanHES!= null && ratePlanHES.isStaycationDeal() && "GETAWAY".equalsIgnoreCase(funnelSource) ){
			if(persuasions == null)
				persuasions = new ArrayList<>();
			PersuasionResponse persuasion = new PersuasionResponse();
			persuasion.setPersuasionText(polyglotService.getTranslatedData(ConstantsTranslation.GETAWAY_DEAL_PERSUASION_TEXT));
			persuasion.setPlaceholderId("rightBottom");
			persuasion.setId("STAYCATION");
			persuasion.setTemplate("TEXT_WITH_BG_IMAGE");
			persuasion.setStyle(new Style());
			persuasion.getStyle().setTextColor("#4a4a4a");
			persuasion.getStyle().setFontSize("SMALL");
			persuasion.getStyle().setBgUrl("https://promos.makemytrip.com/Hotels_product/Hotel_SR/iOS/DealBG.png");
			persuasions.add(persuasion);
		}

		if (ratePlanHES != null && ratePlanHES.getPackageRoomRatePlan() && !setSuperPackagePersuasion(commonModifierResponse)) {
			if (persuasions == null) {
				persuasions = new ArrayList<>();
			}
			String id = Constants.MMT_NON_LUXE_PACKAGE;
			String persuasionTextKey = PERSUASION_NON_LUXE_PACKAGE_TEXT;
			if(isLuxeHotel){
				id = Constants.MMT_LUXE_PACKAGE;
				persuasionTextKey = PERSUASION_LUXE_PACKAGE_TEXT;
			}
			PersuasionResponse persuasion = new PersuasionResponse();
			persuasion.setPersuasionText(polyglotService.getTranslatedData(persuasionTextKey));
			persuasion.setPlaceholderId("rightBottom");
			persuasion.setId(id);
			persuasion.setTemplate("TEXT_WITH_BG_IMAGE");
			persuasion.setStyle(new Style());
			if(isNewSelectRoomPage){
				persuasion.getStyle().setTextColor("#CF8100");
				persuasion.getStyle().setBgUrl("https://promos.makemytrip.com/Growth/Images/B2C/luxe_boundary.png");
			}else{
				persuasion.getStyle().setTextColor("#D1851D");
				persuasion.getStyle().setBgUrl("https://promos.makemytrip.com/Hotels_product/package/tag-bkg.png");
			}
			persuasion.getStyle().setFontSize("SMALL");
			persuasions.add(persuasion);
		}

		if (ratePlanHES != null && ratePlanHES.getPreApproved() != null && ratePlanHES.getPreApproved()) {
			if (persuasions == null)
				persuasions = new ArrayList<>();
			PersuasionResponse persuasion = new PersuasionResponse();
			persuasion.setPersuasionText(polyglotService.getTranslatedData(RTB_PRE_APPROVED_TEXT));
			persuasion.setPlaceholderId("belowRatePlan");
			persuasion.setId("PACKAGE");
			persuasion.setTemplate("IMAGE_TEXT_H");
			persuasion.setStyle(new Style());
			persuasion.getStyle().setTextColor("#249995");
			persuasion.getStyle().setFontSize("SMALL");
			persuasions.add(persuasion);
		}

		if(ratePlanHES!=null && ratePlanHES.getSupplierDetails()!=null && supplierToRateSegmentMapping!=null && supplierToRateSegmentMapping.get(Constants.PACKAGE_RATE_KEY)!=null &&
				supplierToRateSegmentMapping.get(Constants.PACKAGE_RATE_KEY).get(ratePlanHES.getSupplierDetails().getSupplierCode())!=null
				&& (supplierToRateSegmentMapping.get(Constants.PACKAGE_RATE_KEY).get(ratePlanHES.getSupplierDetails().getSupplierCode()).isEmpty()
				|| supplierToRateSegmentMapping.get(Constants.PACKAGE_RATE_KEY).get(ratePlanHES.getSupplierDetails().getSupplierCode()).contains(ratePlanHES.getSegmentId()))){

			if (persuasions == null)
				persuasions = new ArrayList<>();
			PersuasionResponse persuasion = new PersuasionResponse();
			persuasion.setId(Constants.MYPARTNER_EXPEDIA_PKG_RATE);
			persuasion.setPersuasionText(polyglotService.getTranslatedData(PACKAGE_RATE_TEXT));
			persuasions.add(persuasion);
		}

		if (ratePlanHES != null && corpPreferredRateSegmentId != null && corpPreferredRateSegmentId.equals(ratePlanHES.getSegmentId())) {
			PersuasionResponse specialFarePersuasion = buildSpecialFareTagWithInfoPersuasion(corpAlias,isNewSelectRoomPage);
			if (!RTB_EMAIL.equalsIgnoreCase(ratePlanHES.getRpBookingModel()) && specialFarePersuasion != null && specialFarePersuasion.getHover() != null) {
				specialFarePersuasion.getHover().setTitleText(polyglotService.getTranslatedData(SPECIAL_FARE_TITLE_TEXT_NOT_RTB));
			}
			if (persuasions == null)
				persuasions = new ArrayList<>();
			CollectionUtils.addIgnoreNull(persuasions, specialFarePersuasion);
		}

		if (commonModifierResponse != null && MapUtils.isNotEmpty(commonModifierResponse.getExpDataMap())
				&& utility.showBookAtZeroPersuasion(commonModifierResponse.getExpDataMap())
				&& ratePlanHES != null && !StringUtils.isEmpty(ratePlanHES.getBnplPersuasionMsg())) {
			if (persuasions == null)
				persuasions = new ArrayList<>();
			PersuasionResponse bnplPersuasion = buildBnplPersuasion();
			CollectionUtils.addIgnoreNull(persuasions, bnplPersuasion);
		}

		if (ratePlanHES != null && RTB_EMAIL.equalsIgnoreCase(ratePlanHES.getRpBookingModel())) {
			if(persuasions == null)
				persuasions = new ArrayList<>();
			PersuasionResponse confirmationTextPersuasion = buildConfirmationTextPersuasion(corpAlias,isNewSelectRoomPage, isMyBizNewDetailsPage);
			CollectionUtils.addIgnoreNull(persuasions, confirmationTextPersuasion);
		}

		return persuasions;

	}

	public List<BookedInclusion> transformInclusions(RatePlan ratePlanHES , Map<String, String> mealPlanMap,
													 int ap, boolean isBlockPah, String expData, Double foodRating,
													 String askedCurrency, boolean cfarBnplFlag, HotelRates hotelRates) {
		List<BookedInclusion> inclusions = new ArrayList<>();
		List<MealPlan> mealPlan = ratePlanHES.getMealPlans();
		Inclusion losInclusion = buildLosInclusion(ratePlanHES);
		List<Inclusion> inclusionList = ratePlanHES.getInclusions();
		String supplierCode = ratePlanHES.getSupplierDetails()!= null ? ratePlanHES.getSupplierDetails().getSupplierCode() : "";
		Map<String,String> experimentDataMap = utility.getExpDataMap(expData);
		List<Inclusion> inclusionListCopy = new ArrayList<>();
		if(CollectionUtils.isNotEmpty(inclusionList)) {
			for(Inclusion inc:inclusionList) {
				inclusionListCopy.add(inc);
			}
		}

		boolean isMealPlanPresent = false;
		int count = 0;
		if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(inclusionList)) {
			inclusionList.sort(Comparator.comparing(Inclusion::getSegmentIdentifier,Comparator.nullsLast(Comparator.naturalOrder())));
			for (Inclusion inclusion : inclusionList) {
				if (StringUtils.isEmpty(inclusion.getValue()))
					continue;
				count++;
				BookedInclusion bookedInclusion = new BookedInclusion();
				bookedInclusion.setText(inclusion.getCode());
				bookedInclusion.setSubText(inclusion.getValue());
				bookedInclusion.setCode(inclusion.getCode());
				bookedInclusion.setAmount(inclusion.getAmount());
				bookedInclusion.setCategory(inclusion.getCategory());
				if (StringUtils.isNotEmpty(inclusion.getCategory()) && (BREAKFAST.equalsIgnoreCase(inclusion.getCategory()) || LUNCH.equalsIgnoreCase(inclusion.getCategory()) || DINNER.equalsIgnoreCase(inclusion.getCategory()))) {
					bookedInclusion.setCategory(MEAL);
				}
				if(StringUtils.isNotBlank(bookedInclusion.getText()) && bookedInclusion.getText().length() < 10 && !bookedInclusion.getText().equalsIgnoreCase(bookedInclusion.getSubText())){
					bookedInclusion.setText(bookedInclusion.getText() + " - " + bookedInclusion.getSubText());
				}
				bookedInclusion.setIconType(IconType.DEFAULT);
				bookedInclusion.setIconUrl("https://promos.makemytrip.com/Hotels_product/Inclusions/Icons/Default_DefaultDot.png");
				bookedInclusion.setSegmentIdentifier(inclusion.getSegmentIdentifier());
				if(count < 2 && !isMealPlanPresent  && CollectionUtils.isNotEmpty(mealPlan)) {
					if( Constants.MEAL_PLAN_CODE_BREAKFAST.equalsIgnoreCase(mealPlan.get(0).getCode()))
						isMealPlanPresent = true;
					else if (!Constants.MEAL_PLAN_CODE_ROOM_ONLY.equalsIgnoreCase(mealPlan.get(0).getCode()) &&
							!Constants.MEAL_PLAN_CODE_BED_ONLY.equalsIgnoreCase(mealPlan.get(0).getCode()) &&
							!Constants.MEAL_PLAN_CODE_ACC_ONLY.equalsIgnoreCase(mealPlan.get(0).getCode()))
						isMealPlanPresent = true;
					if (isMealPlanPresent) {
						bookedInclusion.setInclusionCode(mealPlan.get(0).getCode());
						bookedInclusion.setType("MEAL_PLAN");
						if (StringUtils.isNotEmpty(bookedInclusion.getCategory()) && !bookedInclusion.getCategory().equalsIgnoreCase(MEAL_UPSELL_CATEGORY)) {
							bookedInclusion.setCategory(MEAL);
						}
						bookedInclusion.setIconUrl("https://promos.makemytrip.com/Hotels_product/Hotel_SR/Android/drawable-hdpi/meal.png");
						if(ratePlanHES.getExtraGuestDetail()!=null && StringUtils.isNotEmpty(ratePlanHES.getExtraGuestDetail().getRatePlanExtraBedText())){
							bookedInclusion.setText(ratePlanHES.getExtraGuestDetail().getRatePlanExtraBedText());
							bookedInclusion.setCode(ratePlanHES.getExtraGuestDetail().getRatePlanExtraBedText());
						}
					}
				}
				if ("Packages".equalsIgnoreCase(inclusion.getCategory()) || "Packages1".equalsIgnoreCase(inclusion.getCategory())
						|| "Packages2".equalsIgnoreCase(inclusion.getCategory()) || "Packages3".equalsIgnoreCase(inclusion.getCategory())
						|| "MMTBLACK".equalsIgnoreCase(inclusion.getCategory())) {
					bookedInclusion.setOnOffer(true);
					bookedInclusion.setIconUrl(inclusion.getImageURL());
					bookedInclusion.setCategory(USP);
				}
				if (BLACK_SEGMENT_IDENTIFIER.equalsIgnoreCase(inclusion.getSegmentIdentifier()) && TRUE.equalsIgnoreCase(experimentDataMap.get(ExperimentKeys.BLACK_REVAMP.getKey()))) {
					bookedInclusion.setIconUrl(inclusion.getImageURL());
					bookedInclusion.setCategory(inclusion.getCategory());
				}

				if (!"Packages3".equalsIgnoreCase(inclusion.getCategory()) && StringUtils.isNotEmpty(inclusion.getCategory()))
					bookedInclusion.setBookable(true);
				if(bookedInclusion.getType()=="MEAL_PLAN")
				{
					inclusions.add(bookedInclusion);
				}
				else {
					inclusions.add(0,bookedInclusion);
				}
			}
		}

		if(!utility.isExperimentValid(experimentDataMap, Constants.FOOD_DINING_REVAMP, 0) && ratePlanHES.isMealAvailableAtProperty()) {
			utility.buildMealsATProperty(mealPlan, inclusions, false, IconType.INFO, true);
		} else {
			buildNoMealInclusions(experimentDataMap, mealPlan, mealPlanMap, ratePlanHES, inclusions, isMealPlanPresent, supplierCode, ap, false);
		}
		String region = MDC.get(MDCHelper.MDCKeys.REGION.getStringValue());
		if (ratePlanHES.getPaymentDetails() != null && ratePlanHES.getPaymentDetails().getPaymentMode() != null &&
				!Constants.PAS.equalsIgnoreCase(ratePlanHES.getPaymentDetails().getPaymentMode().getMappedPayMode())) {

			BookedInclusion pahInclusion = new BookedInclusion();
			if (Utility.isRegionGccOrKsa(region)) {
				pahInclusion.setCode(pahGccText);
				pahInclusion.setText(polyglotService.getTranslatedData(ConstantsTranslation.PAH_GCC_TEXT));
			} else if (Constants.PAH_WITHOUT_CC.equalsIgnoreCase(ratePlanHES.getPaymentDetails().getPaymentMode().name())) {
				pahInclusion.setCode(pahWithoutCCText);
				pahInclusion.setText(polyglotService.getTranslatedData(ConstantsTranslation.PAH_WITHOUT_CC_TEXT));
			} else {
				pahInclusion.setCode(pahWithCCText);
				pahInclusion.setText(polyglotService.getTranslatedData(ConstantsTranslation.PAH_WITH_CC_TEXT));
			}

			pahInclusion.setIconType(IconType.DEFAULT);
			pahInclusion.setIconUrl("https://promos.makemytrip.com/Hotels_product/Inclusions/Icons/Default_DefaultDot.png");
			pahInclusion.setInclusionCode(ratePlanHES.getPaymentDetails().getPaymentMode().getMappedPayMode());
			pahInclusion.setType("PAY_MODE");
			pahInclusion.setCategory(ZPN);
			inclusions.add(0, pahInclusion);
		}

		if (!utility.showBookAtZeroPersuasion(experimentDataMap) && CollectionUtils.isNotEmpty(ratePlanHES.getCancelPenaltyList()) && ratePlanHES.getCancelPenaltyList().get(0).getCancellationType()!=null
				&& ratePlanHES.getCancelPenaltyList().get(0).getCancellationType() ==  CancellationType.FREE_CANCELLATON
				&& ratePlanHES.isBnplApplicable() && !isBlockPah && cfarBnplFlag) {


			/* For FCZPN add an inclusion at start */
			BookedInclusion fczpnInlclusions = new BookedInclusion();
			setInclusionCodeAndText(fczpnInlclusions, ratePlanHES.getBnplVariant(), region, askedCurrency);
			fczpnInlclusions.setType(Constants.CANCELLATION_TYPE_FCZPN);
			fczpnInlclusions.setIconType(IconType.DEFAULT);
			fczpnInlclusions.setIconUrl("https://promos.makemytrip.com/Hotels_product/Inclusions/Icons/Default_DefaultDot.png");
			fczpnInlclusions.setInclusionCode(Constants.CANCELLATION_TYPE_FCZPN);
			fczpnInlclusions.setCategory(ZPN);
			inclusions.add(0,fczpnInlclusions);
		}

		// Free stay for X children inclusion added on top of the list
		if (ratePlanHES != null && ratePlanHES.getFreeChildCount() > 0 && StringUtils.isNotEmpty(ratePlanHES.getFreeChildText())) {
			BookedInclusion freeChildInclusion = utility.getFreeChildInclusion(ratePlanHES.getFreeChildText(), freeChildInclusionIcon);
			inclusions.add(0, freeChildInclusion);
		}
		//		We have added category for inclusions that are made so that we can order them in a specific order based on it
		if(utility.isRatePlanRedesign(experimentDataMap) || experimentDataMap.containsKey(ExperimentKeys.BLACK_REVAMP.getKey())){
			inclusions = reorderInclusions(inclusions);
		}
		if(losInclusion != null){
			createLosInclusion(losInclusion,inclusions);
		}

// 		======================= new logic for Inclusions =========================

		if(utility.isReorderInclusions(experimentDataMap) && DOM_COUNTRY.equalsIgnoreCase(MDC.get(MDCHelper.MDCKeys.REGION.getStringValue())) && org.apache.commons.collections4.CollectionUtils.isNotEmpty(inclusions)) {
			inclusions.clear();
			if (ratePlanHES.getPaymentDetails() != null && ratePlanHES.getPaymentDetails().getPaymentMode() != null &&
					!Constants.PAS.equalsIgnoreCase(ratePlanHES.getPaymentDetails().getPaymentMode().getMappedPayMode())) {

				BookedInclusion pahInclusion = new BookedInclusion();
				if (Utility.isRegionGccOrKsa(region)) {
					pahInclusion.setCode(pahGccText);
					pahInclusion.setText(polyglotService.getTranslatedData(ConstantsTranslation.PAH_GCC_TEXT));
				} else if (Constants.PAH_WITHOUT_CC.equalsIgnoreCase(ratePlanHES.getPaymentDetails().getPaymentMode().name())) {
					pahInclusion.setCode(pahWithoutCCText);
					pahInclusion.setText(polyglotService.getTranslatedData(ConstantsTranslation.PAH_WITHOUT_CC_TEXT));
				} else {
					pahInclusion.setCode(pahWithCCText);
					pahInclusion.setText(polyglotService.getTranslatedData(ConstantsTranslation.PAH_WITH_CC_TEXT));
				}

				pahInclusion.setIconType(IconType.DEFAULT);
				pahInclusion.setIconUrl(dotIconUrl);
				pahInclusion.setInclusionCode(ratePlanHES.getPaymentDetails().getPaymentMode().getMappedPayMode());
				pahInclusion.setType("PAY_MODE");
				pahInclusion.setCategory(ZPN);
				inclusions.add(0, pahInclusion);
			}

			if(!utility.isExperimentValid(experimentDataMap, Constants.FOOD_DINING_REVAMP, 0)
					&& ratePlanHES.isMealAvailableAtProperty()) {
				utility.buildMealsATProperty(mealPlan, inclusions, true, IconType.INFO, true);
			} else {
				buildNoMealInclusions(experimentDataMap, mealPlan, mealPlanMap, ratePlanHES, inclusions, isMealPlanPresent, supplierCode, ap, true);
			}

			if (!utility.showBookAtZeroPersuasion(experimentDataMap) && CollectionUtils.isNotEmpty(ratePlanHES.getCancelPenaltyList()) && ratePlanHES.getCancelPenaltyList().get(0).getCancellationType()!=null
					&& ratePlanHES.getCancelPenaltyList().get(0).getCancellationType() ==  CancellationType.FREE_CANCELLATON
					&& ratePlanHES.isBnplApplicable() && !isBlockPah && cfarBnplFlag) {

				/* For FCZPN add an inclusion at start */
				BookedInclusion fczpnInlclusions = new BookedInclusion();
				setInclusionCodeAndText(fczpnInlclusions, ratePlanHES.getBnplVariant(), region,askedCurrency);
				fczpnInlclusions.setType(Constants.CANCELLATION_TYPE_FCZPN);
				fczpnInlclusions.setIconType(IconType.DEFAULT);
				fczpnInlclusions.setIconUrl("https://promos.makemytrip.com/Hotels_product/Persuasion_Icons/Dot.png");
				fczpnInlclusions.setInclusionCode(Constants.CANCELLATION_TYPE_FCZPN);
				fczpnInlclusions.setCategory(ZPN);
				inclusions.add(0,fczpnInlclusions);
			}

			for(Inclusion inclusion : inclusionListCopy) {
				BookedInclusion bookedInclusion = new BookedInclusion();
				bookedInclusion.setCode(inclusion.getCode());
				bookedInclusion.setAmount(inclusion.getAmount());
				bookedInclusion.setSubText(inclusion.getValue());
				bookedInclusion.setText(inclusion.getCode());
				bookedInclusion.setIconUrl(inclusion.getIconUrl());
				bookedInclusion.setCategory(inclusion.getCategory());
				inclusions.add(bookedInclusion);
			}

			if (ratePlanHES != null && ratePlanHES.getFreeChildCount() > 0 && StringUtils.isNotEmpty(ratePlanHES.getFreeChildText())) {
				BookedInclusion freeChildInclusion = utility.getFreeChildInclusion(ratePlanHES.getFreeChildText(), dotIconUrl);
				inclusions.add(0, freeChildInclusion);
			}
			if(losInclusion != null){
				createLosInclusion(losInclusion,inclusions);
			}
		}
		if (utility.isMealRackRate(experimentDataMap)) {
			inclusions = transformMealInclusions(inclusions, mealPlan, foodRating,askedCurrency);
		}
		// Build IHG Paid child inclusion for IHG hotels
		BookedInclusion ihgPaidChildInclusion = utility.getIHGPaidChildInclusionIfApplicable(ratePlanHES, hotelRates);
		if (null != ihgPaidChildInclusion)
			inclusions.add(0, ihgPaidChildInclusion);

		boolean isExtraAdultChildInclusionEnabled = utility.isExtraAdultChildInclusionExperimentEnabled(experimentDataMap);
		List<String> mealPlanCodesList = new ArrayList<>();
		if (CollectionUtils.isNotEmpty(mealPlan)) {
			mealPlanCodesList = mealPlan.stream().map(MealPlan::getCode).collect(Collectors.toList());
		}
		boolean isDomesticHotel = null != hotelRates && Constants.DH_COUNTRY_CODE.equalsIgnoreCase(hotelRates.getCountryCode());
		boolean extraAdultChildFlagFromHes = null != hotelRates && Boolean.TRUE.equals(hotelRates.getIsExtraAdultChild());
		if (isExtraAdultChildInclusionEnabled && extraAdultChildFlagFromHes && utility.showExtraAdultChildInclusion(mealPlanCodesList, supplierCode, extraAdultChildInclusionConfig, isDomesticHotel)) {
			BookedInclusion extraAdultChildInclusion = utility.buildExtraAdultChildInclusion(extraAdultChildInclusionConfig);
			if (null != extraAdultChildInclusion) {
				inclusions.add(0, extraAdultChildInclusion);
			}
		}
		return inclusions;
	}

	private void createLosInclusion(Inclusion losInclusion, List<BookedInclusion> inclusions) {
		BookedInclusion bookedInclusion = new BookedInclusion();
		bookedInclusion.setInclusionCode(LONGSTAY);
		bookedInclusion.setIconType(IconType.DEFAULT);
		bookedInclusion.setIconUrl(losIconUrl);
		bookedInclusion.setText(losInclusion.getCode());
		bookedInclusion.setSubText(losInclusion.getValue());
		bookedInclusion.setCode(losInclusion.getCode());
		bookedInclusion.setCategory(losInclusion.getCategory());
		bookedInclusion.setSegmentIdentifier(LOS);
		if (inclusions.size() > losPositionSelectRoom)
			inclusions.add(losPositionSelectRoom, bookedInclusion);
		else
			inclusions.add(bookedInclusion);
	}

	private Inclusion buildLosInclusion(RatePlan ratePlan){
		Inclusion losInclusion = null;
		List<String> losNames = new ArrayList<>();
		if(ratePlan != null){
			List<Inclusion> inclusions = new ArrayList<>();
			//Discount Inclusion
			if(ratePlan.getLosDiscountInclusion() != null){
				losInclusion = ratePlan.getLosDiscountInclusion();
				losInclusion.setCategory(LONGSTAY);
				losInclusion.setIconUrl(losIconUrl);
				losInclusion.setValue(losInclusion.getCode());
				losNames.add(losInclusion.getCode());
			}
			//Benefits Inclusions
			if(CollectionUtils.isNotEmpty(ratePlan.getInclusions())){
				for(Inclusion inclusion : ratePlan.getInclusions()){
					if(LOS.equalsIgnoreCase(inclusion.getInclusionType())){
						if(losInclusion == null){
							losInclusion = inclusion;
							losInclusion.setCategory(LONGSTAY);
							losInclusion.setIconUrl(losIconUrl);
							losInclusion.setValue(inclusion.getCode());
						}else{
							losInclusion.setValue(StringUtils.isNotEmpty(losInclusion.getValue()) ?  losInclusion.getValue() + COMMA_SPACE + inclusion.getCode() : inclusion.getCode());
						}
						losNames.add(inclusion.getCode());
						continue;
					}
					inclusions.add(inclusion);
				}
				ratePlan.setInclusions(inclusions);
				if(losInclusion!= null && losNames.size() > 0){
					losInclusion.setCode(polyglotService.getTranslatedData(ConstantsTranslation.LONG_STAY_BENEFITS_HEADING));
					losInclusion.setCode(StringUtils.isNotEmpty(losInclusion.getCode()) ? losInclusion.getCode() + SPACE + losNames.get(0) : losNames.get(0));
					if(losNames.size() > 1) losInclusion.setCode(losInclusion.getCode() + COMMA_SPACE + losNames.get(1));
					if(losNames.size() > 2){
						String plusMore = polyglotService.getTranslatedData(ConstantsTranslation.PLUS_MORE);
						losInclusion.setCode(StringUtils.isNotEmpty(plusMore)
								? losInclusion.getCode() + SPACE + plusMore : losInclusion.getCode());
					}
				}
			}
		}
		return losInclusion;
	}


	public List<BookedInclusion> transformMealInclusions(List<BookedInclusion> inclusions, List<MealPlan> mealPlan, Double foodRating, String askedCurrency) {
		for (BookedInclusion inclusion: inclusions) {
			if (StringUtils.isNotEmpty(inclusion.getCode()) && StringUtils.isNotEmpty(inclusion.getCategory()) && inclusion.getCategory().equalsIgnoreCase(MEAL_UPSELL_CATEGORY)) {
				String amountValue = inclusion.getAmount();
				inclusion.setText(inclusion.getSubText());
				inclusion.setCode(inclusion.getSubText());
				if (foodRating != null) {
					int percent = (int) (foodRating * 20);
					if (percent >= foodRatingThresold) {
						String text = polyglotService.getTranslatedData(ConstantsTranslation.MEAL_UPSELL_DESC_TEXT);
						if (StringUtils.isNotEmpty(text)) {
							inclusion.setDescriptionText(text.replace("{percent}", percent + "%"));
						}
					}
				}
				inclusion.setIconUrl("https://promos.makemytrip.com/Hotels_product/Hotel_SR/Android/drawable-hdpi/meal.png");
				inclusion.setSubText(getMealUpsellSubtext(amountValue, mealPlan, askedCurrency));
			}
		}
		return inclusions;
	}

	public String getMealUpsellSubtext(String value, List<MealPlan> mealPlan, String askedCurrency) {
		String mealUpsellSubtext = polyglotService.getTranslatedData(ConstantsTranslation.MEAL_UPSELL_SUBTEXT);
		String currencySymbol = Currency.getCurrencyEnum(StringUtils.isNotBlank(askedCurrency) ? askedCurrency : DEFAULT_CUR_INR).getCurrencySymbol();
		mealUpsellSubtext = mealUpsellSubtext.replace("{cur}", currencySymbol);
		if (CollectionUtils.isNotEmpty(mealPlan) && mealPlan.get(0) != null && !mealPlan.get(0).getCode().equalsIgnoreCase(EP_MEAL_PLAN)) {
			return String.format(mealUpsellSubtext, value);
		}
		return null;
	}

	private void buildNoMealInclusions(Map<String, String> experimentDataMap, List<MealPlan> mealPlan, Map<String, String> mealPlanMap, RatePlan ratePlanHES, List<BookedInclusion> inclusions, boolean isMealPlanPresent, String supplierCode, int ap, boolean appendAtLast) {
		// this piece of code was to be reused in for reordering of inclusion so created the function that can be called from anywhere it is needed
		if (!isMealPlanPresent && org.apache.commons.collections4.CollectionUtils.isNotEmpty(mealPlan) && MapUtils.isNotEmpty(mealPlanMap) && !(MapUtils.isNotEmpty(experimentDataMap)
				&& Constants.TRUE.equalsIgnoreCase(experimentDataMap.get(Constants.NO_MEAL_INCLUSION_REMOVE)))) {
			if (Constants.MEAL_PLAN_CODE_ROOM_ONLY.equalsIgnoreCase(mealPlan.get(0).getCode()) || Constants.MEAL_PLAN_CODE_BED_ONLY.equalsIgnoreCase(mealPlan.get(0).getCode()) || Constants.MEAL_PLAN_CODE_ACC_ONLY.equalsIgnoreCase(mealPlan.get(0).getCode())) {
				BookedInclusion noMeanInclusion = new BookedInclusion();
				noMeanInclusion.setText(polyglotService.getTranslatedData(mealPlanMap.get(mealPlan.get(0).getCode())));
				noMeanInclusion.setCode(noMeanInclusion.getText());
				if (ap < apLimitForInclusionIcons) {
					noMeanInclusion.setIconType(IconType.DEFAULT);
					noMeanInclusion.setIconUrl(dotIconUrl);
				} else {
					noMeanInclusion.setIconType(IconType.CROSS);
					noMeanInclusion.setIconUrl(redCrossIcon);
				}
				noMeanInclusion.setInclusionCode(mealPlan.get(0).getCode());
				noMeanInclusion.setCategory(MEAL);
				inclusions.add(0, noMeanInclusion);
			} else if (StringUtils.isNotBlank(supplierCode) && !Constants.SUPPLIER_INGO.equalsIgnoreCase(supplierCode)) {
				BookedInclusion noMeanInclusion = new BookedInclusion();

				if (ratePlanHES.getExtraGuestDetail() != null && StringUtils.isNotEmpty(ratePlanHES.getExtraGuestDetail().getRatePlanExtraBedText())) {
					noMeanInclusion.setText(ratePlanHES.getExtraGuestDetail().getRatePlanExtraBedText());
					noMeanInclusion.setCode(ratePlanHES.getExtraGuestDetail().getRatePlanExtraBedText());
				} else {
					noMeanInclusion.setText(mealPlanMap.containsKey(mealPlan.get(0).getCode()) ? polyglotService.getTranslatedData(mealPlanMap.get(mealPlan.get(0).getCode())) : mealPlan.get(0).getValue());
					noMeanInclusion.setCode(noMeanInclusion.getText());

				}

				noMeanInclusion.setInclusionCode(mealPlan.get(0).getCode());
				noMeanInclusion.setType("MEAL_PLAN");
				noMeanInclusion.setCategory(MEAL);
				noMeanInclusion.setIconUrl("https://promos.makemytrip.com/Hotels_product/Hotel_SR/Android/drawable-hdpi/meal.png");
				if (appendAtLast) {
					inclusions.add(noMeanInclusion);
				} else {
					inclusions.add(0, noMeanInclusion);
				}
			}
		}
	}

	private List<BookedInclusion> reorderInclusions(List<BookedInclusion> inclusions){
//		this function orders the inculsion based on ratePlanCategoryList {kids -> zpn -> meal -> others, usp}
		List<BookedInclusion> newInclusions = new ArrayList<>();
		Map<String, List<BookedInclusion>> mp = new HashMap<>();
		for (String s: inclusionOrderList){
			mp.put(s, new ArrayList<>());
		}
		inclusions.forEach(e -> {
			if(StringUtils.isNotEmpty(e.getCategory()) && mp.containsKey(e.getCategory())){
				mp.get(e.getCategory()).add(e);
			} else {
				mp.get(OTHERS).add(e);
			}
		});
		for (String s : inclusionOrderList) {
			newInclusions.addAll(mp.get(s));
		}
		return newInclusions;
	}

	private void setInclusionCodeAndText(BookedInclusion fczpnInlclusions, BNPLVariant bnplVariant, String region, String askedCurrency) {
		String currencySymbol = Currency.getCurrencyEnum(StringUtils.isNotBlank(askedCurrency) ? askedCurrency : DEFAULT_CUR_INR).getCurrencySymbol();
		if (Utility.isRegionGccOrKsa(region)) {
			String bnpText = polyglotService.getTranslatedData(BNPL_GCC_TEXT);
			bnpText = bnpText.replace("{cur}", currencySymbol);
			fczpnInlclusions.setCode(bnpText);
			fczpnInlclusions.setText(bnpText);
		} else if (BNPLVariant.BNPL_AT_1.equals(bnplVariant)) {
			String bnplNewVariantText = polyglotService.getTranslatedData(BNPL_NEW_VARIANT_TEXT);
			bnplNewVariantText = bnplNewVariantText.replace("{cur}", currencySymbol);
			if (StringUtils.isNotBlank(bnplNewVariantText)) {
				fczpnInlclusions.setCode(bnplNewVariantText);
				fczpnInlclusions.setText(bnplNewVariantText);
			}
		} else if (BNPLVariant.BNPL_AT_0.equals(bnplVariant)) {
			String bnplZeroVariantText = polyglotService.getTranslatedData(BNPL_ZERO_VARIANT_TEXT);
			bnplZeroVariantText = bnplZeroVariantText.replace("{cur}", currencySymbol);
			if (StringUtils.isNotBlank(bnplZeroVariantText)) {
				fczpnInlclusions.setCode(bnplZeroVariantText);
				fczpnInlclusions.setText(bnplZeroVariantText);
			}
		} else {
			fczpnInlclusions.setCode(polyglotService.getTranslatedData(ConstantsTranslation.ZERO_PAYMENT_NOW_WITH_CC));
			fczpnInlclusions.setText(polyglotService.getTranslatedData(ConstantsTranslation.ZERO_PAYMENT_NOW_WITH_CC));
		}
	}

	public List<String> getFilterCodes(com.mmt.hotels.model.response.pricing.RatePlan ratePlan, boolean isBlockPAH, int ap,
										CommonModifierResponse commonModifierResponse, boolean isLuxeHotel, String roomName) {
		List<String> filterCodes = new ArrayList<>();
		if (CollectionUtils.isNotEmpty(ratePlan.getMealPlans())
				&& !(Constants.MEAL_PLAN_CODE_ROOM_ONLY.equalsIgnoreCase(ratePlan.getMealPlans().get(0).getCode())
				|| Constants.MEAL_PLAN_CODE_BED_ONLY.equalsIgnoreCase(ratePlan.getMealPlans().get(0).getCode())
				|| Constants.MEAL_PLAN_CODE_ACC_ONLY.equalsIgnoreCase(ratePlan.getMealPlans().get(0).getCode()))) {
			filterCodes.add(Constants.FREE_BREAKFAST);
		}
		if (mealplanFilterEnable &&  CollectionUtils.isNotEmpty(ratePlan.getMealPlans())
				&&  (Constants.MEAL_PLAN_CODE_BREAKFAST_LUNCH_OR_DINNER.equalsIgnoreCase(ratePlan.getMealPlans().get(0).getCode())
				|| Constants.MEAL_PLAN_CODE_BREAKFAST_DINNER.equalsIgnoreCase(ratePlan.getMealPlans().get(0).getCode())
				|| Constants.MEAL_PLAN_CODE_BREAKFAST_LUNCH.equalsIgnoreCase(ratePlan.getMealPlans().get(0).getCode()))) {
			filterCodes.add(Constants.TWO_MEAL_AVAIL);
		}
		if (mealplanFilterEnable && CollectionUtils.isNotEmpty(ratePlan.getMealPlans())
				&&  (Constants.MEAL_PLAN_CODE_ALL_MEALS_AI.equalsIgnoreCase(ratePlan.getMealPlans().get(0).getCode())
				|| Constants.MEAL_PLAN_CODE_ALL_MEALS.equalsIgnoreCase(ratePlan.getMealPlans().get(0).getCode()))) {
			filterCodes.add(Constants.ALL_MEAL_AVAIL);
		}
		if (CollectionUtils.isNotEmpty(ratePlan.getCancelPenaltyList())
				&& null != ratePlan.getCancelPenaltyList().get(0).getCancellationType()
				&& ratePlan.getCancelPenaltyList().get(0).getCancellationType() == CancellationType.FREE_CANCELLATON) {
			filterCodes.add(Constants.FREE_CANCELLATION);
		}
		if (CollectionUtils.isNotEmpty(ratePlan.getCancelPenaltyList())
				&& null != ratePlan.getCancelPenaltyList().get(0).getCancellationType()
				&& ratePlan.getCancelPenaltyList().get(0).getCancellationType() == CancellationType.FREE_CANCELLATON
				&& ratePlan.getMpFareHoldStatus()!=null && ratePlan.getMpFareHoldStatus().isHoldEligible() && ratePlan.getMpFareHoldStatus().getExpiry()!=null &&
				ratePlan.getMpFareHoldStatus().getBookingAmount()==0f) {
			filterCodes.add(BOOK_NOW_AT_0);
		}

		if (CollectionUtils.isNotEmpty(ratePlan.getCancelPenaltyList())
				&& null != ratePlan.getCancelPenaltyList().get(0).getCancellationType()
				&& ratePlan.getCancelPenaltyList().get(0).getCancellationType() == CancellationType.FREE_CANCELLATON
				&& ratePlan.getMpFareHoldStatus()!=null && ratePlan.getMpFareHoldStatus().isHoldEligible() && ratePlan.getMpFareHoldStatus().getExpiry()!=null &&
				ratePlan.getMpFareHoldStatus().getBookingAmount()==1f) {
			filterCodes.add(BOOK_NOW_AT_1);
		}
		if(Utility.isMyBizRequest()
				&& StringUtils.isNotEmpty(ratePlan.getSegmentId())
				&& ratePlan.getSegmentId().equals(ONE_ON_ONE_RATE_SEGMENT)) {
			filterCodes.add(CONTRACTED_FARE);
		}
 		if(Utility.isMyBizRequest()
				&& StringUtils.isNotEmpty(ratePlan.getSegmentId())
				&& HOTEL_CLOUD_RATE_SEGMENT.equals(ratePlan.getSegmentId())) {
			filterCodes.add(HOTEL_CLOUD);
		}
		if(!(Objects.nonNull(commonModifierResponse) && Objects.nonNull(commonModifierResponse.getExtendedUser()) &&  Utility.isMyPartnerRequest(commonModifierResponse.getExtendedUser().getProfileType(), commonModifierResponse.getExtendedUser().getAffiliateId()))) {
			if (null != ratePlan.getPaymentDetails()
					&& ratePlan.getPaymentDetails().getPaymentMode() != PaymentMode.PAS) {
				if(isBlockPAH && ap < 5){
					filterCodes.add("FCZPN");
				}else {
					filterCodes.add("PAH");
				}
			}
			if(ratePlan.isBnplApplicable()){
				filterCodes.add("FCZPN");
			}
			if(CollectionUtils.isNotEmpty(ratePlan.getInclusions()) && ratePlan.getInclusions().stream().anyMatch(a-> ("Packages".equalsIgnoreCase(a.getCategory()) || "Packages1".equalsIgnoreCase(a.getCategory())
					|| "Packages2".equalsIgnoreCase(a.getCategory()) || "Packages3".equalsIgnoreCase(a.getCategory())
					|| "MMTBLACK".equalsIgnoreCase(a.getCategory())))){
				filterCodes.add("SPECIALDEALS");
			}

			if(ratePlan.isStaycationDeal()){
				filterCodes.add("STAYCATION");
			}
		}

		if (ratePlan.getPackageRoomRatePlan()) {
			filterCodes.add(PACKAGE_RATE);
		}

		if (!RTB_EMAIL.equalsIgnoreCase(ratePlan.getRpBookingModel())) {
			filterCodes.add(Constants.INSTANT_BOOKING);
		}

		if (partnerExclusiveFilterEnable && mypatExclusiveRateSegmentIdList.contains(ratePlan.getSegmentId()))
			filterCodes.add("CTA_RATES_AVAIL");

		if (StringUtils.isNotEmpty(roomName) && (roomName.contains("Suite") || roomName.contains("suite"))) {
			filterCodes.add("SUITE");
		}
		return filterCodes;
	}

	public List<Tariff> getTariffs(List<com.mmt.hotels.model.response.pricing.RatePlan> list, String expData, String askedCurrency,String sellableType, int days, String funnelSource, boolean groupBookingPrice,
								   boolean myPartner, boolean isAltAccoHotel, final MarkUpDetails markUpDetails, boolean newPropertyOfferApplicable,
								   boolean isHighSellingAltAcco) {

		if (CollectionUtils.isEmpty(list))
			return null;
		List<Tariff> tariffList = new ArrayList<>();
		Type type = new com.google.gson.reflect.TypeToken<Map<String, String>>() {
		}.getType();
		expData = expData.replaceAll("^\"|\"$", "");
		Map<String,String> expDataMap = gson.fromJson(expData, type);
		NoCostEmiDetails noCostEmiDetailForRootLevel = new NoCostEmiDetails();
		for (com.mmt.hotels.model.response.pricing.RatePlan ratePlan : list) {
			Tariff tariff = new Tariff();
			tariff.setTariffCode(ratePlan.getRatePlanCode());
			tariff.setOccupancydetails(new RoomTariff());
			if(null != ratePlan.getAvailDetails()) {
				if(null != ratePlan.getAvailDetails().getOccupancyDetails()) {
					tariff.getOccupancydetails().setNumberOfAdults(ratePlan.getAvailDetails().getOccupancyDetails().getAdult());
					tariff.getOccupancydetails().setNumberOfChildren(ratePlan.getAvailDetails().getOccupancyDetails().getChild());
					if (ratePlan.getAvailDetails().getOccupancyDetails().getChild() > 0)
						tariff.getOccupancydetails().setChildAges(ratePlan.getAvailDetails().getOccupancyDetails().getChildAges());
					tariff.getOccupancydetails().setRoomCount(ratePlan.getAvailDetails().getOccupancyDetails().getNumOfRooms());
				}
				tariff.setAvailCount(ratePlan.getAvailDetails().getCount());
			}
			if (ratePlan.getDisplayFare() != null)
				tariff.setBnplApplicable(ratePlan.getDisplayFare().getIsBNPLApplicable());
			tariff.setBnplPersuasionMsg(ratePlan.getBnplPersuasionMsg());
			tariff.setRoomTariffs(getRoomTariffs(ratePlan.getRoomTariff()));
			tariff.setMtKey(ratePlan.getMtKey());
			tariff.setIsPreApproved(ratePlan.getPreApproved());
			//set campaign alert node
			if(StringUtils.isNotBlank(ratePlan.getCampaingText())) {
				Alert alert = new Alert();
				alert.setType(AlertType.FREE_CANC_CAMPAIGN);
				alert.setText(ratePlan.getCampaingText());
				tariff.setCampaignAlert(alert);
			}else if(ratePlan.isAllInclusiveRate() &&
					utility.isExperimentOn(expDataMap, Constants.ALL_INCLUSIVE_TRANSFER_EXPERIMENT)
					&& CollectionUtils.isNotEmpty(ratePlan.getAdditionalFees())){
				try {
					double additionalFee = ratePlan.getAdditionalFees().stream()
							.map(e -> e.getAskedCurrencyAmount())
							.collect(Collectors.summingDouble(Double::doubleValue));
					Alert alert = new Alert();
					alert.setType(AlertType.MANDATORY_FEE);
					alert.setText(mandatoryChargesAlert.replace("{currency_code}", askedCurrency).replace("{amount}", String.valueOf((int) additionalFee)));
					tariff.setCampaignAlert(alert);
				}catch(Exception e){
					LOGGER.error("Error while building mandatory fee alert",e);
				}

			}
			tariff.setPriceMap(commonResponseTransformer.getPriceMap(ratePlan.getDisplayFare().getDisplayPriceBreakDown(),
					ratePlan.getDisplayFare().getDisplayPriceBreakDownList(), expData,
					null != ratePlan.getAvailDetails() ? ratePlan.getAvailDetails().getOccupancyDetails().getNumOfRooms() : null,
					askedCurrency,sellableType, days,
					ratePlan.getDisplayFare().getCorpMetaData() != null, ratePlan.getSegmentId(),
					utility.buildToolTip(funnelSource), utility.isGroupBookingFunnel(funnelSource),
					groupBookingPrice, myPartner,isAltAccoHotel, markUpDetails, noCostEmiDetailForRootLevel,
					ratePlan.getLinkedRates(), newPropertyOfferApplicable, isHighSellingAltAcco));
            tariff.setEmiPlanDetail(commonResponseTransformer.buildEmiPlanDetails(noCostEmiDetailForRootLevel));
            tariff.setDefaultPriceKey(ratePlan.getDisplayFare().getDisplayPriceBreakDown() != null ? (ratePlan.getDisplayFare().getDisplayPriceBreakDown().getCouponInfo() != null ?
					ratePlan.getDisplayFare().getDisplayPriceBreakDown().getCouponInfo().getCouponCode() : "DEFAULT"): null);
			tariffList.add(tariff);
		}
		return tariffList;
	}

	private List<RoomTariff> getRoomTariffs(List<com.mmt.hotels.model.response.pricing.RoomTariff> roomTariff) {
		if (CollectionUtils.isEmpty(roomTariff))
			return null;
		List<RoomTariff> roomTariffs = new ArrayList<>();
		for (com.mmt.hotels.model.response.pricing.RoomTariff room : roomTariff) {
			RoomTariff roomTariffCG = new RoomTariff();
			roomTariffCG.setNumberOfAdults(room.getNumberOfAdults());
			roomTariffCG.setNumberOfChildren(room.getNumberOfChildren());
			roomTariffCG.setDisplayPrice(room.getPerNightPrice());
			roomTariffCG.setChildBuckets(room.getChildAgesBuckets());
			if (room.getNumberOfChildren()>0)
				roomTariffCG.setChildAges(room.getChildAges());
			roomTariffs.add(roomTariffCG);
		}
		return roomTariffs;
	}

	private List<OfferDetail> getOffers(List<RangePrice> offers) {
		if (CollectionUtils.isEmpty(offers))
			return null;
		List<OfferDetail> offerDetails = new ArrayList<>();
		for (RangePrice rangePrice : offers) {
			OfferDetail offerDetail = new OfferDetail();
			offerDetail.setLongText(rangePrice.getLongText());
			offerDetail.setOfferType(rangePrice.getOfferType());
			offerDetail.setPriority(rangePrice.getPriority());
			offerDetail.setShortText(rangePrice.getShortText());
			offerDetail.setTncLink(rangePrice.getTncLink());
			offerDetail.setIconUrl(rangePrice.getIconUrl());
			offerDetails.add(offerDetail);
		}
		return offerDetails;
	}

	private SelectRoomBanner buildBanner(SearchRoomsResponse searchRoomsResponse, HotelRates hotelRates) {
		if (hotelRates!=null && searchRoomsResponse!=null) {
			SelectRoomBanner banner = new SelectRoomBanner();
			boolean hasFlexiCancel = false;
			String flexiCancelBannerTitle = null;
			if (CollectionUtils.isNotEmpty(searchRoomsResponse.getRecommendedCombos()) && hotelRates.getRecommendedRoomTypeDetails() != null) {
				hasFlexiCancel = isHasFlexiCancelInRoomDetails(hotelRates.getRecommendedRoomTypeDetails().getRoomType());
				flexiCancelBannerTitle = getFlexiCancelBannerTitleCombo(hotelRates.getRecommendedRoomTypeDetails().getFlexiCancelRoomDetail());
			}

			if (CollectionUtils.isNotEmpty(searchRoomsResponse.getExactRooms()) && hotelRates.getRoomTypeDetails()!=null) {
				Map<String,RoomType> roomTypeMap = hotelRates.getRoomTypeDetails().getRoomType();
				Optional<RoomType> maxRoom = roomTypeMap.values()
						.stream()
						.max(Comparator.comparingInt(room -> (room.getSelectedAmenities()==null?0:room.getSelectedAmenities().size())));

				hasFlexiCancel = isHasFlexiCancelInRoomDetails(roomTypeMap);
				flexiCancelBannerTitle = getFlexiCancelBannerTitle(roomTypeMap);

				if (hasFlexiCancel){
					makeBanner(banner);
					banner.setDescription(flexiCancelBannerTitle);
					return banner;
				}
				if (maxRoom.isPresent() && !maxRoom.get().getRoomTypeCode().equalsIgnoreCase(searchRoomsResponse.getExactRooms().get(0).getRoomCode())) {
					makeBanner(banner,maxRoom.get());
					return banner;
				}
			} else if (CollectionUtils.isNotEmpty(searchRoomsResponse.getOccupancyRooms()) && !hasFlexiCancel) {
				Map<String,RoomType> roomTypeMap = hotelRates.getOccupencyLessRoomTypeDetails().getRoomType();
				Optional<RoomType> maxRoom = roomTypeMap.values()
						.stream()
						.max(Comparator.comparingInt(room -> (room.getSelectedAmenities()==null?0:room.getSelectedAmenities().size())));
				if (maxRoom.isPresent() && !maxRoom.get().getRoomTypeCode().equalsIgnoreCase(searchRoomsResponse.getOccupancyRooms().get(0).getRoomCode())) {
					makeBanner(banner,maxRoom.get());
					return banner;
				}
			} else if (CollectionUtils.isNotEmpty(searchRoomsResponse.getRecommendedCombos())) {
				Map<String,RoomType> roomTypeMap = hotelRates.getRecommendedRoomTypeDetails().getRoomType();
				Optional<RoomType> maxRoom = roomTypeMap.values()
						.stream()
						.max(Comparator.comparingInt(room -> (room.getSelectedAmenities()==null?0:room.getSelectedAmenities().size())));

				if (hasFlexiCancel){
					makeBanner(banner);
					banner.setDescription(flexiCancelBannerTitle);
					return banner;
				}
				if (maxRoom.isPresent() && searchRoomsResponse.getRecommendedCombos().get(0).getRooms().stream().noneMatch(e->e.getRoomCode().equalsIgnoreCase(maxRoom.get().getRoomTypeCode()))) {
					makeBanner(banner,maxRoom.get());
					return banner;
				}
			}
		}
		return null;
	}

	private String getFlexiCancelBannerTitleCombo(FlexiCancelDetails flexiCancelRoomDetail) {
		String text = null;
		if (flexiCancelRoomDetail != null &&
				MapUtils.isNotEmpty(flexiCancelRoomDetail.getAddOnDetails()) &&
				flexiCancelRoomDetail.getAddOnDetails().containsKey(FLEXI_CANCEL) &&
				flexiCancelRoomDetail.getAddOnDetails().get(FLEXI_CANCEL).getRemoved() != null &&
				flexiCancelRoomDetail.getAddOnDetails().get(FLEXI_CANCEL).getRemoved().getTitle() != null) {
			text = flexiCancelRoomDetail.getAddOnDetails().get(FLEXI_CANCEL).getRemoved().getTitle();
		}
		return text;
	}

	private boolean isHasFlexiCancelInRoomDetails(Map<String, RoomType> roomTypeMap) {
		boolean hasFlexiCancelInCombo = roomTypeMap.values()
				.stream()
				.filter(room -> MapUtils.isNotEmpty(room.getRatePlanList()))  // Check if RatePlan is present
				.anyMatch(room -> room.getRatePlanList().values().stream()
						.anyMatch(ratePlan -> ratePlan.getFlexiCancelAddOn() != null && MapUtils.isNotEmpty(ratePlan.getFlexiCancelAddOn().getAddOnPolicies()) &&
								ratePlan.getFlexiCancelAddOn().getAddOnPolicies().containsKey(FLEXI_CANCEL)));
		return hasFlexiCancelInCombo;
	}

	private String getFlexiCancelBannerTitle(Map<String, RoomType> roomTypeMap) {
		Optional<String> removedTitle = roomTypeMap.values()
				.stream()
				.filter(room -> MapUtils.isNotEmpty(room.getRatePlanList()))  // Check if RatePlan is present
				.flatMap(room -> room.getRatePlanList().values().stream())
				.filter(ratePlan -> {
					FlexiCancelDetails flexiCancelDetails = ratePlan.getFlexiCancelDetails();
					return flexiCancelDetails != null &&
							MapUtils.isNotEmpty(flexiCancelDetails.getAddOnDetails()) &&
							flexiCancelDetails.getAddOnDetails().containsKey(FLEXI_CANCEL) &&
							flexiCancelDetails.getAddOnDetails().get(FLEXI_CANCEL).getRemoved() != null &&
							flexiCancelDetails.getAddOnDetails().get(FLEXI_CANCEL).getRemoved().getTitle() != null;
				})
				.map(ratePlan -> ratePlan.getFlexiCancelDetails().getAddOnDetails().get(FLEXI_CANCEL).getRemoved().getTitle())
				.findFirst();

		return removedTitle.orElse(null);
	}

	private void makeBanner(SelectRoomBanner banner) {
			banner.setType(FLEXI_CANCEL);
			banner.setTitle(polyglotService.getTranslatedData(FLEXI_CANCEL_DETAIL_BANNER_TITLE));
			banner.setDescription(polyglotService.getTranslatedData(FLEXI_CANCEL_DETAIL_BANNER_DESC));
			banner.setIconUrl(flexiCancelStaticDetail != null ? flexiCancelStaticDetail.getIconUrl() : "");
			banner.setRedirectLink(flexiCancelStaticDetail != null ? flexiCancelStaticDetail.getRedirectUrl() : "");
			banner.setBgImageUrl(flexiCancelStaticDetail != null ? flexiCancelStaticDetail.getBackgroundImage() : "");
			banner.setActionText(polyglotService.getTranslatedData(FLEXI_CANCEL_LEAN_MORE));
	}

	private void makeBanner(SelectRoomBanner banner, RoomType roomDetails) {
		String text = StringUtils.EMPTY;
		try {
			if (roomDetails.getSelectedAmenities().size()==1) {
				text = polyglotService.getTranslatedData(SELECT_ROOM_1_AMENITIES_BANNER)
						.replace(ROOM_NAME,roomDetails.getRoomTypeName())
						.replace(AMENITY_1,roomDetails.getSelectedAmenities().get(0).getName());
			} else if (roomDetails.getSelectedAmenities().size()==2) {
				text = polyglotService.getTranslatedData(SELECT_ROOM_2_AMENITIES_BANNER)
						.replace(ROOM_NAME,roomDetails.getRoomTypeName())
						.replace(AMENITY_1,roomDetails.getSelectedAmenities().get(0).getName())
						.replace(AMENITY_2,roomDetails.getSelectedAmenities().get(1).getName());
			} else {
				text = polyglotService.getTranslatedData(SELECT_ROOM_3_AMENITIES_BANNER)
						.replace(ROOM_NAME,roomDetails.getRoomTypeName())
						.replace(AMENITY_1,roomDetails.getSelectedAmenities().get(0).getName())
						.replace(AMENITY_2,roomDetails.getSelectedAmenities().get(1).getName())
						.replace(AMENITY_3,roomDetails.getSelectedAmenities().get(2).getName());
			}
		} catch (Exception e) {
			LOGGER.error("Search-rooms banner could not be made");
		}
		if (StringUtils.isNotBlank(text)) {
			banner.setTitle(text);
			banner.setRedirectType(Constants.SELECT_ROOM_BANNER_TYPE);
			banner.setRedirectLink(roomDetails.getRoomTypeCode());
			banner.setIconUrl(Constants.SELECT_ROOM_BANNER_ICON_URL);
			banner.setBgColor(Constants.SELECT_ROOM_BANNER_BG_COLOR);
		} else {
			banner = null;
		}
	}

	public String getComboText(String mealPlanCode, long differenceInPriceFromBaseCombo) {
		if (StringUtils.isNotBlank(mealPlanCode)) {
			switch (mealPlanCode) {
				case Constants.MEAL_PLAN_CODE_ACC_ONLY:
					return "<b>"+polyglotService.getTranslatedData(ConstantsTranslation.ACCOMODATION_ONLY)+"</b>";

				case Constants.MEAL_PLAN_CODE_BED_ONLY:
					return "<b>"+polyglotService.getTranslatedData(ConstantsTranslation.BED_ONLY)+"</b>";

				case Constants.MEAL_PLAN_CODE_ROOM_ONLY:
					return "<b>"+polyglotService.getTranslatedData(ConstantsTranslation.ROOM_ONLY_TEXT)+"</b>";

				case Constants.MEAL_PLAN_CODE_BREAKFAST:
					return polyglotService.getTranslatedData(ADD_BREAKFAST).replace(ConstantsTranslation.AMOUNT.toUpperCase(), String.valueOf(differenceInPriceFromBaseCombo));

				case Constants.MEAL_PLAN_CODE_BREAKFAST_LUNCH:
					return polyglotService.getTranslatedData(ADD_BREAKFAST_AND_LUNCH).replace(ConstantsTranslation.AMOUNT.toUpperCase(), String.valueOf(differenceInPriceFromBaseCombo));

				case Constants.MEAL_PLAN_CODE_BREAKFAST_DINNER:
					return polyglotService.getTranslatedData(ADD_BREAKFAST_AND_DINNER).replace(ConstantsTranslation.AMOUNT.toUpperCase(), String.valueOf(differenceInPriceFromBaseCombo));

				case Constants.MEAL_PLAN_CODE_BREAKFAST_LUNCH_OR_DINNER:
					return polyglotService.getTranslatedData(ADD_BREAKFAST_LUNCH_OR_DINNER).replace(ConstantsTranslation.AMOUNT.toUpperCase(), String.valueOf(differenceInPriceFromBaseCombo));

				case Constants.MEAL_PLAN_CODE_ALL_MEALS:
				case Constants.MEAL_PLAN_CODE_ALL_MEALS_AI:
					return polyglotService.getTranslatedData(ADD_BREAKFAST_LUNCH_AND_DINNER).replace(ConstantsTranslation.AMOUNT.toUpperCase(), String.valueOf(differenceInPriceFromBaseCombo));

				default:
					return null;

			}
		}
		return null;
	}

	public CalendarAvailabilityResponse convertCalendarAvailabilityResponse(com.mmt.hotels.model.response.CalendarAvailabilityResponse calendarAvailabilityResponseHES, String currency) {
		CalendarAvailabilityResponse calendarAvailabilityResponseCG = new CalendarAvailabilityResponse();
		Map<String, CalendarBO> dates = new LinkedHashMap<>();
		if(MapUtils.isNotEmpty(calendarAvailabilityResponseHES.getDates())) {
			calendarAvailabilityResponseHES.getDates().forEach((date, calendarBOHES) -> {
				CalendarBO calendarBO = new CalendarBO();
				calendarBO.setStatus(calendarBOHES.getStatus().name());
				calendarBO.setPrice(calendarBOHES.getPrice());
				calendarBO.setPriceColor(utility.getPriceColorForPriceDrop(calendarBOHES.getPriceVariationType()));
				dates.put(date, calendarBO);
			});
		}
		calendarAvailabilityResponseCG.setDates(dates);
		calendarAvailabilityResponseCG.setCurrency(currency);
		return calendarAvailabilityResponseCG;
	}

	/**
	 * Build instant fare details if any of the rates is a negotiated rate and set instantFareInfo field of searchRoomsResponse.
	 *
	 * @param corpAlias             Organisation alias name.
	 * @param searchRoomsResponse   {@link SearchRoomsResponse} object.
	 * @param currency              User selected currency.
	 * @param isMyBizNewDetialsPage
	 */
	private void buildInstantFareInfo(String corpAlias, SearchRoomsResponse searchRoomsResponse, String currency, boolean isMyBizNewDetialsPage) {
		InstantFareInfo instantFareInfo = new InstantFareInfo();

		String title = StringUtils.EMPTY;

		if (CLIENT_DESKTOP.equalsIgnoreCase(MDC.get(MDCHelper.MDCKeys.CLIENT.getStringValue()))) {
			title = isMyBizNewDetialsPage ? polyglotService.getTranslatedData(ConstantsTranslation.INSTANT_FARE_INFO_MYB_NEW_DETAILS_TITLE) : polyglotService.getTranslatedData(ConstantsTranslation.INSTANT_FARE_INFO_TITLE);
		} else {
			title = polyglotService.getTranslatedData(INSTANT_FARE_INFO_TITLE_MOBILE);
		}
		corpAlias = corpAlias != null ? corpAlias : StringUtils.EMPTY;
		title = StringUtils.replace(title, "{CORP_ALIAS}", corpAlias);
		title = StringUtils.replace(title, "{NO_OF_HOURS}", String.valueOf(noOfHoursForConfirmation));
		instantFareInfo.setTitle(title);

		instantFareInfo.setHeader(polyglotService.getTranslatedData(INSTANT_FARE_INFO_HEADER));

		String subHeader = polyglotService.getTranslatedData(INSTANT_FARE_INFO_SUBHEADER);
		// Get currency symbol based on user selected currency.
		String currencySymbol = Currency.getCurrencyEnum(StringUtils.isNotBlank(currency) ? currency : DEFAULT_CUR_INR).getCurrencySymbol();
		subHeader = StringUtils.replace(subHeader, "{CURRENCY_SYMBOL}", currencySymbol);
		instantFareInfo.setSubheader(subHeader);

		Cta cta = new Cta();
		cta.setTitle(polyglotService.getTranslatedData(INSTANT_BOOKING_FARES));
		cta.setProceedText(polyglotService.getTranslatedData(PROCEED_BOOKING_TEXT));
		List<FiltersData> filtersDataList = new ArrayList<>();
		addFiltersData(filtersDataList, polyglotService.getTranslatedData(INSTANT_BOOKING), Constants.INSTANT_BOOKING);
		cta.setFiltersData(filtersDataList);
		instantFareInfo.setCta(cta);

		searchRoomsResponse.setSpecialFareInfo(instantFareInfo);
	}

	/**
	 * Build filtersData from title, code and add it to {@code filtersDataList}.
	 *
	 * @param filtersDataList Collection of filtersData.
	 * @param title           title for filtersData.
	 * @param code            code for filtersData.
	 */
	private void addFiltersData(List<FiltersData> filtersDataList, String title, String code) {
		FiltersData filtersData = new FiltersData();
		filtersData.setTitle(title);
		filtersData.setCode(code);
		filtersDataList.add(filtersData);
	}

	/**
	 * Build special fare persuasion for mobile if the rate plan is negotiated rate plan.
	 * Negotiated rates are the one-on-one rates that are directly negotiated between the corporate/organization and the hotel.
	 *
	 * @param corpAlias 	Organisation alias name.
	 * @return special fare persuasion.
	 */
	protected PersuasionResponse buildSpecialFarePersuasionForMobile(String corpAlias,boolean isNewSelectRoomPage) {
		PersuasionResponse specialFarePersuasion = new PersuasionResponse();
		specialFarePersuasion.setPlaceholderId(PRICE_BOTTOM_PLACEHOLDER_ID);
		String persuasionText = polyglotService.getTranslatedData(ConstantsTranslation.SPECIAL_FARE_TAG_MOBILE);
		corpAlias = corpAlias != null ? corpAlias : SPECIAL;
		persuasionText = StringUtils.replace(persuasionText, "{CORP_ALIAS}", corpAlias);
		specialFarePersuasion.setPersuasionText(persuasionText);
		specialFarePersuasion.setHtml(true);
		specialFarePersuasion.setTemplate(OVAL_PERSUASION_TEMPLATE);
		Style style = new Style();
		BGGradient bgGradient = new BGGradient();
		bgGradient.setAngle("H");
		if(isNewSelectRoomPage){
			bgGradient.setColor(Arrays.asList("#F2C21A", "#C06D0C"));
		}else{
			bgGradient.setColor(Arrays.asList("#EEAF4C", "#CE6112"));
		}
		style.setBgGradient(bgGradient);
		style.setTextColor("#ffffff");
		style.setFontSize("SMALL");
		style.setMaxLines(1);
		style.setFontType("B");
		specialFarePersuasion.setStyle(style);
		return specialFarePersuasion;
	}

	protected PersuasionResponse buildBnplPersuasion() {
		PersuasionResponse bnplPersuasion = new PersuasionResponse();
		bnplPersuasion.setPlaceholderId(PRICE_BOTTOM_PLACEHOLDER_ID);
		bnplPersuasion.setPersuasionText(polyglotService.getTranslatedData(BNPL_DETAIL_PERSUASION_TITLE));
		bnplPersuasion.setHtml(false);
		bnplPersuasion.setTemplate(IMAGE_TEXT_H);
		Style style = new Style();
		style.setTextColor("#FF018786");
		style.setFontSize("SMALL");
		style.setFontType("B");
		bnplPersuasion.setStyle(style);
		return bnplPersuasion;
	}

	/**
	 * Build booking confirmation from hotelier persuasion for negotiated rate plan in case of mobile client.
	 * Negotiated rates are the one-on-one rates that are directly negotiated between the corporate/organization and the hotel.
	 *
	 * @return booking confirmation persuasion.
	 */
	protected PersuasionResponse buildBookingConfirmationPersuasionForMobile(String corpAlias,boolean isNewSelectRoomPage) {
		PersuasionResponse bookingConfirmationPersuasion = new PersuasionResponse();
		bookingConfirmationPersuasion.setPlaceholderId(BOTTOM_BOX_PLACEHOLDER_ID);
		String persuasionText = polyglotService.getTranslatedData(BOOKING_CONFIRMATION_TEXT_MOBILE);
		persuasionText = StringUtils.replace(persuasionText, NO_OF_HOURS_PLACEHOLDER, String.valueOf(noOfHoursForConfirmation));
		if(isNewSelectRoomPage){
			persuasionText = StringUtils.replace(persuasionText, OPEN_BOLD_TAG, "");
			persuasionText = StringUtils.replace(persuasionText, CLOSE_BOLD_TAG, "");
			bookingConfirmationPersuasion.setIconUrl(negotiatedRateIconUrlNewApp);
		}else{
			bookingConfirmationPersuasion.setIconUrl(negotiatedRateIconUrl);
		}
		bookingConfirmationPersuasion.setPersuasionText(persuasionText);
		bookingConfirmationPersuasion.setHtml(true);
		bookingConfirmationPersuasion.setTemplate(TEXT_IMG_PERSUASION_TEMPLATE);
		Style style = new Style();
		if(isNewSelectRoomPage){
			style.setBgColor("#FFEDD1");
		}else{
			style.setBgColor("#FFF7E6");
		}
		style.setTextColor("#CF8100");
		style.setFontSize("SMALL");
		style.setGravity("center");
		style.setIconHeight(16);
		style.setIconWidth(16);
		bookingConfirmationPersuasion.setStyle(style);
		SubInfo subInfo = new SubInfo();
		String header = polyglotService.getTranslatedData(ConstantsTranslation.SPECIAL_FARE_TAG_MOBILE);
		corpAlias = corpAlias != null ? corpAlias : SPECIAL;
		header = StringUtils.replace(header, "{CORP_ALIAS}", corpAlias);
		subInfo.setHeader(header);
		String text = polyglotService.getTranslatedData(SPECIAL_FARE_TEXT_MOBILE);
		text = StringUtils.replace(text, NO_OF_HOURS_PLACEHOLDER, String.valueOf(noOfHoursForConfirmation));
		if(isNewSelectRoomPage){
			text = StringUtils.replace(text, "FBA71B" , "A5572A");
		}
		subInfo.setText(text);
		subInfo.setSubtext(polyglotService.getTranslatedData(SPECIAL_FARE_SUBTEXT_MOBILE));
		bookingConfirmationPersuasion.setSubInfo(subInfo);
		return bookingConfirmationPersuasion;
	}

	/**
	 * Adding label,key,type and amount from supplier detail map(deal/amount/expiry)
	 *
	 * @param hotelRates
	 * @return PrimaryOffer
	 */
	private PrimaryOffer getPrimaryOfferForSupplierDeals(HotelRates hotelRates,String askedCurrency) {
		Map<String, String> supplierDealsDetailMap = null;
		String deal, amount;
		long expiry=0;
		PrimaryOffer primaryOffer = new PrimaryOffer();

		Map<String, RoomType> roomTypeMap = getRoomType(hotelRates);
		if (MapUtils.isNotEmpty(roomTypeMap)) {
			Optional<String> firstRoomType = roomTypeMap.keySet().stream().findFirst();
			if (firstRoomType.isPresent()) {
				String firstRoomTypeKey = firstRoomType.get();
				RoomType roomType = roomTypeMap.get(firstRoomTypeKey);
				Optional<String> firstRatePlan = roomType.getRatePlanList().keySet().stream().findFirst();
				if (firstRatePlan.isPresent()) {
					String firstRatePlanKey = firstRatePlan.get();
					RatePlan ratePlan = roomType.getRatePlanList().get(firstRatePlanKey);
					if (null != ratePlan.getDisplayFare() && null != ratePlan.getDisplayFare().getDisplayPriceBreakDown() && MapUtils.isNotEmpty(ratePlan.getDisplayFare().getDisplayPriceBreakDown().getSupplierDealsDetailMap())) {
						supplierDealsDetailMap = ratePlan.getDisplayFare().getDisplayPriceBreakDown().getSupplierDealsDetailMap();
						if (StringUtils.isNotEmpty(supplierDealsDetailMap.get(DEAL)) && StringUtils.isNotEmpty(supplierDealsDetailMap.get(DISCOUNT))) {
							deal = supplierDealsDetailMap.get(DEAL);
							amount = String.valueOf(Math.round(Double.parseDouble(supplierDealsDetailMap.get(DISCOUNT))));
							if (supplierDealsDetailMap.containsKey(EXPIRY) && StringUtils.isNotEmpty(supplierDealsDetailMap.get(EXPIRY))) {
								expiry = Long.parseLong(supplierDealsDetailMap.get(EXPIRY));
							}
							setPrimaryOfferFieldsForSupplier(deal, amount, expiry, primaryOffer,askedCurrency);
						}
					}
				}
			}
		}
		return primaryOffer;
	}

	private PrimaryOffer getPrimaryOfferForNoCostEmi(HotelRates hotelRates) {
		//Build No-Cost emi detail page persuasion for Apps only
		String client = MDC.get(MDCHelper.MDCKeys.CLIENT.getStringValue());
		if (hotelRates == null || !(Constants.ANDROID.equalsIgnoreCase(client) || Constants.DEVICE_IOS.equalsIgnoreCase(client))) {
			return null;
		}

		NoCostEmiDetails noCostEmiDetails = getNoCostEmiDetailsFromRoomTypeDetails(hotelRates.getRoomTypeDetails());
		if (noCostEmiDetails == null) {
			noCostEmiDetails = getNoCostEmiDetailsFromRecommendedRoomTypeDetails(hotelRates.getRecommendedRoomTypeDetails());
		}

		if (noCostEmiDetails != null) {
			PrimaryOffer primaryOffer = new PrimaryOffer();
			setPrimaryOfferFieldsForNoCostEmi(Math.round(noCostEmiDetails.getEmiAmount()), noCostEmiDetails.getTenure(), primaryOffer);
			return primaryOffer;
		}

		return null;
	}

	private NoCostEmiDetails getNoCostEmiDetailsFromRoomTypeDetails(RoomTypeDetails roomTypeDetails) {
		if (roomTypeDetails == null || MapUtils.isEmpty(roomTypeDetails.getRoomType())) {
			return null;
		}

		return roomTypeDetails.getRoomType().values().stream()
				.filter(roomType -> MapUtils.isNotEmpty(roomType.getRatePlanList()))
				.map(roomType -> roomType.getRatePlanList().values().stream()
						.filter(ratePlan -> ratePlan.getDisplayFare() != null
								&& CollectionUtils.isNotEmpty(ratePlan.getDisplayFare().getDisplayPriceBreakDownList()))
						.flatMap(ratePlan -> ratePlan.getDisplayFare().getDisplayPriceBreakDownList().stream())
						.flatMap(displayPriceBreakDown -> Optional.ofNullable(displayPriceBreakDown.getNoCostEmiDetailsList())
								.orElse(Collections.emptyList()).stream())
						.findFirst())
				.filter(Optional::isPresent)
				.map(Optional::get)
				.findFirst()
				.orElse(null);
	}

	private NoCostEmiDetails getNoCostEmiDetailsFromRecommendedRoomTypeDetails(RoomTypeDetails roomTypeDetails) {
		if (roomTypeDetails == null || roomTypeDetails.getTotalDisplayFare() == null ||
				CollectionUtils.isEmpty(roomTypeDetails.getTotalDisplayFare().getDisplayPriceBreakDownList())) {
			return null;
		}

		return roomTypeDetails.getTotalDisplayFare().getDisplayPriceBreakDownList().stream()
				.flatMap(displayPriceBreakDown -> Optional.ofNullable(displayPriceBreakDown.getNoCostEmiDetailsList())
						.orElse(Collections.emptyList()).stream())
				.findFirst()
				.orElse(null);
	}

	private void setPrimaryOfferFieldsForNoCostEmi(long emiAmount, int tenure, PrimaryOffer primaryOffer) {
		Style style = new Style();
		style.setBgColor(supplierBgColor);
		primaryOffer.setStyle(style);
		primaryOffer.setDescription(MessageFormat.format(polyglotService.getTranslatedData(ConstantsTranslation.NO_COST_EMI_DETAIL_PAGE_TEXT), emiAmount, tenure));
		primaryOffer.setType(polyglotService.getTranslatedData(NO_COST_EMI));
		primaryOffer.setIconUrl(noCostEmiIconUrl);
	}

	private Map<String, RoomType> getRoomType(HotelRates hotelRates) {
		if (hotelRates!=null) {
			if (hotelRates.getRoomTypeDetails() != null && MapUtils.isNotEmpty(hotelRates.getRoomTypeDetails().getRoomType())) {
				return hotelRates.getRoomTypeDetails().getRoomType();
			} else if (hotelRates.getRecommendedRoomTypeDetails() != null && MapUtils.isNotEmpty(hotelRates.getRecommendedRoomTypeDetails().getRoomType())) {
				return hotelRates.getRecommendedRoomTypeDetails().getRoomType();
			}
		}
		return null;
	}

	/**
	 * Setting Primary Offer Fields
	 *
	 * @param deal,amount,expiry,primaryOffer
	 * @return void
	 */
	private void setPrimaryOfferFieldsForSupplier(String deal, String amount, long expiry, PrimaryOffer primaryOffer,String askedCurrency) {
		Style style = new Style();
		style.setBgColor(supplierBgColor);
		primaryOffer.setStyle(style);
		String description = null;
		if (deal.equalsIgnoreCase(EARLY_BIRD)) {
			description = polyglotService.getTranslatedData(PRIMARY_OFFER_DESCRIPTION).replace("{amount}", amount);
		} else {
			description = polyglotService.getTranslatedData(PRIMARY_OFFER_DESCRIPTION_LAST_MINUTE).replace("{amount}", amount);
		}
		String finalDescriptionText = utility.buildDescriptionText(description,amount, askedCurrency);
		primaryOffer.setDescription(finalDescriptionText);
		if (expiry>0) {
			primaryOffer.setExpiry(expiry);
		}
		primaryOffer.setType(polyglotService.getTranslatedData(SUPPLIER));
		primaryOffer.setIconUrl(buildIconUrlForPrimaryOffer(deal));
	}

	/**
	 * Builds Icon url on the basis of deal either EARLY_BIRD OR LAST_MINUTE
	 *
	 * @param deal
	 * @return String
	 */
	private String buildIconUrlForPrimaryOffer(String deal) {
		String iconUrl=null;
		if (deal.equalsIgnoreCase(EARLY_BIRD)) {
			iconUrl= earlyBirdIconUrl;
		} else {
			iconUrl= lastMinuteIconUrl;
		}
		return iconUrl;
	}

	private void updateRoomPersuasion(JsonNode roomPersuasions) {
		if(roomPersuasions==null){
			return;
		}
		try{
			roomPersuasions.fields().forEachRemaining(rp -> {
				if(rp.getKey().equalsIgnoreCase("PLACEHOLDER_SELECT_M1")){
					JsonNode roomPersuasion = rp.getValue();
					roomPersuasion.fields().forEachRemaining(entry -> {
						if(entry.getKey().equalsIgnoreCase("data")){
							for (JsonNode data : entry.getValue()) {

								//Updated iconType in data node
								if(data.isObject() && data.has("icontype")){
									ObjectNode objectNode = (ObjectNode) data;
									JsonNode jsonNode = objectNode.get("icontype");
									if(jsonNode.isTextual() && jsonNode.textValue().equals("lightning_icon")){
										objectNode.put("icontype","lightning_icon_v2");
									}
									data = (JsonNode) objectNode;
								}
								data.fields().forEachRemaining(props -> {
									//Updated Style
									if(props.getKey().equalsIgnoreCase("style")){
										JsonNode style = props.getValue();
										//Removed fontType from style node
										if(style.isObject() && style.has("fontType")){
											ObjectNode objectNode = (ObjectNode) style;
											objectNode.remove("fontType");
											style = (JsonNode) objectNode;
										}

										//Removed bgColor from style node
										if(style.isObject() && style.has("bgColor")){
											ObjectNode objectNode = (ObjectNode) style;
											objectNode.remove("bgColor");
											style = (JsonNode) objectNode;
										}

										//Updated textColor in style node
										if(style.isObject() && style.has("textColor")){
											ObjectNode objectNode = (ObjectNode) style;
											objectNode.put("textColor", "#CF8100");
											style = (JsonNode) objectNode;
										}
									}
								});
							}
						}
					});
				}
			});
		}catch (Exception e){
			logger.error("Exception occurred in updating roomPersuasions: {}", e.getMessage(), e);
		}
	}

	private boolean isSearchDateDaysIsSameAsAlternateDateDays(com.mmt.hotels.model.response.pricing.AlternatePriceCard priceCard, SearchRoomsCriteria searchRoomsCriteria) {
        String checkInAlternateDay = dateUtil.dayOfWeek(priceCard.getCheckIn());
        String checkOutAlternateDay = dateUtil.dayOfWeek(priceCard.getCheckOut());

        String checkInSearchedDay = dateUtil.dayOfWeek(searchRoomsCriteria.getCheckIn());
        String checkOutSearchedDay = dateUtil.dayOfWeek(searchRoomsCriteria.getCheckOut());

        return dateUtil.isSameDay(checkInAlternateDay, checkInSearchedDay) && dateUtil.isSameDay(checkOutAlternateDay, checkOutSearchedDay);
    }

	private boolean isWeekendRateAvailable(com.mmt.hotels.model.response.pricing.AlternatePriceCard priceCard, SearchRoomsCriteria searchRoomsCriteria, int los) {
        LocalDate checkInDate = dateUtil.getLocalDate(priceCard.getCheckIn(), DateUtil.YYYY_MM_DD);
        LocalDate checkOutDate = dateUtil.getLocalDate(priceCard.getCheckOut(), DateUtil.YYYY_MM_DD);
        LocalDate searchCheckInDate = dateUtil.getLocalDate(searchRoomsCriteria.getCheckIn(), DateUtil.YYYY_MM_DD);

        return los == 1 && checkInDate != null && searchCheckInDate != null && checkOutDate != null
                && checkInDate.isAfter(searchCheckInDate) && checkInDate.getDayOfWeek() == DayOfWeek.SATURDAY && checkOutDate.getDayOfWeek() == DayOfWeek.SUNDAY;
    }

	private boolean isNextDayRateAvailable(com.mmt.hotels.model.response.pricing.AlternatePriceCard priceCard, SearchRoomsCriteria searchRoomsCriteria, int los) {
		LocalDate checkInSearchedDate = dateUtil.getLocalDate(searchRoomsCriteria.getCheckIn(), DateUtil.YYYY_MM_DD);
		LocalDate checkInAlternateDate = dateUtil.getLocalDate(priceCard.getCheckIn(), DateUtil.YYYY_MM_DD);
		LocalDate added = checkInSearchedDate.plusDays(1);
		return los == 1 && checkInAlternateDate.equals(added);
    }

	public void buildAlternatePriceCard(RoomDetailsResponse roomDetailsResponse, SearchRoomsResponse searchRoomsResponse, SearchRoomsRequest searchRoomsRequest) {
		AlternatePriceCard alternatePriceCard = new AlternatePriceCard();
		try {
			SearchRoomsCriteria searchRoomsCriteria = searchRoomsRequest.getSearchCriteria();
			alternatePriceCard.setHeading(commonConfigConsul.getPriceWidgetHeadline());
			alternatePriceCard.setSubheading(commonConfigConsul.getPriceWidgetSubHeadline());
			alternatePriceCard.setNewFeatureTag(commonConfigConsul.getPriceWidgetNewFeatureTag());
			List<PriceCardDetail> priceCardDetails = new ArrayList<>();
			alternatePriceCard.setData(priceCardDetails);
			com.mmt.hotels.model.response.pricing.AlternatePriceCard priceCardSelected = null;
			if(null != roomDetailsResponse && CollectionUtils.isNotEmpty(roomDetailsResponse.getAlternatePriceCard())) {
				for (com.mmt.hotels.model.response.pricing.AlternatePriceCard priceCard : roomDetailsResponse.getAlternatePriceCard()) {
					if (priceCard.isSelected()) {
						priceCardSelected = priceCard;
						break;
					}
				}
			}
			if(null != roomDetailsResponse && CollectionUtils.isNotEmpty(roomDetailsResponse.getAlternatePriceCard())) {
				for (com.mmt.hotels.model.response.pricing.AlternatePriceCard priceCard : roomDetailsResponse.getAlternatePriceCard()) {
					PriceCardDetail priceCardDetail = new PriceCardDetail();
					priceCardDetail.setText(dateUtil.concatDate(priceCard.getCheckIn(), priceCard.getCheckOut()));
					priceCardDetail.setPrice(priceCard.getPrice());
					priceCardDetail.setCheaper(priceCard.isCheaper());
					priceCardDetail.setSelected(priceCard.isSelected());
					priceCardDetail.setCurrency(priceCard.getCurrency());
					boolean isWeekend = isWeekendRateAvailable(priceCard, searchRoomsCriteria, dateUtil.getDaysDiff(searchRoomsRequest.getSearchCriteria().getCheckIn(), searchRoomsRequest.getSearchCriteria().getCheckOut()));
					boolean isNextDay = isNextDayRateAvailable(priceCard, searchRoomsCriteria, dateUtil.getDaysDiff(searchRoomsRequest.getSearchCriteria().getCheckIn(), searchRoomsRequest.getSearchCriteria().getCheckOut()));
					boolean isSameDayOfWeek = isSearchDateDaysIsSameAsAlternateDateDays(priceCard, searchRoomsCriteria);
					priceCardDetail.setComingWeekend(isWeekend);
					priceCardDetail.setNextDay(isNextDay);
					if (priceCard.isSelected()) {
						priceCardDetail.setSameDayOfWeek(false);
					} else {
						priceCardDetail.setSameDayOfWeek(isSameDayOfWeek);
					}

					DateRange dateRange = new DateRange();
					dateRange.setCheckIn(priceCard.getCheckIn());
					dateRange.setCheckOut(priceCard.getCheckOut());
					priceCardDetail.setDateRange(dateRange);

					SubTextData subTextData = new SubTextData();
					double delta = priceCardSelected != null ? priceCard.getDelta() / priceCardSelected.getPrice() : 1.0;
					if (priceCard.getDelta() == 0) {
						priceCardDetail.setSamePrice(true);
					} else {
						priceCardDetail.setSamePrice(false);
					}
					if (priceCard.getDelta() > 0 && delta > 0.03) {
						subTextData.setAmount(priceCard.getDelta());
						subTextData.setText(priceCard.isCheaper() ? CHEAPER_BY : EXPENSIVE_BY);
					}
					if (priceCard.isSelected()) {
						subTextData.setText(priceCard.isCheaper() ? CHEAPEST_PRICE : SELECTED_PRICE);
						priceCardDetail.setHoverText(commonConfigConsul.getPriceWidgetHoverHtmlForSelected());
					} else {
						priceCardDetail.setHoverText(commonConfigConsul.getPriceWidgetHoverHtml());
					}
					priceCardDetail.setSubTextData(subTextData);
					priceCardDetails.add(priceCardDetail);
				}
			} else {
				logger.warn("No alternate price card details found for {}", null != roomDetailsResponse);
			}
			searchRoomsResponse.setAlternatePriceCard(alternatePriceCard);
		} catch (Exception exp) {
			logger.error("error while building alternate price card", exp);
		}
	}

}

